//
//  MacWindowManager.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 12/24/24.
//

#if targetEnvironment(macCatalyst)
import Foundation
import SwiftUI
import AppKit

@MainActor
class MacWindowManager: ObservableObject {
    static let shared = MacWindowManager()
    
    @Published var windows: [MacWindow] = []
    @Published var activeWindow: MacWindow?
    
    private init() {
        setupWindowManagement()
    }
    
    // MARK: - Window Management
    
    func setupWindowManagement() {
        // Configure main window
        if let scene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let titlebar = scene.titlebar {
            
            // Configure titlebar
            titlebar.titleVisibility = .visible
            titlebar.toolbar = createMainToolbar()
            
            // Set window properties
            if let window = scene.windows.first {
                window.windowScene?.sizeRestrictions?.minimumSize = CGSize(width: 800, height: 600)
                window.windowScene?.sizeRestrictions?.maximumSize = CGSize(width: 1400, height: 1000)
            }
        }
    }
    
    private func createMainToolbar() -> NSToolbar {
        let toolbar = NSToolbar(identifier: "PetCapsuleToolbar")
        toolbar.delegate = ToolbarDelegate.shared
        toolbar.allowsUserCustomization = true
        toolbar.autosavesConfiguration = true
        toolbar.displayMode = .iconAndLabel
        
        return toolbar
    }
    
    // MARK: - Window Actions
    
    func openNewWindow(type: WindowType) {
        let window = MacWindow(
            id: UUID(),
            type: type,
            title: type.title,
            isVisible: true
        )
        
        windows.append(window)
        activeWindow = window
        
        // Create new window scene
        createWindowScene(for: window)
    }
    
    func closeWindow(_ window: MacWindow) {
        windows.removeAll { $0.id == window.id }
        
        if activeWindow?.id == window.id {
            activeWindow = windows.first
        }
    }
    
    func focusWindow(_ window: MacWindow) {
        activeWindow = window
        
        // Bring window to front
        if let windowScene = findWindowScene(for: window) {
            windowScene.windows.first?.makeKeyAndVisible()
        }
    }
    
    private func createWindowScene(for window: MacWindow) {
        let userActivity = NSUserActivity(activityType: "com.petcapsule.window")
        userActivity.userInfo = ["windowType": window.type.rawValue]
        
        let options = UIScene.ActivationRequestOptions()
        options.requestingScene = UIApplication.shared.connectedScenes.first
        
        UIApplication.shared.requestSceneSessionActivation(
            nil,
            userActivity: userActivity,
            options: options
        ) { error in
            if let error = error {
                print("Failed to create window scene: \(error)")
            }
        }
    }
    
    private func findWindowScene(for window: MacWindow) -> UIWindowScene? {
        return UIApplication.shared.connectedScenes
            .compactMap { $0 as? UIWindowScene }
            .first { scene in
                scene.session.userInfo?["windowType"] as? String == window.type.rawValue
            }
    }
    
    // MARK: - Toolbar Actions
    
    @objc func addPetAction() {
        NotificationCenter.default.post(name: .navigateToDestination, object: AppDestination.addPet)
    }
    
    @objc func addMemoryAction() {
        NotificationCenter.default.post(name: .navigateToDestination, object: AppDestination.addMemory)
    }
    
    @objc func healthViewAction() {
        NotificationCenter.default.post(name: .navigateToDestination, object: AppDestination.health)
    }
    
    @objc func aiChatAction() {
        NotificationCenter.default.post(name: .navigateToDestination, object: AppDestination.aiChat)
    }
    
    @objc func emergencyAction() {
        EmergencyCallService.shared.callEmergencyVet(for: nil)
    }
    
    @objc func settingsAction() {
        NotificationCenter.default.post(name: .navigateToDestination, object: AppDestination.settings)
    }
}

// MARK: - Toolbar Delegate

class ToolbarDelegate: NSObject, NSToolbarDelegate {
    static let shared = ToolbarDelegate()
    
    private let toolbarItems: [ToolbarItem] = [
        ToolbarItem(
            identifier: "addPet",
            label: "Add Pet",
            image: "pawprint.fill",
            action: #selector(MacWindowManager.shared.addPetAction)
        ),
        ToolbarItem(
            identifier: "addMemory",
            label: "Add Memory",
            image: "camera.fill",
            action: #selector(MacWindowManager.shared.addMemoryAction)
        ),
        ToolbarItem(
            identifier: "health",
            label: "Health",
            image: "heart.fill",
            action: #selector(MacWindowManager.shared.healthViewAction)
        ),
        ToolbarItem(
            identifier: "aiChat",
            label: "AI Chat",
            image: "brain.head.profile",
            action: #selector(MacWindowManager.shared.aiChatAction)
        ),
        ToolbarItem(
            identifier: "emergency",
            label: "Emergency",
            image: "phone.fill",
            action: #selector(MacWindowManager.shared.emergencyAction)
        ),
        ToolbarItem(
            identifier: "settings",
            label: "Settings",
            image: "gear",
            action: #selector(MacWindowManager.shared.settingsAction)
        )
    ]
    
    func toolbar(_ toolbar: NSToolbar, itemForItemIdentifier itemIdentifier: NSToolbarItem.Identifier, willBeInsertedIntoToolbar flag: Bool) -> NSToolbarItem? {
        
        guard let toolbarItem = toolbarItems.first(where: { $0.identifier == itemIdentifier.rawValue }) else {
            return nil
        }
        
        let item = NSToolbarItem(itemIdentifier: itemIdentifier)
        item.label = toolbarItem.label
        item.toolTip = toolbarItem.label
        item.image = NSImage(systemSymbolName: toolbarItem.image, accessibilityDescription: toolbarItem.label)
        item.target = MacWindowManager.shared
        item.action = toolbarItem.action
        
        return item
    }
    
    func toolbarDefaultItemIdentifiers(_ toolbar: NSToolbar) -> [NSToolbarItem.Identifier] {
        return [
            NSToolbarItem.Identifier("addPet"),
            NSToolbarItem.Identifier("addMemory"),
            NSToolbarItem.Identifier.flexibleSpace,
            NSToolbarItem.Identifier("health"),
            NSToolbarItem.Identifier("aiChat"),
            NSToolbarItem.Identifier.flexibleSpace,
            NSToolbarItem.Identifier("emergency"),
            NSToolbarItem.Identifier("settings")
        ]
    }
    
    func toolbarAllowedItemIdentifiers(_ toolbar: NSToolbar) -> [NSToolbarItem.Identifier] {
        return toolbarDefaultItemIdentifiers(toolbar) + [
            NSToolbarItem.Identifier.space,
            NSToolbarItem.Identifier.flexibleSpace
        ]
    }
}

// MARK: - Mac-Specific Views

struct MacSidebarView: View {
    @StateObject private var petManager = PetManager.shared
    @State private var selectedSection: SidebarSection = .dashboard
    
    var body: some View {
        List(selection: $selectedSection) {
            Section("Overview") {
                SidebarRow(
                    section: .dashboard,
                    icon: "house.fill",
                    title: "Dashboard"
                )
                
                SidebarRow(
                    section: .health,
                    icon: "heart.fill",
                    title: "Health"
                )
                
                SidebarRow(
                    section: .memories,
                    icon: "photo.fill",
                    title: "Memories"
                )
            }
            
            Section("AI Assistants") {
                SidebarRow(
                    section: .aiChat,
                    icon: "brain.head.profile",
                    title: "AI Chat"
                )
                
                SidebarRow(
                    section: .nutrition,
                    icon: "leaf.fill",
                    title: "Dr. Nutrition"
                )
                
                SidebarRow(
                    section: .training,
                    icon: "figure.walk",
                    title: "Trainer Pro"
                )
            }
            
            Section("Tools") {
                SidebarRow(
                    section: .planner,
                    icon: "calendar",
                    title: "Planner"
                )
                
                SidebarRow(
                    section: .emergency,
                    icon: "phone.fill",
                    title: "Emergency"
                )
                
                SidebarRow(
                    section: .settings,
                    icon: "gear",
                    title: "Settings"
                )
            }
            
            if !petManager.pets.isEmpty {
                Section("Pets") {
                    ForEach(petManager.pets) { pet in
                        PetSidebarRow(pet: pet)
                    }
                }
            }
        }
        .listStyle(.sidebar)
        .frame(minWidth: 200)
    }
}

struct SidebarRow: View {
    let section: SidebarSection
    let icon: String
    let title: String
    
    var body: some View {
        Label(title, systemImage: icon)
            .tag(section)
    }
}

struct PetSidebarRow: View {
    let pet: Pet
    
    var body: some View {
        HStack {
            Circle()
                .fill(Color.blue)
                .frame(width: 8, height: 8)
            
            Text(pet.name)
                .font(.subheadline)
            
            Spacer()
            
            Text("\(pet.age)y")
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }
}

struct MacDetailView: View {
    let section: SidebarSection
    
    var body: some View {
        Group {
            switch section {
            case .dashboard:
                MacDashboardView()
            case .health:
                PetHealthChartsView(pet: Pet.samplePet)
            case .memories:
                MemoriesView()
            case .aiChat:
                AIAgentsView()
            case .nutrition:
                NutritionPlannerView()
            case .training:
                TrainingPlannerView()
            case .planner:
                PlannerView()
            case .emergency:
                EmergencyContactsView()
            case .settings:
                SettingsView()
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
}

struct MacDashboardView: View {
    @StateObject private var petManager = PetManager.shared
    
    var body: some View {
        ScrollView {
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 20) {
                // Pet Overview Cards
                ForEach(petManager.pets.prefix(3)) { pet in
                    PetOverviewCard(pet: pet)
                }
                
                // Quick Stats
                QuickStatsCard()
                
                // Recent Activity
                RecentActivityCard()
                
                // Health Alerts
                HealthAlertsCard()
            }
            .padding()
        }
        .navigationTitle("Dashboard")
    }
}

// MARK: - Supporting Types

struct MacWindow: Identifiable {
    let id: UUID
    let type: WindowType
    let title: String
    let isVisible: Bool
}

enum WindowType: String, CaseIterable {
    case main = "main"
    case health = "health"
    case memories = "memories"
    case aiChat = "aiChat"
    case settings = "settings"
    
    var title: String {
        switch self {
        case .main: return "PetCapsule"
        case .health: return "Pet Health"
        case .memories: return "Memories"
        case .aiChat: return "AI Assistants"
        case .settings: return "Settings"
        }
    }
}

enum SidebarSection: String, CaseIterable {
    case dashboard = "dashboard"
    case health = "health"
    case memories = "memories"
    case aiChat = "aiChat"
    case nutrition = "nutrition"
    case training = "training"
    case planner = "planner"
    case emergency = "emergency"
    case settings = "settings"
}

struct ToolbarItem {
    let identifier: String
    let label: String
    let image: String
    let action: Selector
}

#endif
