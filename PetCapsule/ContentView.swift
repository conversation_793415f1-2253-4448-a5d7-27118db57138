//
//  ContentView.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI

struct ContentView: View {
    @StateObject private var authService = AuthenticationService()
    @StateObject private var dataService = RealDataService()
    @StateObject private var supabaseService = SupabaseService.shared
    @StateObject private var subscriptionService = SubscriptionService.shared
    @StateObject private var petAISupportService = PetAISupportService.shared
    @StateObject private var themeManager = ThemeManager.shared

    // iOS 18 Services (with availability checks)
    @available(iOS 18.0, *)
    private var iconManager: AppIconManager? {
        return AppIconManager.shared
    }

    // Optional iOS 18 services
    @available(iOS 18.0, *)
    private var appleIntelligenceService: AppleIntelligenceService? {
        return AppleIntelligenceService.shared
    }

    @available(iOS 18.0, *)
    private var passkeyService: PasskeyAuthenticationService? {
        return PasskeyAuthenticationService.shared
    }

    @available(iOS 18.0, *)
    private var mlService: EnhancedMLService? {
        return EnhancedMLService.shared
    }

    @available(iOS 18.0, *)
    private var activityManager: PetLiveActivityManager? {
        return PetLiveActivityManager.shared
    }

    var body: some View {
        Group {
            if authService.isAuthenticated {
                MainAppView()
                    .environmentObject(dataService)
                    .environmentObject(authService)
                    .environmentObject(supabaseService)
                    .environmentObject(subscriptionService)
                    .environmentObject(petAISupportService)
                    .environmentObject(themeManager)
                    .environmentObject(AIConversationService.shared)

                    .modifier(iOS18EnvironmentModifier(
                        appleIntelligenceService: {
                            if #available(iOS 18.0, *) { return appleIntelligenceService } else { return nil }
                        }(),
                        passkeyService: {
                            if #available(iOS 18.0, *) { return passkeyService } else { return nil }
                        }(),
                        mlService: {
                            if #available(iOS 18.0, *) { return mlService } else { return nil }
                        }(),
                        activityManager: {
                            if #available(iOS 18.0, *) { return activityManager } else { return nil }
                        }(),
                        iconManager: {
                            if #available(iOS 18.0, *) { return iconManager } else { return nil }
                        }()
                    ))
                    .preferredColorScheme(themeManager.currentTheme.colorScheme)
            } else {
                AuthenticationView()
                    .environmentObject(authService)
                    .environmentObject(subscriptionService)
                    .environmentObject(themeManager)
                    .preferredColorScheme(themeManager.currentTheme.colorScheme)
            }
        }
        .onAppear {
            Task {
                // Only check authentication status if not already authenticated
                if !authService.isAuthenticated {
                    await authService.checkAuthenticationStatus()
                }
            }
        }
    }
}

// MARK: - iOS 18 Environment Modifier

struct iOS18EnvironmentModifier: ViewModifier {
    let appleIntelligenceService: Any?
    let passkeyService: Any?
    let mlService: Any?
    let activityManager: Any?
    let iconManager: Any?

    func body(content: Content) -> some View {
        Group {
            if #available(iOS 18.0, *) {
                content
                    .environmentObject((appleIntelligenceService as? AppleIntelligenceService) ?? AppleIntelligenceService.shared)
                    .environmentObject((passkeyService as? PasskeyAuthenticationService) ?? PasskeyAuthenticationService.shared)
                    .environmentObject((mlService as? EnhancedMLService) ?? EnhancedMLService.shared)
                    .environmentObject((activityManager as? PetLiveActivityManager) ?? PetLiveActivityManager.shared)
                    .environmentObject((iconManager as? AppIconManager) ?? AppIconManager.shared)
            } else {
                content
            }
        }
    }
}

#Preview {
    ContentView()
}
