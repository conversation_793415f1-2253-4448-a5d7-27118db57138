//
//  PetCapsuleWidgets.swift
//  PetCapsule
//
//  Enhanced iOS 18 Widgets with Interactive Elements and Smart Stack Support
//

import SwiftUI
import WidgetKit
import AppIntents

// MARK: - Widget Bundle

@available(iOS 18.0, *)
// @main - Commented out to avoid duplicate @main attribute
struct PetCapsuleWidgetBundle: WidgetBundle {
    var body: some Widget {
        PetDashboardWidget()
        VaccinationTrackerWidget()
        MemoryTimelineWidget()
        WalkPlannerWidget()
        PetHealthWidget()
        EmergencyContactWidget()
    }
}

// MARK: - Pet Dashboard Widget

@available(iOS 18.0, *)
struct PetDashboardWidget: Widget {
    let kind: String = "PetDashboardWidget"
    
    var body: some WidgetConfiguration {
        AppIntentConfiguration(
            kind: kind,
            intent: PetDashboardConfigurationIntent.self,
            provider: PetDashboardProvider()
        ) { entry in
            PetDashboardWidgetView(entry: entry)
                .containerBackground(.fill.tertiary, for: .widget)
        }
        .configurationDisplayName("Pet Dashboard")
        .description("Overview of your pet's health, activities, and upcoming care")
        .supportedFamilies([.systemSmall, .systemMedium, .systemLarge])
        .contentMarginsDisabled()
    }
}

@available(iOS 18.0, *)
struct PetDashboardWidgetView: View {
    let entry: PetDashboardEntry
    
    var body: some View {
        ZStack {
            // Glass morphism background
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(.white.opacity(0.2), lineWidth: 1)
                )
            
            VStack(spacing: 12) {
                // Pet Info Header
                HStack {
                    AsyncImage(url: entry.pet.imageURL) { image in
                        image
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                    } placeholder: {
                        Image(systemName: "pawprint.circle.fill")
                            .foregroundStyle(.blue)
                    }
                    .frame(width: 40, height: 40)
                    .clipShape(Circle())
                    
                    VStack(alignment: .leading, spacing: 2) {
                        Text(entry.pet.name)
                            .font(.headline)
                            .fontWeight(.bold)
                        
                        Text(entry.pet.type)
                            .font(.caption)
                            .foregroundStyle(.secondary)
                    }
                    
                    Spacer()
                    
                    // Health Status Indicator
                    Circle()
                        .fill(entry.healthStatus.color)
                        .frame(width: 12, height: 12)
                }
                
                // Quick Stats
                HStack(spacing: 16) {
                    StatView(
                        icon: "syringe.fill",
                        value: "\(entry.upcomingVaccinations)",
                        label: "Due",
                        color: .orange
                    )
                    
                    StatView(
                        icon: "camera.fill",
                        value: "\(entry.recentMemories)",
                        label: "Memories",
                        color: .blue
                    )
                    
                    StatView(
                        icon: "figure.walk",
                        value: entry.walkStatus,
                        label: "Walk",
                        color: .green
                    )
                }
                
                // Interactive Buttons
                HStack(spacing: 8) {
                    Button(intent: QuickAddMemoryIntent()) {
                        Label("Memory", systemImage: "camera.fill")
                            .font(.caption)
                            .padding(.horizontal, 12)
                            .padding(.vertical, 6)
                            .background(.blue.opacity(0.2))
                            .foregroundStyle(.blue)
                            .clipShape(Capsule())
                    }
                    .buttonStyle(.plain)
                    
                    Button(intent: EmergencyVetContactIntent()) {
                        Label("Emergency", systemImage: "phone.fill")
                            .font(.caption)
                            .padding(.horizontal, 12)
                            .padding(.vertical, 6)
                            .background(.red.opacity(0.2))
                            .foregroundStyle(.red)
                            .clipShape(Capsule())
                    }
                    .buttonStyle(.plain)
                }
            }
            .padding(16)
        }
    }
}

@available(iOS 18.0, *)
struct StatView: View {
    let icon: String
    let value: String
    let label: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 4) {
            Image(systemName: icon)
                .foregroundStyle(color)
                .font(.title3)
            
            Text(value)
                .font(.headline)
                .fontWeight(.bold)
            
            Text(label)
                .font(.caption2)
                .foregroundStyle(.secondary)
        }
    }
}

// MARK: - Vaccination Tracker Widget

@available(iOS 18.0, *)
struct VaccinationTrackerWidget: Widget {
    let kind: String = "VaccinationTrackerWidget"
    
    var body: some WidgetConfiguration {
        AppIntentConfiguration(
            kind: kind,
            intent: VaccinationTrackerConfigurationIntent.self,
            provider: VaccinationTrackerProvider()
        ) { entry in
            VaccinationTrackerWidgetView(entry: entry)
                .containerBackground(.fill.tertiary, for: .widget)
        }
        .configurationDisplayName("Vaccination Tracker")
        .description("Track upcoming vaccinations and health appointments")
        .supportedFamilies([.systemMedium, .systemLarge])
    }
}

@available(iOS 18.0, *)
struct VaccinationTrackerWidgetView: View {
    let entry: VaccinationTrackerEntry
    
    var body: some View {
        ZStack {
            // Gradient background
            LinearGradient(
                colors: [.blue.opacity(0.3), .purple.opacity(0.3)],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            
            VStack(alignment: .leading, spacing: 12) {
                // Header
                HStack {
                    Image(systemName: "syringe.fill")
                        .foregroundStyle(.blue)
                        .font(.title2)
                    
                    Text("Vaccination Tracker")
                        .font(.headline)
                        .fontWeight(.bold)
                    
                    Spacer()
                    
                    if entry.hasUrgent {
                        Image(systemName: "exclamationmark.triangle.fill")
                            .foregroundStyle(.orange)
                            .font(.title3)
                    }
                }
                
                // Vaccination List
                ForEach(entry.upcomingVaccinations.prefix(3), id: \.id) { vaccination in
                    HStack {
                        VStack(alignment: .leading, spacing: 2) {
                            Text(vaccination.vaccineName)
                                .font(.subheadline)
                                .fontWeight(.medium)
                            
                            Text(vaccination.dueDate.formatted(date: .abbreviated, time: .omitted))
                                .font(.caption)
                                .foregroundStyle(.secondary)
                        }
                        
                        Spacer()
                        
                        // Urgency indicator
                        let daysUntilDue = Calendar.current.dateComponents([.day], from: Date(), to: vaccination.dueDate).day ?? 0
                        
                        Text("\(daysUntilDue) days")
                            .font(.caption)
                            .fontWeight(.semibold)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(daysUntilDue <= 7 ? .orange.opacity(0.3) : .green.opacity(0.3))
                            .foregroundStyle(daysUntilDue <= 7 ? .orange : .green)
                            .clipShape(Capsule())
                    }
                    .padding(.vertical, 4)
                }
                
                // Action Button
                Button(intent: CheckVaccinationStatusIntent()) {
                    HStack {
                        Text("View All Vaccinations")
                            .font(.caption)
                            .fontWeight(.medium)
                        
                        Image(systemName: "arrow.right")
                            .font(.caption2)
                    }
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(.blue.opacity(0.2))
                    .foregroundStyle(.blue)
                    .clipShape(Capsule())
                }
                .buttonStyle(.plain)
            }
            .padding(16)
        }
    }
}

// MARK: - Memory Timeline Widget

@available(iOS 18.0, *)
struct MemoryTimelineWidget: Widget {
    let kind: String = "MemoryTimelineWidget"
    
    var body: some WidgetConfiguration {
        AppIntentConfiguration(
            kind: kind,
            intent: MemoryTimelineConfigurationIntent.self,
            provider: MemoryTimelineProvider()
        ) { entry in
            MemoryTimelineWidgetView(entry: entry)
                .containerBackground(.fill.tertiary, for: .widget)
        }
        .configurationDisplayName("Memory Timeline")
        .description("Recent pet memories and moments")
        .supportedFamilies([.systemMedium, .systemLarge])
    }
}

@available(iOS 18.0, *)
struct MemoryTimelineWidgetView: View {
    let entry: MemoryTimelineEntry
    
    var body: some View {
        ZStack {
            // Glass morphism background
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(.white.opacity(0.2), lineWidth: 1)
                )
            
            VStack(alignment: .leading, spacing: 12) {
                // Header
                HStack {
                    Image(systemName: "camera.fill")
                        .foregroundStyle(.pink)
                        .font(.title2)
                    
                    Text("Recent Memories")
                        .font(.headline)
                        .fontWeight(.bold)
                    
                    Spacer()
                    
                    Text("\(entry.totalMemories)")
                        .font(.caption)
                        .fontWeight(.semibold)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(.pink.opacity(0.2))
                        .foregroundStyle(.pink)
                        .clipShape(Capsule())
                }
                
                // Memory Grid
                LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 8), count: 3), spacing: 8) {
                    ForEach(entry.recentMemories.prefix(6), id: \.id) { memory in
                        Button(intent: {
                            var intent = ViewMemoryIntent()
                            intent.memoryId = memory.id.uuidString
                            return intent
                        }()) {
                            AsyncImage(url: memory.imageURL) { image in
                                image
                                    .resizable()
                                    .aspectRatio(contentMode: .fill)
                            } placeholder: {
                                RoundedRectangle(cornerRadius: 8)
                                    .fill(.gray.opacity(0.3))
                                    .overlay(
                                        Image(systemName: "photo")
                                            .foregroundStyle(.gray)
                                    )
                            }
                            .frame(height: 60)
                            .clipShape(RoundedRectangle(cornerRadius: 8))
                        }
                        .buttonStyle(.plain)
                    }
                }
                
                // Add Memory Button
                Button(intent: QuickAddMemoryIntent()) {
                    HStack {
                        Image(systemName: "plus.circle.fill")
                        Text("Add Memory")
                            .fontWeight(.medium)
                    }
                    .font(.caption)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 8)
                    .background(.blue.opacity(0.2))
                    .foregroundStyle(.blue)
                    .clipShape(Capsule())
                }
                .buttonStyle(.plain)
            }
            .padding(16)
        }
    }
}

// MARK: - Walk Planner Widget

@available(iOS 18.0, *)
struct WalkPlannerWidget: Widget {
    let kind: String = "WalkPlannerWidget"
    
    var body: some WidgetConfiguration {
        StaticConfiguration(
            kind: kind,
            provider: WalkPlannerProvider()
        ) { entry in
            WalkPlannerWidgetView(entry: entry)
                .containerBackground(.fill.tertiary, for: .widget)
        }
        .configurationDisplayName("Walk Planner")
        .description("Weather conditions and walk recommendations")
        .supportedFamilies([.systemSmall, .systemMedium])
    }
}

@available(iOS 18.0, *)
struct WalkPlannerWidgetView: View {
    let entry: WalkPlannerEntry
    
    var body: some View {
        ZStack {
            // Weather-based gradient
            LinearGradient(
                colors: entry.isGoodWeather ? 
                    [.green.opacity(0.3), .blue.opacity(0.3)] : 
                    [.orange.opacity(0.3), .red.opacity(0.3)],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            
            VStack(spacing: 12) {
                // Weather Info
                HStack {
                    Image(systemName: entry.weatherIcon)
                        .foregroundStyle(entry.isGoodWeather ? .green : .orange)
                        .font(.title)
                    
                    VStack(alignment: .leading) {
                        Text(entry.temperature)
                            .font(.title2)
                            .fontWeight(.bold)
                        
                        Text(entry.weatherCondition)
                            .font(.caption)
                            .foregroundStyle(.secondary)
                    }
                    
                    Spacer()
                }
                
                // Walk Recommendation
                VStack(alignment: .leading, spacing: 4) {
                    Text("Walk Recommendation")
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundStyle(.secondary)
                    
                    Text(entry.walkRecommendation)
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .lineLimit(2)
                }
                
                // Action Button
                Button(intent: PlanWalkIntent()) {
                    HStack {
                        Image(systemName: "figure.walk")
                        Text("Plan Walk")
                            .fontWeight(.medium)
                    }
                    .font(.caption)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 8)
                    .background(.blue.opacity(0.2))
                    .foregroundStyle(.blue)
                    .clipShape(Capsule())
                }
                .buttonStyle(.plain)
            }
            .padding(16)
        }
    }
}

// MARK: - Pet Health Widget

@available(iOS 18.0, *)
struct PetHealthWidget: Widget {
    let kind: String = "PetHealthWidget"

    var body: some WidgetConfiguration {
        StaticConfiguration(
            kind: kind,
            provider: PetHealthProvider()
        ) { entry in
            PetHealthWidgetView(entry: entry)
                .containerBackground(.fill.tertiary, for: .widget)
        }
        .configurationDisplayName("Pet Health")
        .description("Monitor your pet's health status and alerts")
        .supportedFamilies([.systemMedium])
    }
}

@available(iOS 18.0, *)
struct PetHealthWidgetView: View {
    let entry: PetHealthEntry

    var body: some View {
        VStack(spacing: 12) {
            HStack {
                Image(systemName: "heart.fill")
                    .foregroundStyle(.red)
                    .font(.title2)

                Text("Health Status")
                    .font(.headline)
                    .fontWeight(.bold)

                Spacer()
            }

            HStack {
                VStack(alignment: .leading) {
                    Text("Overall Health")
                        .font(.caption)
                        .foregroundStyle(.secondary)

                    Text(entry.healthStatus.title)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundStyle(entry.healthStatus.color)
                }

                Spacer()

                Circle()
                    .fill(entry.healthStatus.color)
                    .frame(width: 20, height: 20)
            }

            if let alert = entry.healthAlert {
                HStack {
                    Image(systemName: "exclamationmark.triangle.fill")
                        .foregroundStyle(.orange)
                        .font(.caption)

                    Text(alert)
                        .font(.caption)
                        .lineLimit(2)

                    Spacer()
                }
                .padding(8)
                .background(.orange.opacity(0.1))
                .clipShape(RoundedRectangle(cornerRadius: 8))
            }
        }
        .padding(16)
    }
}

// MARK: - Emergency Contact Widget

@available(iOS 18.0, *)
struct EmergencyContactWidget: Widget {
    let kind: String = "EmergencyContactWidget"

    var body: some WidgetConfiguration {
        StaticConfiguration(
            kind: kind,
            provider: EmergencyContactProvider()
        ) { entry in
            EmergencyContactWidgetView(entry: entry)
                .containerBackground(.fill.tertiary, for: .widget)
        }
        .configurationDisplayName("Emergency Contact")
        .description("Quick access to emergency veterinary contacts")
        .supportedFamilies([.systemSmall])
    }
}

@available(iOS 18.0, *)
struct EmergencyContactWidgetView: View {
    let entry: EmergencyContactEntry

    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: "phone.fill")
                .font(.largeTitle)
                .foregroundStyle(.red)

            Text("Emergency")
                .font(.headline)
                .fontWeight(.bold)

            Text("Tap to Call")
                .font(.caption)
                .foregroundStyle(.secondary)

            Button(intent: EmergencyVetContactIntent()) {
                Text("Call Now")
                    .font(.caption)
                    .fontWeight(.semibold)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(.red)
                    .foregroundStyle(.white)
                    .clipShape(Capsule())
            }
            .buttonStyle(.plain)
        }
        .padding(16)
    }
}

// MARK: - Widget Providers

@available(iOS 18.0, *)
struct PetDashboardProvider: AppIntentTimelineProvider {
    func placeholder(in context: Context) -> PetDashboardEntry {
        PetDashboardEntry(
            date: Date(),
            pet: WidgetPet(id: UUID(), name: "Buddy", type: "Dog", imageURL: nil),
            healthStatus: .good,
            upcomingVaccinations: 2,
            recentMemories: 5,
            walkStatus: "Today"
        )
    }

    func snapshot(for configuration: PetDashboardConfigurationIntent, in context: Context) async -> PetDashboardEntry {
        return placeholder(in: context)
    }

    func timeline(for configuration: PetDashboardConfigurationIntent, in context: Context) async -> Timeline<PetDashboardEntry> {
        let entry = placeholder(in: context)
        let timeline = Timeline(entries: [entry], policy: .atEnd)
        return timeline
    }
}

@available(iOS 18.0, *)
struct VaccinationTrackerProvider: AppIntentTimelineProvider {
    func placeholder(in context: Context) -> VaccinationTrackerEntry {
        VaccinationTrackerEntry(
            date: Date(),
            upcomingVaccinations: [
                VaccinationRecord(id: UUID(), petId: UUID(), vaccineName: "Bordetella", dueDate: Date(), isCompleted: false)
            ],
            hasUrgent: true
        )
    }

    func snapshot(for configuration: VaccinationTrackerConfigurationIntent, in context: Context) async -> VaccinationTrackerEntry {
        return placeholder(in: context)
    }

    func timeline(for configuration: VaccinationTrackerConfigurationIntent, in context: Context) async -> Timeline<VaccinationTrackerEntry> {
        let entry = placeholder(in: context)
        let timeline = Timeline(entries: [entry], policy: .atEnd)
        return timeline
    }
}

@available(iOS 18.0, *)
struct MemoryTimelineProvider: AppIntentTimelineProvider {
    func placeholder(in context: Context) -> MemoryTimelineEntry {
        MemoryTimelineEntry(
            date: Date(),
            recentMemories: [
                WidgetMemory(id: UUID(), title: "Park Day", imageURL: nil, date: Date())
            ],
            totalMemories: 10
        )
    }

    func snapshot(for configuration: MemoryTimelineConfigurationIntent, in context: Context) async -> MemoryTimelineEntry {
        return placeholder(in: context)
    }

    func timeline(for configuration: MemoryTimelineConfigurationIntent, in context: Context) async -> Timeline<MemoryTimelineEntry> {
        let entry = placeholder(in: context)
        let timeline = Timeline(entries: [entry], policy: .atEnd)
        return timeline
    }
}

@available(iOS 18.0, *)
struct WalkPlannerProvider: TimelineProvider {
    func placeholder(in context: Context) -> WalkPlannerEntry {
        WalkPlannerEntry(
            date: Date(),
            temperature: "72°F",
            weatherCondition: "Sunny",
            weatherIcon: "sun.max.fill",
            isGoodWeather: true,
            walkRecommendation: "Perfect weather for a walk!"
        )
    }

    func getSnapshot(in context: Context, completion: @escaping (WalkPlannerEntry) -> ()) {
        completion(placeholder(in: context))
    }

    func getTimeline(in context: Context, completion: @escaping (Timeline<WalkPlannerEntry>) -> ()) {
        let entry = placeholder(in: context)
        let timeline = Timeline(entries: [entry], policy: .atEnd)
        completion(timeline)
    }
}

@available(iOS 18.0, *)
struct PetHealthProvider: TimelineProvider {
    func placeholder(in context: Context) -> PetHealthEntry {
        PetHealthEntry(
            date: Date(),
            healthStatus: .good,
            healthAlert: "Vaccination due in 15 days"
        )
    }

    func getSnapshot(in context: Context, completion: @escaping (PetHealthEntry) -> ()) {
        completion(placeholder(in: context))
    }

    func getTimeline(in context: Context, completion: @escaping (Timeline<PetHealthEntry>) -> ()) {
        let entry = placeholder(in: context)
        let timeline = Timeline(entries: [entry], policy: .atEnd)
        completion(timeline)
    }
}

@available(iOS 18.0, *)
struct EmergencyContactProvider: TimelineProvider {
    func placeholder(in context: Context) -> EmergencyContactEntry {
        EmergencyContactEntry(date: Date())
    }

    func getSnapshot(in context: Context, completion: @escaping (EmergencyContactEntry) -> ()) {
        completion(placeholder(in: context))
    }

    func getTimeline(in context: Context, completion: @escaping (Timeline<EmergencyContactEntry>) -> ()) {
        let entry = placeholder(in: context)
        let timeline = Timeline(entries: [entry], policy: .atEnd)
        completion(timeline)
    }
}

// MARK: - Supporting Types and Providers

@available(iOS 18.0, *)
struct PetDashboardEntry: TimelineEntry {
    let date: Date
    let pet: WidgetPet
    let healthStatus: PetHealthStatus
    let upcomingVaccinations: Int
    let recentMemories: Int
    let walkStatus: String
}

@available(iOS 18.0, *)
struct VaccinationTrackerEntry: TimelineEntry {
    let date: Date
    let upcomingVaccinations: [VaccinationRecord]
    let hasUrgent: Bool
}

@available(iOS 18.0, *)
struct MemoryTimelineEntry: TimelineEntry {
    let date: Date
    let recentMemories: [WidgetMemory]
    let totalMemories: Int
}

@available(iOS 18.0, *)
struct WalkPlannerEntry: TimelineEntry {
    let date: Date
    let temperature: String
    let weatherCondition: String
    let weatherIcon: String
    let isGoodWeather: Bool
    let walkRecommendation: String
}

@available(iOS 18.0, *)
struct PetHealthEntry: TimelineEntry {
    let date: Date
    let healthStatus: PetHealthStatus
    let healthAlert: String?
}

@available(iOS 18.0, *)
struct EmergencyContactEntry: TimelineEntry {
    let date: Date
}

@available(iOS 18.0, *)
struct WidgetPet {
    let id: UUID
    let name: String
    let type: String
    let imageURL: URL?
}

@available(iOS 18.0, *)
struct WidgetMemory {
    let id: UUID
    let title: String
    let imageURL: URL?
    let date: Date
}

// MARK: - Configuration Intents

@available(iOS 18.0, *)
struct PetDashboardConfigurationIntent: WidgetConfigurationIntent {
    static var title: LocalizedStringResource = "Pet Dashboard Configuration"
    static var description = IntentDescription("Choose which pet to display in the dashboard")
    
    @Parameter(title: "Pet", description: "Select your pet", optionsProvider: PetOptionsProvider())
    var pet: PetEntity?
}

@available(iOS 18.0, *)
struct VaccinationTrackerConfigurationIntent: WidgetConfigurationIntent {
    static var title: LocalizedStringResource = "Vaccination Tracker Configuration"
    static var description = IntentDescription("Configure vaccination tracking settings")
    
    @Parameter(title: "Pet", description: "Select your pet", optionsProvider: PetOptionsProvider())
    var pet: PetEntity?
    
    @Parameter(title: "Show Urgent Only", description: "Only show urgent vaccinations")
    var showUrgentOnly: Bool?
}

@available(iOS 18.0, *)
struct MemoryTimelineConfigurationIntent: WidgetConfigurationIntent {
    static var title: LocalizedStringResource = "Memory Timeline Configuration"
    static var description = IntentDescription("Configure memory timeline display")
    
    @Parameter(title: "Pet", description: "Select your pet", optionsProvider: PetOptionsProvider())
    var pet: PetEntity?
    
    @Parameter(title: "Show Favorites Only", description: "Only show favorite memories")
    var showFavoritesOnly: Bool?
}

// MARK: - Widget-Specific App Intents

@available(iOS 18.0, *)
struct ViewMemoryIntent: AppIntent {
    static var title: LocalizedStringResource = "View Memory"
    static var description = IntentDescription("View a specific pet memory")
    static var openAppWhenRun: Bool = true
    
    @Parameter(title: "Memory ID")
    var memoryId: String
    
    func perform() async throws -> some IntentResult {
        // Open app to specific memory
        return .result()
    }
}
