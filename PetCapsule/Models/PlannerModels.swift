//
//  PlannerModels.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 6/6/25.
//

import SwiftUI
import Foundation

// MARK: - Weather Data
struct WeatherData {
    let temperature: Int
    let humidity: Int
    let windSpeed: Double
    let condition: String
    let icon: String
    
    static let sample = WeatherData(
        temperature: 72,
        humidity: 45,
        windSpeed: 5.2,
        condition: "Sunny",
        icon: "sun.max.fill"
    )
}

// MARK: - Air Quality Data
struct AirQualityData {
    let index: Int
    let description: String
    let color: Color
    
    static let sample = AirQualityData(
        index: 42,
        description: "Good",
        color: .green
    )
}

// MARK: - Walk Recommendation
struct WalkRecommendation {
    let score: Int
    let timeSlot: String
    let reason: String
    let duration: String
    
    static let sample = WalkRecommendation(
        score: 95,
        timeSlot: "Best time: 8:00 AM - 10:00 AM",
        reason: "Perfect weather conditions with low air pollution and comfortable temperature for your pet.",
        duration: "30-45 minutes recommended"
    )
}

// MARK: - Hourly Forecast
struct HourlyForecast {
    let hour: String
    let temperature: Int
    let icon: String
    let color: Color
    let walkQuality: WalkQuality
    
    static let sampleData = [
        HourlyForecast(hour: "9 AM", temperature: 68, icon: "sun.max", color: .orange, walkQuality: .excellent),
        HourlyForecast(hour: "10 AM", temperature: 72, icon: "sun.max", color: .orange, walkQuality: .excellent),
        HourlyForecast(hour: "11 AM", temperature: 75, icon: "sun.max", color: .orange, walkQuality: .good),
        HourlyForecast(hour: "12 PM", temperature: 78, icon: "sun.max", color: .orange, walkQuality: .good),
        HourlyForecast(hour: "1 PM", temperature: 82, icon: "sun.max", color: .red, walkQuality: .fair),
        HourlyForecast(hour: "2 PM", temperature: 85, icon: "sun.max", color: .red, walkQuality: .poor),
        HourlyForecast(hour: "3 PM", temperature: 83, icon: "cloud.sun", color: .blue, walkQuality: .fair),
        HourlyForecast(hour: "4 PM", temperature: 80, icon: "cloud.sun", color: .blue, walkQuality: .good)
    ]
}

// MARK: - Weekly Forecast
struct WeeklyForecast {
    let day: String
    let highTemp: Int
    let lowTemp: Int
    let icon: String
    let color: Color
    let walkQuality: WalkQuality
    
    static let sampleData = [
        WeeklyForecast(day: "Today", highTemp: 78, lowTemp: 62, icon: "sun.max", color: .orange, walkQuality: .excellent),
        WeeklyForecast(day: "Tomorrow", highTemp: 75, lowTemp: 58, icon: "cloud.sun", color: .blue, walkQuality: .good),
        WeeklyForecast(day: "Wednesday", highTemp: 72, lowTemp: 55, icon: "cloud", color: .gray, walkQuality: .good),
        WeeklyForecast(day: "Thursday", highTemp: 69, lowTemp: 52, icon: "cloud.rain", color: .blue, walkQuality: .poor),
        WeeklyForecast(day: "Friday", highTemp: 74, lowTemp: 56, icon: "cloud.sun", color: .blue, walkQuality: .good),
        WeeklyForecast(day: "Saturday", highTemp: 79, lowTemp: 61, icon: "sun.max", color: .orange, walkQuality: .excellent),
        WeeklyForecast(day: "Sunday", highTemp: 82, lowTemp: 64, icon: "sun.max", color: .orange, walkQuality: .good)
    ]
}

// MARK: - Walk Quality
enum WalkQuality: String, CaseIterable {
    case excellent = "Excellent"
    case good = "Good"
    case fair = "Fair"
    case poor = "Poor"
    
    var color: Color {
        switch self {
        case .excellent: return .green
        case .good: return .blue
        case .fair: return .orange
        case .poor: return .red
        }
    }
    
    var description: String {
        return self.rawValue
    }
}

// MARK: - Pet Friendly Location
struct PetFriendlyLocation {
    let id = UUID()
    let name: String
    let type: LocationType
    let rating: Double
    let distance: Double
    let address: String
    let imageURL: String
    let amenities: [String]
    let isOpen: Bool
    
    static let sampleData = [
        PetFriendlyLocation(
            name: "Central Park Dog Run",
            type: .park,
            rating: 4.8,
            distance: 0.3,
            address: "123 Park Ave, New York, NY",
            imageURL: "https://example.com/park1.jpg",
            amenities: ["Off-leash area", "Water fountains", "Waste bags"],
            isOpen: true
        ),
        PetFriendlyLocation(
            name: "Pawsome Pet Store",
            type: .store,
            rating: 4.5,
            distance: 0.7,
            address: "456 Main St, New York, NY",
            imageURL: "https://example.com/store1.jpg",
            amenities: ["Pet supplies", "Grooming", "Training classes"],
            isOpen: true
        ),
        PetFriendlyLocation(
            name: "The Barking Lot Cafe",
            type: .restaurant,
            rating: 4.3,
            distance: 1.2,
            address: "789 Coffee St, New York, NY",
            imageURL: "https://example.com/cafe1.jpg",
            amenities: ["Pet-friendly patio", "Dog treats", "Water bowls"],
            isOpen: true
        ),
        PetFriendlyLocation(
            name: "Happy Tails Veterinary",
            type: .veterinary,
            rating: 4.9,
            distance: 2.1,
            address: "321 Health Ave, New York, NY",
            imageURL: "https://example.com/vet1.jpg",
            amenities: ["Emergency care", "Grooming", "Boarding"],
            isOpen: true
        ),
        PetFriendlyLocation(
            name: "Riverside Walking Trail",
            type: .trail,
            rating: 4.6,
            distance: 1.8,
            address: "Riverside Dr, New York, NY",
            imageURL: "https://example.com/trail1.jpg",
            amenities: ["Scenic views", "Multiple trails", "Pet waste stations"],
            isOpen: true
        )
    ]
}

// MARK: - Location Type
enum LocationType: String, CaseIterable {
    case park = "Park"
    case store = "Pet Store"
    case restaurant = "Restaurant"
    case veterinary = "Veterinary"
    case trail = "Trail"
    case beach = "Beach"
    case hotel = "Pet Hotel"
    
    var color: Color {
        switch self {
        case .park: return .green
        case .store: return .blue
        case .restaurant: return .orange
        case .veterinary: return .red
        case .trail: return .brown
        case .beach: return .cyan
        case .hotel: return .purple
        }
    }
    
    var icon: String {
        switch self {
        case .park: return "tree.fill"
        case .store: return "bag.fill"
        case .restaurant: return "fork.knife"
        case .veterinary: return "cross.fill"
        case .trail: return "figure.hiking"
        case .beach: return "beach.umbrella.fill"
        case .hotel: return "bed.double.fill"
        }
    }
}

// MARK: - Environmental Alert
struct EnvironmentalAlert {
    let id = UUID()
    let title: String
    let message: String
    let severity: AlertSeverity
    let icon: String
    let timeAgo: String
    
    static let sampleData = [
        EnvironmentalAlert(
            title: "High UV Index",
            message: "UV index is high today. Consider shorter walks during peak hours.",
            severity: .warning,
            icon: "sun.max.trianglebadge.exclamationmark",
            timeAgo: "2 hours ago"
        ),
        EnvironmentalAlert(
            title: "Air Quality Alert",
            message: "Air quality is moderate. Sensitive pets should limit outdoor time.",
            severity: .moderate,
            icon: "wind.circle.fill",
            timeAgo: "4 hours ago"
        )
    ]
}

// MARK: - Alert Severity
enum AlertSeverity: String, CaseIterable {
    case low = "Low"
    case moderate = "Moderate"
    case warning = "Warning"
    case severe = "Severe"
    
    var color: Color {
        switch self {
        case .low: return .green
        case .moderate: return .yellow
        case .warning: return .orange
        case .severe: return .red
        }
    }
}

// MARK: - Walk Memory
struct WalkMemory {
    let id = UUID()
    let location: String
    let date: Date
    let duration: String
    let distance: String
    let imageURL: String
    let isFavorite: Bool
    let notes: String

    static let sampleData = [
        WalkMemory(
            location: "Central Park",
            date: Date().addingTimeInterval(-86400), // Yesterday
            duration: "45 min",
            distance: "2.3 mi",
            imageURL: "https://example.com/walk1.jpg",
            isFavorite: true,
            notes: "Beautiful sunny day, Max loved the duck pond!"
        ),
        WalkMemory(
            location: "Riverside Trail",
            date: Date().addingTimeInterval(-172800), // 2 days ago
            duration: "30 min",
            distance: "1.8 mi",
            imageURL: "https://example.com/walk2.jpg",
            isFavorite: false,
            notes: "Nice evening walk, met some friendly dogs."
        ),
        WalkMemory(
            location: "Dog Beach",
            date: Date().addingTimeInterval(-259200), // 3 days ago
            duration: "60 min",
            distance: "3.1 mi",
            imageURL: "https://example.com/walk3.jpg",
            isFavorite: true,
            notes: "First time at the beach, Max was so excited!"
        ),
        WalkMemory(
            location: "Neighborhood Park",
            date: Date().addingTimeInterval(-345600), // 4 days ago
            duration: "25 min",
            distance: "1.2 mi",
            imageURL: "https://example.com/walk4.jpg",
            isFavorite: false,
            notes: "Quick morning walk before work."
        ),
        WalkMemory(
            location: "Mountain Trail",
            date: Date().addingTimeInterval(-604800), // 1 week ago
            duration: "90 min",
            distance: "4.5 mi",
            imageURL: "https://example.com/walk5.jpg",
            isFavorite: true,
            notes: "Amazing hiking adventure! Max was tired but happy."
        ),
        WalkMemory(
            location: "City Park",
            date: Date().addingTimeInterval(-691200), // 8 days ago
            duration: "35 min",
            distance: "2.0 mi",
            imageURL: "https://example.com/walk6.jpg",
            isFavorite: false,
            notes: "Busy park day, lots of other dogs to play with."
        )
    ]
}

// MARK: - Community Event
struct CommunityEvent {
    let id = UUID()
    let title: String
    let description: String
    let organizer: String
    let date: Date
    let location: String
    let category: EventCategory
    let attendees: Int
    let maxAttendees: Int?
    let isJoined: Bool
    let imageURL: String

    enum EventCategory: String, CaseIterable {
        case walks = "Group Walks"
        case training = "Training"
        case social = "Social"
        case adoption = "Adoption"

        var color: Color {
            switch self {
            case .walks: return .green
            case .training: return .blue
            case .social: return .orange
            case .adoption: return .purple
            }
        }

        var icon: String {
            switch self {
            case .walks: return "figure.walk"
            case .training: return "graduationcap.fill"
            case .social: return "person.3.fill"
            case .adoption: return "heart.fill"
            }
        }
    }

    static let sampleData = [
        CommunityEvent(
            title: "Morning Dog Walk Group",
            description: "Join us for a relaxing morning walk through Central Park. All dogs and skill levels welcome!",
            organizer: "Sarah Johnson",
            date: Date().addingTimeInterval(86400), // Tomorrow
            location: "Central Park Entrance",
            category: .walks,
            attendees: 12,
            maxAttendees: 20,
            isJoined: false,
            imageURL: "https://example.com/event1.jpg"
        ),
        CommunityEvent(
            title: "Puppy Training Class",
            description: "Basic obedience training for puppies 6 months and under. Professional trainer included.",
            organizer: "Happy Paws Training",
            date: Date().addingTimeInterval(172800), // Day after tomorrow
            location: "Community Center",
            category: .training,
            attendees: 8,
            maxAttendees: 12,
            isJoined: true,
            imageURL: "https://example.com/event2.jpg"
        ),
        CommunityEvent(
            title: "Dog Birthday Party",
            description: "Celebrate Max's 3rd birthday with cake, games, and lots of fun for dogs and their humans!",
            organizer: "The Peterson Family",
            date: Date().addingTimeInterval(259200), // 3 days from now
            location: "Riverside Dog Park",
            category: .social,
            attendees: 15,
            maxAttendees: nil,
            isJoined: false,
            imageURL: "https://example.com/event3.jpg"
        ),
        CommunityEvent(
            title: "Rescue Dog Adoption Event",
            description: "Meet adorable rescue dogs looking for their forever homes. Adoption fees waived today!",
            organizer: "City Animal Shelter",
            date: Date().addingTimeInterval(345600), // 4 days from now
            location: "Pet Store Plaza",
            category: .adoption,
            attendees: 25,
            maxAttendees: nil,
            isJoined: false,
            imageURL: "https://example.com/event4.jpg"
        ),
        CommunityEvent(
            title: "Advanced Agility Training",
            description: "For dogs who have completed basic training. Focus on jumps, tunnels, and obstacle courses.",
            organizer: "Pro Pet Training",
            date: Date().addingTimeInterval(432000), // 5 days from now
            location: "Training Facility",
            category: .training,
            attendees: 6,
            maxAttendees: 10,
            isJoined: true,
            imageURL: "https://example.com/event5.jpg"
        ),
        CommunityEvent(
            title: "Beach Day Social",
            description: "Bring your water-loving pups for a fun day at the dog beach. Swimming and beach games!",
            organizer: "Beach Dogs Club",
            date: Date().addingTimeInterval(604800), // 1 week from now
            location: "Sunset Dog Beach",
            category: .social,
            attendees: 18,
            maxAttendees: 30,
            isJoined: false,
            imageURL: "https://example.com/event6.jpg"
        )
    ]
}
