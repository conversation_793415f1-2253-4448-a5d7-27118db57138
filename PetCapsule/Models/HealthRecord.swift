//
//  HealthRecord.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 12/24/24.
//

import Foundation
import SwiftData

@Model
class HealthRecord: Identifiable {
    @Attribute(.unique) var id: UUID
    var petId: UUID
    var type: HealthRecordType
    var value: Double
    var unit: String
    var notes: String?
    var date: Date
    var createdAt: Date
    var updatedAt: Date
    
    init(
        id: UUID = UUID(),
        petId: UUID,
        type: HealthRecordType,
        value: Double,
        unit: String,
        notes: String? = nil,
        date: Date = Date()
    ) {
        self.id = id
        self.petId = petId
        self.type = type
        self.value = value
        self.unit = unit
        self.notes = notes
        self.date = date
        self.createdAt = Date()
        self.updatedAt = Date()
    }
}

enum HealthRecordType: String, CaseIterable, Codable {
    case weight = "weight"
    case temperature = "temperature"
    case heartRate = "heart_rate"
    case bloodPressure = "blood_pressure"
    case medication = "medication"
    case allergy = "allergy"
    case condition = "condition"
    case symptom = "symptom"
    case treatment = "treatment"
    case examination = "examination"
    
    var displayName: String {
        switch self {
        case .weight: return "Weight"
        case .temperature: return "Temperature"
        case .heartRate: return "Heart Rate"
        case .bloodPressure: return "Blood Pressure"
        case .medication: return "Medication"
        case .allergy: return "Allergy"
        case .condition: return "Medical Condition"
        case .symptom: return "Symptom"
        case .treatment: return "Treatment"
        case .examination: return "Examination"
        }
    }
    
    var icon: String {
        switch self {
        case .weight: return "scalemass.fill"
        case .temperature: return "thermometer"
        case .heartRate: return "heart.fill"
        case .bloodPressure: return "waveform.path.ecg"
        case .medication: return "pills.fill"
        case .allergy: return "exclamationmark.triangle.fill"
        case .condition: return "cross.case.fill"
        case .symptom: return "stethoscope"
        case .treatment: return "bandage.fill"
        case .examination: return "checkmark.circle.fill"
        }
    }
    
    var defaultUnit: String {
        switch self {
        case .weight: return "lbs"
        case .temperature: return "°F"
        case .heartRate: return "BPM"
        case .bloodPressure: return "mmHg"
        case .medication: return "mg"
        case .allergy: return ""
        case .condition: return ""
        case .symptom: return ""
        case .treatment: return ""
        case .examination: return ""
        }
    }
}
