//
//  MemoryTestView.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 6/6/25.
//

import SwiftUI
import PhotosUI

struct MemoryTestView: View {
    @EnvironmentObject var realDataService: RealDataService
    @StateObject private var storageService = SupabaseStorageService.shared
    
    @State private var selectedPhoto: PhotosPickerItem?
    @State private var testImage: UIImage?
    @State private var uploadedURL: String?
    @State private var isUploading = false
    @State private var uploadResult = ""
    @State private var showResult = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                Text("Memory Upload Test")
                    .font(.title)
                    .fontWeight(.bold)
                
                // Photo Picker
                PhotosPicker(
                    selection: $selectedPhoto,
                    matching: .images
                ) {
                    VStack {
                        Image(systemName: "photo.badge.plus")
                            .font(.system(size: 50))
                            .foregroundColor(.blue)
                        
                        Text("Select Test Image")
                            .font(.headline)
                    }
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(.ultraThinMaterial)
                    )
                }
                
                // Show selected image
                if let testImage = testImage {
                    Image(uiImage: testImage)
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(maxHeight: 200)
                        .cornerRadius(12)
                }
                
                // Upload button
                if testImage != nil {
                    Button("Test Upload") {
                        testImageUpload()
                    }
                    .disabled(isUploading)
                    .buttonStyle(.borderedProminent)
                }
                
                // Upload progress
                if isUploading {
                    VStack {
                        ProgressView()
                        Text("Uploading...")
                            .font(.caption)
                    }
                }
                
                // Results
                if showResult {
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Upload Result:")
                            .font(.headline)
                        
                        Text(uploadResult)
                            .font(.caption)
                            .foregroundColor(uploadedURL != nil ? .green : .red)
                        
                        if let url = uploadedURL {
                            AsyncImage(url: URL(string: url)) { image in
                                image
                                    .resizable()
                                    .aspectRatio(contentMode: .fit)
                            } placeholder: {
                                ProgressView()
                            }
                            .frame(maxHeight: 150)
                            .cornerRadius(8)
                        }
                    }
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(.ultraThinMaterial)
                    )
                }
                
                Spacer()
            }
            .padding()
            .navigationTitle("Debug")
            .navigationBarTitleDisplayMode(.inline)
        }
        .onChange(of: selectedPhoto) { _, newPhoto in
            Task {
                await loadSelectedPhoto(newPhoto)
            }
        }
    }
    
    private func loadSelectedPhoto(_ photo: PhotosPickerItem?) async {
        guard let photo = photo else { return }
        
        if let data = try? await photo.loadTransferable(type: Data.self),
           let image = UIImage(data: data) {
            await MainActor.run {
                testImage = image
                uploadResult = ""
                uploadedURL = nil
                showResult = false
            }
        }
    }
    
    private func testImageUpload() {
        guard let image = testImage,
              let imageData = image.optimizedForUpload() else {
            uploadResult = "Failed to prepare image data"
            showResult = true
            return
        }
        
        isUploading = true
        uploadResult = ""
        showResult = false
        
        Task {
            // Get current user ID
            guard let userId = realDataService.getCurrentUserId() else {
                await MainActor.run {
                    isUploading = false
                    uploadResult = "No authenticated user found"
                    showResult = true
                }
                return
            }
            
            let testMemoryId = UUID()
            
            print("🧪 Testing image upload - Memory ID: \(testMemoryId), User ID: \(userId)")
            print("🧪 Image data size: \(imageData.count) bytes")
            
            // Test upload
            let uploadedURL = await storageService.uploadMemoryImage(
                imageData,
                memoryId: testMemoryId,
                userId: userId
            )
            
            await MainActor.run {
                isUploading = false
                
                if let url = uploadedURL {
                    self.uploadedURL = url
                    uploadResult = "✅ Upload successful!\nURL: \(url)"
                } else {
                    uploadResult = "❌ Upload failed. Check console for details."
                }
                
                showResult = true
            }
        }
    }
}

#Preview {
    MemoryTestView()
        .environmentObject(RealDataService())
}
