//
//  StorageTestView.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 6/8/25.
//

import SwiftUI
import UIKit

struct StorageTestView: View {
    @State private var selectedImage: UIImage?
    @State private var showingImagePicker = false
    @State private var uploadResult: String = ""
    @State private var isUploading = false
    @ObservedObject private var storageService = SupabaseStorageService.shared
    @ObservedObject private var supabaseService = SupabaseService.shared
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                Text("Storage Upload Test")
                    .font(.title)
                    .padding()
                
                // User info and development mode warning
                VStack(spacing: 12) {
                    if let user = supabaseService.currentUser {
                        VStack {
                            Text("Logged in as:")
                            Text(user.email)
                                .font(.caption)
                                .foregroundColor(.secondary)
                            Text("User ID: \(user.id)")
                                .font(.caption2)
                                .foregroundColor(.secondary)
                        }
                        .padding()
                        .background(Color.gray.opacity(0.1))
                        .cornerRadius(8)
                    } else {
                        Text("Not logged in")
                            .foregroundColor(.red)
                    }

                    // Development mode warning
                    if UserDefaults.standard.bool(forKey: "isDevelopmentMode") {
                        VStack(spacing: 8) {
                            Text("⚠️ Development Mode Active")
                                .font(.headline)
                                .foregroundColor(.orange)

                            Text("Storage uploads require real authentication. Please sign out and sign in with a real account to test image uploads.")
                                .font(.caption)
                                .multilineTextAlignment(.center)
                                .foregroundColor(.secondary)

                            Button("Sign Out of Development Mode") {
                                signOutOfDevelopmentMode()
                            }
                            .buttonStyle(.bordered)
                            .foregroundColor(.orange)
                        }
                        .padding()
                        .background(Color.orange.opacity(0.1))
                        .cornerRadius(8)
                        .overlay(
                            RoundedRectangle(cornerRadius: 8)
                                .stroke(Color.orange.opacity(0.3), lineWidth: 1)
                        )
                    }
                }
                
                // Image selection
                if let image = selectedImage {
                    Image(uiImage: image)
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(height: 200)
                        .cornerRadius(8)
                } else {
                    Rectangle()
                        .fill(Color.gray.opacity(0.3))
                        .frame(height: 200)
                        .cornerRadius(8)
                        .overlay(
                            Text("No image selected")
                                .foregroundColor(.secondary)
                        )
                }
                
                Button("Select Image") {
                    showingImagePicker = true
                }
                .buttonStyle(.bordered)
                
                Button("Upload Test Image") {
                    uploadTestImage()
                }
                .buttonStyle(.borderedProminent)
                .disabled(selectedImage == nil || isUploading)
                
                if isUploading {
                    ProgressView("Uploading...")
                        .padding()
                }
                
                ScrollView {
                    Text(uploadResult)
                        .font(.caption)
                        .padding()
                        .frame(maxWidth: .infinity, alignment: .leading)
                        .background(Color.gray.opacity(0.1))
                        .cornerRadius(8)
                }
                
                Spacer()
            }
            .padding()
            .navigationTitle("Storage Test")
            .sheet(isPresented: $showingImagePicker) {
                StorageTestImagePicker(selectedImage: $selectedImage)
            }
        }
    }
    
    private func uploadTestImage() {
        guard let image = selectedImage,
              let imageData = image.optimizedForUpload(),
              let user = supabaseService.currentUser,
              let userId = UUID(uuidString: user.id) else {
            uploadResult = "❌ Missing image, user, or invalid user ID"
            return
        }

        isUploading = true
        uploadResult = "🔄 Starting upload test...\n"
        uploadResult += "📊 Image size: \(image.size)\n"
        uploadResult += "📊 Data size: \(imageData.count) bytes\n"
        uploadResult += "👤 User ID: \(userId)\n"

        Task {
            let testMemoryId = UUID()
            uploadResult += "🆔 Test Memory ID: \(testMemoryId)\n"

            let result = await storageService.uploadMemoryImage(
                imageData,
                memoryId: testMemoryId,
                userId: userId
            )

            await MainActor.run {
                isUploading = false
                if let url = result {
                    uploadResult += "✅ Upload successful!\n"
                    uploadResult += "🔗 URL: \(url)\n"
                } else {
                    uploadResult += "❌ Upload failed!\n"
                    if let error = storageService.error {
                        uploadResult += "💥 Error: \(error)\n"
                    }
                }
            }
        }
    }

    private func signOutOfDevelopmentMode() {
        UserDefaults.standard.removeObject(forKey: "isDevelopmentMode")
        UserDefaults.standard.removeObject(forKey: "developmentUserId")
        UserDefaults.standard.removeObject(forKey: "developmentUserEmail")
        UserDefaults.standard.removeObject(forKey: "developmentUserName")

        // This will trigger the app to show the authentication screen
        exit(0)
    }
}

struct StorageTestImagePicker: UIViewControllerRepresentable {
    @Binding var selectedImage: UIImage?
    @Environment(\.dismiss) private var dismiss

    func makeUIViewController(context: Context) -> UIImagePickerController {
        let picker = UIImagePickerController()
        picker.delegate = context.coordinator
        picker.sourceType = .photoLibrary
        return picker
    }

    func updateUIViewController(_ uiViewController: UIImagePickerController, context: Context) {}

    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }

    class Coordinator: NSObject, UIImagePickerControllerDelegate, UINavigationControllerDelegate {
        let parent: StorageTestImagePicker

        init(_ parent: StorageTestImagePicker) {
            self.parent = parent
        }

        func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
            if let image = info[.originalImage] as? UIImage {
                parent.selectedImage = image
            }
            parent.dismiss()
        }

        func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
            parent.dismiss()
        }
    }
}

#Preview {
    StorageTestView()
}
