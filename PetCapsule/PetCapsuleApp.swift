//
//  PetCapsuleApp.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//  Enhanced with iOS 18 Features for Apple Award Consideration
//

import SwiftUI
import SwiftData
import AppIntents
import ActivityKit

@main
struct PetCapsuleApp: App {
    var sharedModelContainer: ModelContainer = {
        let schema = Schema([
            Pet.self,
            Memory.self,
            Vault.self,
            User.self,
            MemoryGem.self
        ])
        let modelConfiguration = ModelConfiguration(schema: schema, isStoredInMemoryOnly: false)

        do {
            return try ModelContainer(for: schema, configurations: [modelConfiguration])
        } catch {
            fatalError("Could not create ModelContainer: \(error)")
        }
    }()

    var body: some Scene {
        WindowGroup {
            ContentView()
                .onAppear {
                    setupiOS18Features()
                }
        }
        .modelContainer(sharedModelContainer)
    }

    // MARK: - iOS 18 Features Setup

    private func setupiOS18Features() {
        if #available(iOS 18.0, *) {
            // Register App Shortcuts
            PetCapsuleShortcuts.updateAppShortcutParameters()

            // Initialize Apple Intelligence Service
            _ = AppleIntelligenceService.shared

            // Initialize Passkey Authentication
            _ = PasskeyAuthenticationService.shared

            // Initialize App Icon Manager
            _ = AppIconManager.shared

            // Initialize Enhanced ML Service
            _ = EnhancedMLService.shared

            // Initialize Live Activity Manager
            _ = PetLiveActivityManager.shared

            // Initialize Visual Intelligence Service
            _ = VisualIntelligenceService.shared

            // Initialize Visual Intelligence Content Provider
            _ = PetVisualIntelligenceContentProvider.shared

            // Setup notification observers for iOS 18 features
            setupNotificationObservers()
        }
    }

    private func setupNotificationObservers() {
        // Listen for passkey upgrade notifications
        NotificationCenter.default.addObserver(
            forName: .passkeyUpgradeAvailable,
            object: nil,
            queue: .main
        ) { _ in
            // Handle passkey upgrade prompt
            print("Passkey upgrade available")
        }

        // Listen for Apple Intelligence availability changes
        NotificationCenter.default.addObserver(
            forName: UIApplication.didBecomeActiveNotification,
            object: nil,
            queue: .main
        ) { _ in
            if #available(iOS 18.0, *) {
                AppleIntelligenceService.shared.objectWillChange.send()
            }
        }
    }
}
