//
//  AIResponseValidator.swift
//  PetCapsule
//
//  AI Response Validation and Formatting Service
//  Ensures agents stay within their specialties and responses are well-formatted
//

import Foundation

class AIResponseValidator {
    static let shared = AIResponseValidator()
    
    private init() {}
    
    // MARK: - Response Validation
    
    func validateAndFormatResponse(
        response: String,
        agent: AIAgent,
        userMessage: String
    ) -> String {
        // Clean and format the response to remove excessive markdown and repetitive introductions
        return cleanAndFormatResponse(response: response, agent: agent)
    }
    
    // MARK: - Specialty Validation
    
    private func isResponseWithinSpecialty(response: String, agent: AIAgent, userMessage: String) -> Bool {
        let responseKeywords = extractKeywords(from: response.lowercased())
        let messageKeywords = extractKeywords(from: userMessage.lowercased())
        
        let agentKeywords = getAgentKeywords(for: agent)
        let forbiddenKeywords = getForbiddenKeywords(for: agent)
        
        // Check if response contains forbidden topics
        for forbidden in forbiddenKeywords {
            if responseKeywords.contains(forbidden) && !agentKeywords.contains(forbidden) {
                return false
            }
        }
        
        // Check if response addresses agent's specialty
        let specialtyMatch = agentKeywords.contains { keyword in
            responseKeywords.contains(keyword) || messageKeywords.contains(keyword)
        }
        
        return specialtyMatch
    }
    
    private func getAgentKeywords(for agent: AIAgent) -> [String] {
        switch agent.name {
        case "Dr. Nutrition":
            return ["nutrition", "food", "diet", "feeding", "meal", "weight", "calories", "supplement", "treat", "kibble", "wet food", "dry food", "portion", "feeding schedule"]
            
        case "Health Guardian":
            return ["health", "symptom", "medical", "vet", "veterinarian", "illness", "disease", "vaccination", "checkup", "medicine", "treatment", "diagnosis", "prevention"]
            
        case "Style Guru":
            return ["grooming", "brushing", "bathing", "nail", "trim", "coat", "fur", "skin", "dental", "teeth", "styling", "haircut", "shampoo", "conditioner"]
            
        case "Trainer Pro":
            return ["training", "behavior", "obedience", "command", "sit", "stay", "come", "heel", "leash", "socialization", "positive reinforcement", "trick", "discipline"]
            
        case "Shopping Assistant":
            return ["product", "buy", "purchase", "recommend", "price", "cost", "brand", "toy", "accessory", "equipment", "collar", "leash", "bed", "carrier"]
            
        case "Wellness Coach":
            return ["wellness", "mental health", "stress", "anxiety", "exercise", "activity", "enrichment", "bonding", "quality of life", "emotional", "calm", "relaxation"]
            
        default:
            return []
        }
    }
    
    private func getForbiddenKeywords(for agent: AIAgent) -> [String] {
        let allKeywords = [
            "nutrition", "food", "diet", "feeding", "health", "medical", "grooming", "training", "shopping", "wellness", "behavior", "symptom", "product", "exercise"
        ]
        
        let agentKeywords = getAgentKeywords(for: agent)
        return allKeywords.filter { !agentKeywords.contains($0) }
    }
    
    private func extractKeywords(from text: String) -> [String] {
        let words = text.components(separatedBy: .whitespacesAndNewlines)
        return words.map { $0.trimmingCharacters(in: .punctuationCharacters) }
    }
    
    // MARK: - Response Formatting
    
    private func formatResponse(response: String, agent: AIAgent) -> String {
        // Return response with minimal formatting to preserve Gemini's output
        if response.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            return getAgentGreeting(for: agent) + "\n\nI'm here to help! Please ask me anything about " + agent.specialties.joined(separator: ", ") + "."
        }
        return response
    }

    private func cleanAndFormatResponse(response: String, agent: AIAgent) -> String {
        var cleanedResponse = response

        // Remove excessive markdown formatting
        cleanedResponse = cleanedResponse.replacingOccurrences(of: "##", with: "")
        cleanedResponse = cleanedResponse.replacingOccurrences(of: "###", with: "")
        cleanedResponse = cleanedResponse.replacingOccurrences(of: "**", with: "")
        cleanedResponse = cleanedResponse.replacingOccurrences(of: "*", with: "")
        cleanedResponse = cleanedResponse.replacingOccurrences(of: "--", with: "")
        cleanedResponse = cleanedResponse.replacingOccurrences(of: "---", with: "")

        // Remove repetitive agent introductions
        let introPatterns = [
            "Hello! I'm \(agent.name)",
            "I'm \(agent.name)",
            "Hello! I'm Health Guardian",
            "I'm Health Guardian",
            "your pet health monitoring specialist",
            "your health monitoring specialist",
            "How can I help you",
            "How can I help",
            "I can help assess"
        ]

        for pattern in introPatterns {
            cleanedResponse = cleanedResponse.replacingOccurrences(of: pattern, with: "", options: .caseInsensitive)
        }

        // Clean up extra whitespace and newlines
        cleanedResponse = cleanedResponse.replacingOccurrences(of: "\n\n\n", with: "\n\n")
        cleanedResponse = cleanedResponse.trimmingCharacters(in: .whitespacesAndNewlines)

        // Split into meaningful sections
        let sections = cleanedResponse.components(separatedBy: "\n\n").filter {
            !$0.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
        }

        // Format response concisely
        var formattedResponse = ""

        for (index, section) in sections.enumerated() {
            let cleanSection = section.trimmingCharacters(in: .whitespacesAndNewlines)
            if !cleanSection.isEmpty {
                if index > 0 {
                    formattedResponse += "\n\n"
                }
                formattedResponse += formatSectionConcisely(cleanSection)
            }
        }

        // Ensure response is not too long
        if formattedResponse.count > 500 {
            formattedResponse = String(formattedResponse.prefix(500)) + "..."
        }

        return formattedResponse.isEmpty ? "I'm here to help with your \(agent.specialties.first ?? "pet care") questions!" : formattedResponse
    }

    private func formatSectionConcisely(_ content: String) -> String {
        let lines = content.components(separatedBy: .newlines).filter {
            !$0.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
        }

        var formattedSection = ""

        for (index, line) in lines.enumerated() {
            let cleanLine = line.trimmingCharacters(in: .whitespacesAndNewlines)
            if !cleanLine.isEmpty {
                if index > 0 {
                    formattedSection += "\n"
                }

                // Format as bullet point if it's a list item
                if cleanLine.hasPrefix("•") || cleanLine.hasPrefix("-") {
                    formattedSection += cleanLine
                } else if lines.count > 1 && !cleanLine.hasSuffix(".") && !cleanLine.hasSuffix("!") && !cleanLine.hasSuffix("?") {
                    formattedSection += "• \(cleanLine)"
                } else {
                    formattedSection += cleanLine
                }
            }
        }

        return formattedSection
    }
    
    private func applyAgentFormatting(response: String, agent: AIAgent) -> String {
        let greeting = getAgentGreeting(for: agent)
        let sections = splitIntoSections(response)
        
        var formattedResponse = greeting + "\n\n"
        
        for (index, section) in sections.enumerated() {
            let sectionTitle = getSectionTitle(for: index, agent: agent)
            formattedResponse += "## \(sectionTitle)\n\n"
            formattedResponse += formatSectionContent(section) + "\n\n"
        }
        
        formattedResponse += getAgentClosing(for: agent)
        
        return formattedResponse
    }
    
    private func getAgentGreeting(for agent: AIAgent) -> String {
        switch agent.name {
        case "Dr. Nutrition":
            return "Hello! I'm Dr. Nutrition 🥗, your pet nutrition specialist."
        case "Health Guardian":
            return "Hello! I'm Health Guardian 🏥, your pet health monitoring specialist."
        case "Style Guru":
            return "Hello! I'm Style Guru ✂️, your pet grooming and styling specialist."
        case "Trainer Pro":
            return "Hello! I'm Trainer Pro 🎾, your pet training and behavior specialist."
        case "Shopping Assistant":
            return "Hello! I'm Shopping Assistant 🛍️, your pet product and shopping specialist."
        case "Wellness Coach":
            return "Hello! I'm Wellness Coach 🧘‍♀️, your pet wellness and mental health specialist."
        default:
            return "Hello! I'm here to help with your pet care needs."
        }
    }
    
    private func getSectionTitle(for index: Int, agent: AIAgent) -> String {
        let sectionTitles: [String]
        
        switch agent.name {
        case "Dr. Nutrition":
            sectionTitles = ["Nutritional Assessment", "Recommendations", "Feeding Schedule", "Next Steps"]
        case "Health Guardian":
            sectionTitles = ["Health Assessment", "Observations", "Recommendations", "Monitoring Plan"]
        case "Style Guru":
            sectionTitles = ["Grooming Assessment", "Step-by-Step Guide", "Tools Needed", "Maintenance Schedule"]
        case "Trainer Pro":
            sectionTitles = ["Behavior Assessment", "Training Plan", "Instructions", "Progress Tracking"]
        case "Shopping Assistant":
            sectionTitles = ["Product Analysis", "Recommendations", "Price Comparison", "Purchase Priority"]
        case "Wellness Coach":
            sectionTitles = ["Wellness Assessment", "Activity Plan", "Environmental Tips", "Bonding Strategies"]
        default:
            sectionTitles = ["Assessment", "Recommendations", "Action Plan", "Next Steps"]
        }
        
        return index < sectionTitles.count ? sectionTitles[index] : "Additional Information"
    }
    
    private func splitIntoSections(_ response: String) -> [String] {
        let sentences = response.components(separatedBy: ". ")
        let sectionSize = max(1, sentences.count / 3)
        
        var sections: [String] = []
        for i in stride(from: 0, to: sentences.count, by: sectionSize) {
            let endIndex = min(i + sectionSize, sentences.count)
            let section = Array(sentences[i..<endIndex]).joined(separator: ". ")
            sections.append(section)
        }
        
        return sections
    }
    
    private func formatSectionContent(_ content: String) -> String {
        let sentences = content.components(separatedBy: ". ")
        return sentences.map { "• \($0.trimmingCharacters(in: .whitespacesAndNewlines))" }.joined(separator: "\n")
    }
    
    private func getAgentClosing(for agent: AIAgent) -> String {
        switch agent.name {
        case "Dr. Nutrition":
            return "🥗 **Remember**: Always transition to new foods gradually over 7-10 days to avoid digestive upset!"
        case "Health Guardian":
            return "🏥 **Important**: If you notice any concerning symptoms, please consult your veterinarian promptly."
        case "Style Guru":
            return "✂️ **Tip**: Regular grooming keeps your pet healthy and strengthens your bond together!"
        case "Trainer Pro":
            return "🎾 **Success Key**: Consistency and positive reinforcement are the foundations of great training!"
        case "Shopping Assistant":
            return "🛍️ **Smart Shopping**: Always check reviews and ensure products are appropriate for your pet's size and age!"
        case "Wellness Coach":
            return "🧘‍♀️ **Wellness Reminder**: A happy pet is a healthy pet - focus on both physical and mental well-being!"
        default:
            return "Feel free to ask if you need more specific guidance!"
        }
    }
    
    // MARK: - Redirection Response
    
    private func generateRedirectionResponse(for agent: AIAgent, userMessage: String) -> String {
        let greeting = getAgentGreeting(for: agent)
        let specialty = agent.specialties.joined(separator: ", ")
        let redirectionSuggestion = getRedirectionSuggestion(for: agent, userMessage: userMessage)
        
        return """
        \(greeting)
        
        ## Outside My Specialty Area
        
        I specialize in **\(specialty)**, but your question seems to be about a different area of pet care.
        
        ## Recommended Specialist
        
        \(redirectionSuggestion)
        
        ## How I Can Help
        
        I'd be happy to assist you with any questions related to:
        \(agent.specialties.map { "• \($0)" }.joined(separator: "\n"))
        
        Feel free to ask me anything within my area of expertise! 😊
        """
    }
    
    private func getRedirectionSuggestion(for agent: AIAgent, userMessage: String) -> String {
        let message = userMessage.lowercased()
        
        if message.contains("health") || message.contains("sick") || message.contains("symptom") {
            return "• For health concerns, please consult **Health Guardian** 🏥"
        } else if message.contains("food") || message.contains("diet") || message.contains("nutrition") {
            return "• For nutrition questions, **Dr. Nutrition** 🥗 is your expert"
        } else if message.contains("grooming") || message.contains("bath") || message.contains("brush") {
            return "• For grooming advice, **Style Guru** ✂️ can help you"
        } else if message.contains("training") || message.contains("behavior") || message.contains("command") {
            return "• For training guidance, **Trainer Pro** 🎾 is your specialist"
        } else if message.contains("buy") || message.contains("product") || message.contains("recommend") {
            return "• For product recommendations, **Shopping Assistant** 🛍️ can assist"
        } else if message.contains("stress") || message.contains("anxiety") || message.contains("wellness") {
            return "• For wellness concerns, **Wellness Coach** 🧘‍♀️ is your guide"
        } else {
            return "• Please try asking one of our other specialists who can better help with your question"
        }
    }
}
