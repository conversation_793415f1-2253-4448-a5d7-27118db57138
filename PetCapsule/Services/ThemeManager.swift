//
//  ThemeManager.swift
//  PetCapsule
//
//  Manages app-wide theme and appearance settings
//

import SwiftUI
import Combine

enum AppTheme: String, CaseIterable {
    case system = "System"
    case light = "Light"
    case dark = "Dark"
    
    var colorScheme: ColorScheme? {
        switch self {
        case .system:
            return nil
        case .light:
            return .light
        case .dark:
            return .dark
        }
    }
    
    var icon: String {
        switch self {
        case .system:
            return "circle.lefthalf.filled"
        case .light:
            return "sun.max.fill"
        case .dark:
            return "moon.fill"
        }
    }
}

@MainActor
class ThemeManager: ObservableObject {
    static let shared = ThemeManager()
    
    @Published var currentTheme: AppTheme {
        didSet {
            UserDefaults.standard.set(currentTheme.rawValue, forKey: "selectedTheme")
            applyTheme()
        }
    }
    
    @Published var isDarkMode: Bool = false
    
    private init() {
        // Load saved theme or default to system
        let savedTheme = UserDefaults.standard.string(forKey: "selectedTheme") ?? AppTheme.system.rawValue
        self.currentTheme = AppTheme(rawValue: savedTheme) ?? .system
        
        // Set initial dark mode state
        updateDarkModeState()
        applyTheme()
    }
    
    func setTheme(_ theme: AppTheme) {
        currentTheme = theme
    }
    
    private func applyTheme() {
        updateDarkModeState()
        
        // Apply to all windows
        for scene in UIApplication.shared.connectedScenes {
            if let windowScene = scene as? UIWindowScene {
                for window in windowScene.windows {
                    window.overrideUserInterfaceStyle = currentTheme.colorScheme?.uiUserInterfaceStyle ?? .unspecified
                }
            }
        }
    }
    
    private func updateDarkModeState() {
        switch currentTheme {
        case .system:
            isDarkMode = UITraitCollection.current.userInterfaceStyle == .dark
        case .light:
            isDarkMode = false
        case .dark:
            isDarkMode = true
        }
    }
}

// MARK: - ColorScheme Extension
extension ColorScheme {
    var uiUserInterfaceStyle: UIUserInterfaceStyle {
        switch self {
        case .light:
            return .light
        case .dark:
            return .dark
        @unknown default:
            return .unspecified
        }
    }
}

// MARK: - Theme-Aware Colors
extension Color {
    static var themeBackground: Color {
        Color(UIColor.systemBackground)
    }
    
    static var themeSecondaryBackground: Color {
        Color(UIColor.secondarySystemBackground)
    }
    
    static var themeTertiaryBackground: Color {
        Color(UIColor.tertiarySystemBackground)
    }
    
    static var themePrimary: Color {
        Color(UIColor.label)
    }
    
    static var themeSecondary: Color {
        Color(UIColor.secondaryLabel)
    }
    
    static var themeTertiary: Color {
        Color(UIColor.tertiaryLabel)
    }
    
    static var themeAccent: Color {
        Color.purple
    }
    
    static var themeBorder: Color {
        Color(UIColor.separator)
    }
    
    static var themeCardBackground: Color {
        Color(UIColor.systemBackground)
    }
    
    static var themeCardShadow: Color {
        Color.black.opacity(0.1)
    }
}

// MARK: - Theme-Aware Gradients
extension LinearGradient {
    static var themeCard: LinearGradient {
        LinearGradient(
            colors: [
                Color.themeCardBackground,
                Color.themeSecondaryBackground.opacity(0.5)
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }
    
    static var themeAccentGradient: LinearGradient {
        LinearGradient(
            colors: [
                Color.purple.opacity(0.8),
                Color.blue.opacity(0.6)
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }
}

// MARK: - View Modifiers
struct ThemeCardModifier: ViewModifier {
    @EnvironmentObject private var themeManager: ThemeManager
    
    func body(content: Content) -> some View {
        content
            .background(Color.themeCardBackground)
            .cornerRadius(16)
            .shadow(
                color: Color.themeCardShadow,
                radius: themeManager.isDarkMode ? 0 : 8,
                x: 0,
                y: themeManager.isDarkMode ? 0 : 2
            )
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(Color.themeBorder.opacity(themeManager.isDarkMode ? 0.3 : 0.1), lineWidth: 1)
            )
    }
}

struct ThemeButtonModifier: ViewModifier {
    let style: ButtonStyleType
    @EnvironmentObject private var themeManager: ThemeManager
    
    func body(content: Content) -> some View {
        content
            .background(
                style == .primary ? Color.themeAccent : Color.themeSecondaryBackground
            )
            .foregroundColor(
                style == .primary ? .white : Color.themePrimary
            )
            .cornerRadius(12)
            .shadow(
                color: Color.themeCardShadow,
                radius: themeManager.isDarkMode ? 0 : 4,
                x: 0,
                y: themeManager.isDarkMode ? 0 : 2
            )
    }
}

// MARK: - View Extensions
extension View {
    func themeCard() -> some View {
        modifier(ThemeCardModifier())
    }
    
    func themeButton(_ style: ButtonStyleType = .primary) -> some View {
        modifier(ThemeButtonModifier(style: style))
    }
    
    func preferredColorScheme(_ theme: AppTheme) -> some View {
        self.preferredColorScheme(theme.colorScheme)
    }
}
