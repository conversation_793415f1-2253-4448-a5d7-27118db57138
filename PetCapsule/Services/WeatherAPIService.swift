//
//  WeatherAPIService.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 6/6/25.
//

import Foundation
import CoreLocation
import SwiftUI

class WeatherAPIService: ObservableObject {
    static let shared = WeatherAPIService()
    
    // Using OpenWeatherMap API (free tier)
    private let apiKey = APIKeys.getAPIKey(for: .openWeather)
    private let baseURL = APIEndpoints.openWeatherBase
    private let airQualityURL = APIEndpoints.airQualityBase
    
    private init() {}
    
    // MARK: - Current Weather
    
    func getCurrentWeather(for location: CLLocationCoordinate2D) async throws -> WeatherData {
        let url = "\(baseURL)/weather"
        var components = URLComponents(string: url)!
        components.queryItems = [
            URLQueryItem(name: "lat", value: "\(location.latitude)"),
            URLQueryItem(name: "lon", value: "\(location.longitude)"),
            URLQueryItem(name: "appid", value: apiKey),
            URLQueryItem(name: "units", value: "imperial")
        ]
        
        let (data, response) = try await URLSession.shared.data(from: components.url!)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            print("❌ Weather API Error: \(response)")
            // Return sample data if API fails
            return WeatherData.sample
        }
        
        let weatherResponse = try JSONDecoder().decode(OpenWeatherResponse.self, from: data)
        return convertToWeatherData(weatherResponse)
    }
    
    // MARK: - Hourly Forecast
    
    func getHourlyForecast(for location: CLLocationCoordinate2D) async throws -> [HourlyForecast] {
        let url = "\(baseURL)/forecast"
        var components = URLComponents(string: url)!
        components.queryItems = [
            URLQueryItem(name: "lat", value: "\(location.latitude)"),
            URLQueryItem(name: "lon", value: "\(location.longitude)"),
            URLQueryItem(name: "appid", value: apiKey),
            URLQueryItem(name: "units", value: "imperial"),
            URLQueryItem(name: "cnt", value: "8") // Next 8 hours
        ]
        
        let (data, response) = try await URLSession.shared.data(from: components.url!)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            print("❌ Forecast API Error: \(response)")
            return HourlyForecast.sampleData
        }
        
        let forecastResponse = try JSONDecoder().decode(OpenWeatherForecastResponse.self, from: data)
        return forecastResponse.list.map { convertToHourlyForecast($0) }
    }
    
    // MARK: - Air Quality
    
    func getAirQuality(for location: CLLocationCoordinate2D) async throws -> AirQualityData {
        let url = airQualityURL
        var components = URLComponents(string: url)!
        components.queryItems = [
            URLQueryItem(name: "lat", value: "\(location.latitude)"),
            URLQueryItem(name: "lon", value: "\(location.longitude)"),
            URLQueryItem(name: "appid", value: apiKey)
        ]
        
        let (data, response) = try await URLSession.shared.data(from: components.url!)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            print("❌ Air Quality API Error: \(response)")
            return AirQualityData.sample
        }
        
        let airQualityResponse = try JSONDecoder().decode(OpenWeatherAirQualityResponse.self, from: data)
        return convertToAirQualityData(airQualityResponse)
    }
    
    // MARK: - Walk Recommendation
    
    func generateWalkRecommendation(weather: WeatherData, airQuality: AirQualityData) -> WalkRecommendation {
        var score = 100
        var reasons: [String] = []
        
        // Temperature scoring
        if weather.temperature < 32 {
            score -= 30
            reasons.append("Very cold temperature")
        } else if weather.temperature < 45 {
            score -= 15
            reasons.append("Cold temperature")
        } else if weather.temperature > 85 {
            score -= 20
            reasons.append("Hot temperature")
        } else if weather.temperature > 75 {
            score -= 10
            reasons.append("Warm temperature")
        }
        
        // Humidity scoring
        if weather.humidity > 80 {
            score -= 15
            reasons.append("High humidity")
        }
        
        // Air quality scoring
        if airQuality.index > 100 {
            score -= 25
            reasons.append("Poor air quality")
        } else if airQuality.index > 50 {
            score -= 10
            reasons.append("Moderate air quality")
        }
        
        // Wind speed (assuming we have it)
        // if weather.windSpeed > 15 {
        //     score -= 10
        //     reasons.append("Strong winds")
        // }
        
        score = max(0, score)
        
        let timeSlot = getBestTimeSlot(score: score)
        let reason = generateReasonText(score: score, reasons: reasons)
        
        return WalkRecommendation(
            score: score,
            timeSlot: timeSlot,
            reason: reason,
            duration: getDurationRecommendation(score: score)
        )
    }
    
    // MARK: - Helper Methods
    
    private func convertToWeatherData(_ response: OpenWeatherResponse) -> WeatherData {
        let iconName = getWeatherIcon(response.weather.first?.main ?? "Clear")
        
        return WeatherData(
            temperature: Int(response.main.temp),
            humidity: response.main.humidity,
            windSpeed: response.wind?.speed ?? 0.0,
            condition: response.weather.first?.main ?? "Clear",
            icon: iconName
        )
    }
    
    private func convertToHourlyForecast(_ item: OpenWeatherForecastItem) -> HourlyForecast {
        let hour = formatHour(from: item.dt)
        let iconName = getWeatherIcon(item.weather.first?.main ?? "Clear")
        let walkQuality = calculateWalkQuality(temp: item.main.temp, humidity: item.main.humidity)
        
        return HourlyForecast(
            hour: hour,
            temperature: Int(item.main.temp),
            icon: iconName,
            color: getWeatherColor(item.weather.first?.main ?? "Clear"),
            walkQuality: walkQuality
        )
    }
    
    private func convertToAirQualityData(_ response: OpenWeatherAirQualityResponse) -> AirQualityData {
        let aqi = response.list.first?.main.aqi ?? 1
        let (description, color) = getAirQualityInfo(aqi: aqi)
        
        return AirQualityData(
            index: aqi * 20, // Convert 1-5 scale to 0-100
            description: description,
            color: color
        )
    }
    
    private func getWeatherIcon(_ condition: String) -> String {
        switch condition.lowercased() {
        case "clear":
            return "sun.max.fill"
        case "clouds":
            return "cloud.fill"
        case "rain":
            return "cloud.rain.fill"
        case "snow":
            return "cloud.snow.fill"
        case "thunderstorm":
            return "cloud.bolt.rain.fill"
        default:
            return "sun.max.fill"
        }
    }
    
    private func getWeatherColor(_ condition: String) -> Color {
        switch condition.lowercased() {
        case "clear":
            return .orange
        case "clouds":
            return .gray
        case "rain":
            return .blue
        case "snow":
            return .cyan
        case "thunderstorm":
            return .purple
        default:
            return .orange
        }
    }
    
    private func calculateWalkQuality(temp: Double, humidity: Int) -> WalkQuality {
        var score = 100
        
        if temp < 32 || temp > 85 {
            score -= 40
        } else if temp < 45 || temp > 75 {
            score -= 20
        }
        
        if humidity > 80 {
            score -= 20
        }
        
        switch score {
        case 80...100:
            return .excellent
        case 60...79:
            return .good
        case 40...59:
            return .fair
        default:
            return .poor
        }
    }
    
    private func getAirQualityInfo(aqi: Int) -> (String, Color) {
        switch aqi {
        case 1:
            return ("Good", .green)
        case 2:
            return ("Fair", .yellow)
        case 3:
            return ("Moderate", .orange)
        case 4:
            return ("Poor", .red)
        case 5:
            return ("Very Poor", .purple)
        default:
            return ("Good", .green)
        }
    }
    
    private func formatHour(from timestamp: TimeInterval) -> String {
        let date = Date(timeIntervalSince1970: timestamp)
        let formatter = DateFormatter()
        formatter.dateFormat = "h a"
        return formatter.string(from: date)
    }
    
    private func getBestTimeSlot(score: Int) -> String {
        if score >= 80 {
            return "Best time: Now - 2:00 PM"
        } else if score >= 60 {
            return "Good time: 8:00 AM - 10:00 AM"
        } else {
            return "Consider indoor activities"
        }
    }
    
    private func generateReasonText(score: Int, reasons: [String]) -> String {
        if score >= 80 {
            return "Perfect weather conditions for your pet. Great temperature and air quality!"
        } else if score >= 60 {
            return "Good conditions with minor considerations: \(reasons.joined(separator: ", "))"
        } else {
            return "Challenging conditions: \(reasons.joined(separator: ", ")). Consider shorter walks or indoor activities."
        }
    }
    
    private func getDurationRecommendation(score: Int) -> String {
        switch score {
        case 80...100:
            return "45-60 minutes recommended"
        case 60...79:
            return "30-45 minutes recommended"
        case 40...59:
            return "15-30 minutes recommended"
        default:
            return "10-15 minutes or indoor activities"
        }
    }
}

// MARK: - OpenWeather API Models

struct OpenWeatherResponse: Codable {
    let main: OpenWeatherMain
    let weather: [OpenWeatherWeather]
    let wind: OpenWeatherWind?
}

struct OpenWeatherMain: Codable {
    let temp: Double
    let humidity: Int
}

struct OpenWeatherWeather: Codable {
    let main: String
    let description: String
}

struct OpenWeatherWind: Codable {
    let speed: Double
}

struct OpenWeatherForecastResponse: Codable {
    let list: [OpenWeatherForecastItem]
}

struct OpenWeatherForecastItem: Codable {
    let dt: TimeInterval
    let main: OpenWeatherMain
    let weather: [OpenWeatherWeather]
}

struct OpenWeatherAirQualityResponse: Codable {
    let list: [OpenWeatherAirQualityItem]
}

struct OpenWeatherAirQualityItem: Codable {
    let main: OpenWeatherAirQualityMain
}

struct OpenWeatherAirQualityMain: Codable {
    let aqi: Int
}
