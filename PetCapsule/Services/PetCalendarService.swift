//
//  PetCalendarService.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 12/24/24.
//

import Foundation
import EventKit
import EventKitUI
import SwiftUI
import Combine

@MainActor
class PetCalendarService: ObservableObject {
    static let shared = PetCalendarService()
    
    @Published var isAuthorized = false
    @Published var petEvents: [PetEvent] = []
    @Published var upcomingEvents: [PetEvent] = []
    @Published var calendarSyncEnabled = true
    
    private let eventStore = EKEventStore()
    private var petCalendar: EKCalendar?
    
    private init() {
        requestCalendarAccess()
    }
    
    // MARK: - Authorization
    
    func requestCalendarAccess() {
        switch EKEventStore.authorizationStatus(for: .event) {
        case .notDetermined:
            eventStore.requestAccess(to: .event) { [weak self] granted, error in
                DispatchQueue.main.async {
                    self?.isAuthorized = granted
                    if granted {
                        self?.setupPetCalendar()
                    }
                }
            }
        case .authorized:
            isAuthorized = true
            setupPetCalendar()
        case .denied, .restricted:
            isAuthorized = false
        @unknown default:
            isAuthorized = false
        }
    }
    
    // MARK: - Calendar Setup
    
    private func setupPetCalendar() {
        // Find existing PetCapsule calendar or create new one
        let calendars = eventStore.calendars(for: .event)
        
        if let existingCalendar = calendars.first(where: { $0.title == "PetCapsule" }) {
            petCalendar = existingCalendar
        } else {
            createPetCalendar()
        }
        
        loadPetEvents()
    }
    
    private func createPetCalendar() {
        let calendar = EKCalendar(for: .event, eventStore: eventStore)
        calendar.title = "PetCapsule"
        calendar.cgColor = UIColor.systemBlue.cgColor
        
        // Use default source (usually iCloud)
        if let source = eventStore.defaultCalendarForNewEvents?.source {
            calendar.source = source
        }
        
        do {
            try eventStore.saveCalendar(calendar, commit: true)
            petCalendar = calendar
        } catch {
            print("Failed to create PetCapsule calendar: \(error)")
        }
    }
    
    // MARK: - Event Management
    
    func loadPetEvents() {
        guard let calendar = petCalendar else { return }
        
        let startDate = Calendar.current.date(byAdding: .month, value: -1, to: Date()) ?? Date()
        let endDate = Calendar.current.date(byAdding: .month, value: 6, to: Date()) ?? Date()
        
        let predicate = eventStore.predicateForEvents(
            withStart: startDate,
            end: endDate,
            calendars: [calendar]
        )
        
        let events = eventStore.events(matching: predicate)
        
        petEvents = events.compactMap { event in
            PetEvent.fromEKEvent(event)
        }
        
        updateUpcomingEvents()
    }
    
    private func updateUpcomingEvents() {
        let now = Date()
        let nextWeek = Calendar.current.date(byAdding: .weekOfYear, value: 1, to: now) ?? now
        
        upcomingEvents = petEvents.filter { event in
            event.startDate >= now && event.startDate <= nextWeek
        }.sorted { $0.startDate < $1.startDate }
    }
    
    // MARK: - Vet Appointment Management
    
    func scheduleVetAppointment(
        for pet: Pet,
        title: String,
        date: Date,
        duration: TimeInterval = 3600, // 1 hour default
        veterinarian: String? = nil,
        notes: String? = nil,
        location: String? = nil
    ) async throws -> PetEvent {
        
        guard let calendar = petCalendar else {
            throw CalendarError.calendarNotAvailable
        }
        
        let event = EKEvent(eventStore: eventStore)
        event.title = title
        event.startDate = date
        event.endDate = date.addingTimeInterval(duration)
        event.calendar = calendar
        event.notes = buildEventNotes(pet: pet, veterinarian: veterinarian, notes: notes)
        
        if let location = location {
            event.location = location
        }
        
        // Add alarms
        let reminderAlarm = EKAlarm(relativeOffset: -24 * 60 * 60) // 1 day before
        let urgentAlarm = EKAlarm(relativeOffset: -60 * 60) // 1 hour before
        event.alarms = [reminderAlarm, urgentAlarm]
        
        // Set custom properties
        event.setValue(pet.id.uuidString, forKey: "petId")
        event.setValue("vet_appointment", forKey: "eventType")
        
        do {
            try eventStore.save(event, span: .thisEvent)
            
            let petEvent = PetEvent(
                id: UUID(),
                ekEventIdentifier: event.eventIdentifier,
                title: title,
                startDate: date,
                endDate: date.addingTimeInterval(duration),
                type: .vetAppointment,
                petId: pet.id,
                location: location,
                notes: notes,
                veterinarian: veterinarian,
                isCompleted: false
            )
            
            petEvents.append(petEvent)
            updateUpcomingEvents()
            
            return petEvent
            
        } catch {
            throw CalendarError.failedToSaveEvent(error)
        }
    }
    
    // MARK: - Vaccination Scheduling
    
    func scheduleVaccination(
        for pet: Pet,
        vaccineName: String,
        date: Date,
        veterinarian: String? = nil,
        location: String? = nil
    ) async throws -> PetEvent {
        
        let title = "\(pet.name) - \(vaccineName) Vaccination"
        
        return try await scheduleVetAppointment(
            for: pet,
            title: title,
            date: date,
            duration: 1800, // 30 minutes
            veterinarian: veterinarian,
            notes: "Vaccination appointment for \(vaccineName)",
            location: location
        )
    }
    
    // MARK: - Medication Reminders
    
    func scheduleMedicationReminder(
        for pet: Pet,
        medicationName: String,
        schedule: MedicationSchedule
    ) async throws -> [PetEvent] {
        
        guard let calendar = petCalendar else {
            throw CalendarError.calendarNotAvailable
        }
        
        var events: [PetEvent] = []
        let dates = generateMedicationDates(schedule: schedule)
        
        for date in dates {
            let event = EKEvent(eventStore: eventStore)
            event.title = "\(pet.name) - \(medicationName)"
            event.startDate = date
            event.endDate = date.addingTimeInterval(300) // 5 minutes
            event.calendar = calendar
            event.notes = "Medication reminder for \(pet.name)"
            event.isAllDay = false
            
            // Add reminder alarm
            let alarm = EKAlarm(relativeOffset: 0) // At the time
            event.alarms = [alarm]
            
            // Set recurrence if needed
            if let recurrenceRule = createRecurrenceRule(for: schedule) {
                event.recurrenceRules = [recurrenceRule]
            }
            
            do {
                try eventStore.save(event, span: .thisEvent)
                
                let petEvent = PetEvent(
                    id: UUID(),
                    ekEventIdentifier: event.eventIdentifier,
                    title: event.title,
                    startDate: date,
                    endDate: event.endDate,
                    type: .medicationReminder,
                    petId: pet.id,
                    location: nil,
                    notes: "Medication: \(medicationName)",
                    veterinarian: nil,
                    isCompleted: false
                )
                
                events.append(petEvent)
                
            } catch {
                print("Failed to save medication reminder: \(error)")
            }
        }
        
        petEvents.append(contentsOf: events)
        updateUpcomingEvents()
        
        return events
    }
    
    // MARK: - Grooming Appointments
    
    func scheduleGroomingAppointment(
        for pet: Pet,
        date: Date,
        groomer: String? = nil,
        services: [String] = [],
        location: String? = nil
    ) async throws -> PetEvent {
        
        let servicesText = services.isEmpty ? "Grooming" : services.joined(separator: ", ")
        let title = "\(pet.name) - \(servicesText)"
        
        let notes = buildGroomingNotes(services: services, groomer: groomer)
        
        return try await scheduleVetAppointment(
            for: pet,
            title: title,
            date: date,
            duration: 7200, // 2 hours
            veterinarian: groomer,
            notes: notes,
            location: location
        )
    }
    
    // MARK: - Event Updates
    
    func updateEvent(_ petEvent: PetEvent) async throws {
        guard let ekEvent = eventStore.event(withIdentifier: petEvent.ekEventIdentifier) else {
            throw CalendarError.eventNotFound
        }
        
        ekEvent.title = petEvent.title
        ekEvent.startDate = petEvent.startDate
        ekEvent.endDate = petEvent.endDate
        ekEvent.location = petEvent.location
        ekEvent.notes = petEvent.notes
        
        do {
            try eventStore.save(ekEvent, span: .thisEvent)
            
            // Update local array
            if let index = petEvents.firstIndex(where: { $0.id == petEvent.id }) {
                petEvents[index] = petEvent
            }
            
            updateUpcomingEvents()
            
        } catch {
            throw CalendarError.failedToUpdateEvent(error)
        }
    }
    
    func deleteEvent(_ petEvent: PetEvent) async throws {
        guard let ekEvent = eventStore.event(withIdentifier: petEvent.ekEventIdentifier) else {
            throw CalendarError.eventNotFound
        }
        
        do {
            try eventStore.remove(ekEvent, span: .thisEvent)
            
            // Remove from local array
            petEvents.removeAll { $0.id == petEvent.id }
            updateUpcomingEvents()
            
        } catch {
            throw CalendarError.failedToDeleteEvent(error)
        }
    }
    
    func markEventCompleted(_ petEvent: PetEvent) async throws {
        var updatedEvent = petEvent
        updatedEvent.isCompleted = true
        
        try await updateEvent(updatedEvent)
    }
    
    // MARK: - Helper Methods
    
    private func buildEventNotes(pet: Pet, veterinarian: String?, notes: String?) -> String {
        var eventNotes = "Pet: \(pet.name) (\(pet.species))\n"
        eventNotes += "Age: \(pet.age) years\n"
        eventNotes += "Weight: \(pet.weight) lbs\n"
        
        if let vet = veterinarian {
            eventNotes += "Veterinarian: \(vet)\n"
        }
        
        if let additionalNotes = notes {
            eventNotes += "\nNotes: \(additionalNotes)"
        }
        
        return eventNotes
    }
    
    private func buildGroomingNotes(services: [String], groomer: String?) -> String {
        var notes = "Grooming Services:\n"
        
        if services.isEmpty {
            notes += "• General grooming"
        } else {
            for service in services {
                notes += "• \(service)\n"
            }
        }
        
        if let groomer = groomer {
            notes += "\nGroomer: \(groomer)"
        }
        
        return notes
    }
    
    private func generateMedicationDates(schedule: MedicationSchedule) -> [Date] {
        var dates: [Date] = []
        let calendar = Calendar.current
        
        switch schedule.frequency {
        case .daily:
            for day in 0..<30 { // Next 30 days
                if let date = calendar.date(byAdding: .day, value: day, to: schedule.startDate) {
                    for time in schedule.times {
                        if let scheduledDate = calendar.date(bySettingHour: time.hour, minute: time.minute, second: 0, of: date) {
                            dates.append(scheduledDate)
                        }
                    }
                }
            }
        case .weekly:
            for week in 0..<12 { // Next 12 weeks
                if let date = calendar.date(byAdding: .weekOfYear, value: week, to: schedule.startDate) {
                    for time in schedule.times {
                        if let scheduledDate = calendar.date(bySettingHour: time.hour, minute: time.minute, second: 0, of: date) {
                            dates.append(scheduledDate)
                        }
                    }
                }
            }
        case .monthly:
            for month in 0..<6 { // Next 6 months
                if let date = calendar.date(byAdding: .month, value: month, to: schedule.startDate) {
                    for time in schedule.times {
                        if let scheduledDate = calendar.date(bySettingHour: time.hour, minute: time.minute, second: 0, of: date) {
                            dates.append(scheduledDate)
                        }
                    }
                }
            }
        }
        
        return dates
    }
    
    private func createRecurrenceRule(for schedule: MedicationSchedule) -> EKRecurrenceRule? {
        let frequency: EKRecurrenceFrequency
        
        switch schedule.frequency {
        case .daily:
            frequency = .daily
        case .weekly:
            frequency = .weekly
        case .monthly:
            frequency = .monthly
        }
        
        return EKRecurrenceRule(
            recurrenceWith: frequency,
            interval: 1,
            end: nil
        )
    }
    
    // MARK: - Calendar Integration
    
    func exportToCalendar(_ petEvent: PetEvent) async throws {
        // Event is already in calendar, this could be used for sharing
        guard let ekEvent = eventStore.event(withIdentifier: petEvent.ekEventIdentifier) else {
            throw CalendarError.eventNotFound
        }
        
        // Could implement sharing functionality here
        print("Event exported: \(ekEvent.title ?? "Unknown")")
    }
    
    func importFromCalendar() async {
        // Import existing calendar events that might be pet-related
        let calendars = eventStore.calendars(for: .event)
        let startDate = Date()
        let endDate = Calendar.current.date(byAdding: .year, value: 1, to: startDate) ?? startDate
        
        let predicate = eventStore.predicateForEvents(
            withStart: startDate,
            end: endDate,
            calendars: calendars
        )
        
        let events = eventStore.events(matching: predicate)
        
        // Look for pet-related keywords in event titles
        let petKeywords = ["vet", "vaccination", "grooming", "pet", "dog", "cat"]
        
        for event in events {
            let title = event.title?.lowercased() ?? ""
            
            if petKeywords.contains(where: title.contains) {
                // Suggest importing this event
                print("Found potential pet event: \(event.title ?? "Unknown")")
            }
        }
    }
}

// MARK: - Data Models

struct PetEvent: Identifiable, Codable {
    let id: UUID
    let ekEventIdentifier: String
    let title: String
    let startDate: Date
    let endDate: Date
    let type: PetEventType
    let petId: UUID
    let location: String?
    let notes: String?
    let veterinarian: String?
    var isCompleted: Bool
    
    static func fromEKEvent(_ ekEvent: EKEvent) -> PetEvent? {
        guard let identifier = ekEvent.eventIdentifier else { return nil }
        
        return PetEvent(
            id: UUID(),
            ekEventIdentifier: identifier,
            title: ekEvent.title,
            startDate: ekEvent.startDate,
            endDate: ekEvent.endDate,
            type: .vetAppointment, // Default type
            petId: UUID(), // Would need to parse from notes or custom field
            location: ekEvent.location,
            notes: ekEvent.notes,
            veterinarian: nil,
            isCompleted: false
        )
    }
}

enum PetEventType: String, CaseIterable, Codable {
    case vetAppointment = "vet_appointment"
    case vaccination = "vaccination"
    case medicationReminder = "medication_reminder"
    case groomingAppointment = "grooming_appointment"
    case checkup = "checkup"
    case surgery = "surgery"
    case training = "training"
    case boarding = "boarding"
    
    var displayName: String {
        switch self {
        case .vetAppointment: return "Vet Appointment"
        case .vaccination: return "Vaccination"
        case .medicationReminder: return "Medication"
        case .groomingAppointment: return "Grooming"
        case .checkup: return "Checkup"
        case .surgery: return "Surgery"
        case .training: return "Training"
        case .boarding: return "Boarding"
        }
    }
    
    var icon: String {
        switch self {
        case .vetAppointment: return "stethoscope"
        case .vaccination: return "syringe"
        case .medicationReminder: return "pills"
        case .groomingAppointment: return "scissors"
        case .checkup: return "checkmark.circle"
        case .surgery: return "cross"
        case .training: return "figure.walk"
        case .boarding: return "house"
        }
    }
    
    var color: Color {
        switch self {
        case .vetAppointment: return .blue
        case .vaccination: return .green
        case .medicationReminder: return .orange
        case .groomingAppointment: return .purple
        case .checkup: return .teal
        case .surgery: return .red
        case .training: return .indigo
        case .boarding: return .brown
        }
    }
}

struct MedicationSchedule {
    let frequency: MedicationFrequency
    let times: [MedicationTime]
    let startDate: Date
    let endDate: Date?
}

enum MedicationFrequency {
    case daily
    case weekly
    case monthly
}

struct MedicationTime {
    let hour: Int
    let minute: Int
}

enum CalendarError: Error {
    case calendarNotAvailable
    case eventNotFound
    case failedToSaveEvent(Error)
    case failedToUpdateEvent(Error)
    case failedToDeleteEvent(Error)
}
