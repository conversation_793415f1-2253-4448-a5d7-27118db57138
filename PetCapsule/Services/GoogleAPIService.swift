//
//  GoogleAPIService.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 6/6/25.
//

import Foundation
import CoreLocation

class GoogleAPIService: ObservableObject {
    static let shared = GoogleAPIService()
    
    private let apiKey = APIKeys.getAPIKey(for: .googlePlaces)
    private let baseURL = APIEndpoints.googleMapsBase
    private let placesURL = APIEndpoints.googlePlacesBase

    // Service Account Configuration
    private let serviceAccount = APIKeys.googleServiceAccount
    
    private init() {}
    
    // MARK: - Places API
    
    func searchPetFriendlyPlaces(near location: CLLocationCoordinate2D, radius: Int = 5000) async throws -> [PetFriendlyLocation] {
        let url = "\(placesURL)/places:searchNearby"
        
        var request = URLRequest(url: URL(string: url)!)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue("places.id,places.displayName,places.formattedAddress,places.rating,places.userRatingCount,places.location,places.types,places.photos", forHTTPHeaderField: "X-Goog-FieldMask")
        
        let requestBody: [String: Any] = [
            "includedTypes": [
                "park",
                "veterinary_care",
                "pet_store",
                "restaurant",
                "cafe",
                "tourist_attraction"
            ],
            "maxResultCount": 20,
            "locationRestriction": [
                "circle": [
                    "center": [
                        "latitude": location.latitude,
                        "longitude": location.longitude
                    ],
                    "radius": radius
                ]
            ]
        ]
        
        request.httpBody = try JSONSerialization.data(withJSONObject: requestBody)
        
        // Add authentication header
        if let authToken = await getAccessToken() {
            request.setValue("Bearer \(authToken)", forHTTPHeaderField: "Authorization")
        }
        
        let (data, response) = try await URLSession.shared.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            print("❌ Places API Error: \(response)")
            return []
        }
        
        let placesResponse = try JSONDecoder().decode(GooglePlacesResponse.self, from: data)
        return placesResponse.places.compactMap { place in
            convertToLocationModel(place)
        }
    }
    
    func getPlaceDetails(placeId: String) async throws -> PetFriendlyLocation? {
        let url = "\(placesURL)/places/\(placeId)"
        
        var request = URLRequest(url: URL(string: url)!)
        request.setValue("places.id,places.displayName,places.formattedAddress,places.rating,places.userRatingCount,places.location,places.types,places.photos,places.reviews,places.openingHours", forHTTPHeaderField: "X-Goog-FieldMask")
        
        if let authToken = await getAccessToken() {
            request.setValue("Bearer \(authToken)", forHTTPHeaderField: "Authorization")
        }
        
        let (data, response) = try await URLSession.shared.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            return nil
        }
        
        let place = try JSONDecoder().decode(GooglePlace.self, from: data)
        return convertToLocationModel(place)
    }
    
    // MARK: - Geocoding API
    
    func getCurrentLocation() async throws -> CLLocationCoordinate2D {
        // For now, return a default location (San Francisco)
        // In a real app, you'd use CLLocationManager
        return CLLocationCoordinate2D(latitude: 37.7749, longitude: -122.4194)
    }
    
    func reverseGeocode(coordinate: CLLocationCoordinate2D) async throws -> String {
        let url = "\(baseURL)/geocode/json"
        var components = URLComponents(string: url)!
        components.queryItems = [
            URLQueryItem(name: "latlng", value: "\(coordinate.latitude),\(coordinate.longitude)"),
            URLQueryItem(name: "key", value: apiKey)
        ]
        
        let (data, _) = try await URLSession.shared.data(from: components.url!)
        let response = try JSONDecoder().decode(GoogleGeocodingResponse.self, from: data)
        
        return response.results.first?.formattedAddress ?? "Unknown Location"
    }
    
    // MARK: - Authentication
    
    private func getAccessToken() async -> String? {
        // For now, we'll use API key authentication
        // In production, implement proper service account JWT authentication
        return nil
    }
    
    // MARK: - Helper Methods
    
    private func convertToLocationModel(_ place: GooglePlace) -> PetFriendlyLocation {
        let locationType = determineLocationType(from: place.types)
        let distance = calculateDistance(to: place.location)
        
        return PetFriendlyLocation(
            name: place.displayName.text,
            type: locationType,
            rating: place.rating ?? 0.0,
            distance: distance,
            address: place.formattedAddress ?? "",
            imageURL: getPhotoURL(from: place.photos?.first),
            amenities: extractAmenities(from: place.types),
            isOpen: true // We'd need to check opening hours
        )
    }
    
    private func determineLocationType(from types: [String]) -> LocationType {
        if types.contains("park") {
            return .park
        } else if types.contains("veterinary_care") {
            return .veterinary
        } else if types.contains("pet_store") {
            return .store
        } else if types.contains("restaurant") || types.contains("cafe") {
            return .restaurant
        } else {
            return .park
        }
    }
    
    private func calculateDistance(to location: GoogleLocation) -> Double {
        // Calculate distance from current location
        // For now, return a random distance
        return Double.random(in: 0.1...5.0)
    }
    
    private func getPhotoURL(from photo: GooglePhoto?) -> String {
        guard let photo = photo else {
            return "https://via.placeholder.com/300x200"
        }
        
        return "\(placesURL)/places/\(photo.name)/media?maxHeightPx=400&maxWidthPx=600&key=\(apiKey)"
    }
    
    private func extractAmenities(from types: [String]) -> [String] {
        var amenities: [String] = []
        
        if types.contains("park") {
            amenities.append(contentsOf: ["Off-leash area", "Water fountains", "Waste bags"])
        }
        if types.contains("veterinary_care") {
            amenities.append(contentsOf: ["Emergency care", "Grooming", "Boarding"])
        }
        if types.contains("pet_store") {
            amenities.append(contentsOf: ["Pet supplies", "Grooming", "Training classes"])
        }
        if types.contains("restaurant") || types.contains("cafe") {
            amenities.append(contentsOf: ["Pet-friendly patio", "Dog treats", "Water bowls"])
        }
        
        return amenities
    }
}

// MARK: - Google API Models

struct GooglePlacesResponse: Codable {
    let places: [GooglePlace]
}

struct GooglePlace: Codable {
    let id: String
    let displayName: GoogleDisplayName
    let formattedAddress: String?
    let rating: Double?
    let userRatingCount: Int?
    let location: GoogleLocation
    let types: [String]
    let photos: [GooglePhoto]?
    
    enum CodingKeys: String, CodingKey {
        case id, displayName, formattedAddress, rating, userRatingCount, location, types, photos
    }
}

struct GoogleDisplayName: Codable {
    let text: String
}

struct GoogleLocation: Codable {
    let latitude: Double
    let longitude: Double
}

struct GooglePhoto: Codable {
    let name: String
    let widthPx: Int?
    let heightPx: Int?
}

struct GoogleGeocodingResponse: Codable {
    let results: [GoogleGeocodingResult]
    let status: String
}

struct GoogleGeocodingResult: Codable {
    let formattedAddress: String
    
    enum CodingKeys: String, CodingKey {
        case formattedAddress = "formatted_address"
    }
}
