//
//  PetPassKitService.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 12/24/24.
//

import Foundation
import PassKit
import SwiftUI
import Combine

@MainActor
class PetPassKitService: ObservableObject {
    static let shared = PetPassKitService()
    
    @Published var isPassKitAvailable = false
    @Published var petPasses: [PetPass] = []
    @Published var isGeneratingPass = false
    
    private let passLibrary = PKPassLibrary()
    
    private init() {
        checkPassKitAvailability()
        loadExistingPasses()
    }
    
    // MARK: - Setup
    
    private func checkPassKitAvailability() {
        isPassKitAvailable = PKPassLibrary.isPassLibraryAvailable()
    }
    
    private func loadExistingPasses() {
        let passes = passLibrary.passes()
        
        // Filter for PetCapsule passes
        petPasses = passes.compactMap { pass in
            if pass.passTypeIdentifier.contains("petcapsule") {
                return PetPass.fromPKPass(pass)
            }
            return nil
        }
    }
    
    // MARK: - Pet ID Pass Creation
    
    func createPetIDPass(for pet: Pet) async throws -> PetPass {
        guard isPassKitAvailable else {
            throw PassKitError.passKitNotAvailable
        }
        
        isGeneratingPass = true
        
        do {
            let passData = try await generatePetIDPassData(for: pet)
            let pass = try PKPass(data: passData)
            
            let petPass = PetPass(
                id: UUID(),
                petId: pet.id,
                passType: .petID,
                serialNumber: pass.serialNumber,
                passTypeIdentifier: pass.passTypeIdentifier,
                createdAt: Date(),
                isActive: true
            )
            
            // Add to Wallet
            try await addPassToWallet(pass)
            
            petPasses.append(petPass)
            isGeneratingPass = false
            
            return petPass
            
        } catch {
            isGeneratingPass = false
            throw error
        }
    }
    
    // MARK: - Vaccination Pass Creation
    
    func createVaccinationPass(for pet: Pet, vaccination: VaccinationRecord) async throws -> PetPass {
        guard isPassKitAvailable else {
            throw PassKitError.passKitNotAvailable
        }
        
        isGeneratingPass = true
        
        do {
            let passData = try await generateVaccinationPassData(for: pet, vaccination: vaccination)
            let pass = try PKPass(data: passData)
            
            let petPass = PetPass(
                id: UUID(),
                petId: pet.id,
                passType: .vaccination,
                serialNumber: pass.serialNumber,
                passTypeIdentifier: pass.passTypeIdentifier,
                createdAt: Date(),
                isActive: true
            )
            
            // Add to Wallet
            try await addPassToWallet(pass)
            
            petPasses.append(petPass)
            isGeneratingPass = false
            
            return petPass
            
        } catch {
            isGeneratingPass = false
            throw error
        }
    }
    
    // MARK: - Medical Record Pass
    
    func createMedicalRecordPass(for pet: Pet, records: [HealthRecord]) async throws -> PetPass {
        guard isPassKitAvailable else {
            throw PassKitError.passKitNotAvailable
        }
        
        isGeneratingPass = true
        
        do {
            let passData = try await generateMedicalRecordPassData(for: pet, records: records)
            let pass = try PKPass(data: passData)
            
            let petPass = PetPass(
                id: UUID(),
                petId: pet.id,
                passType: .medicalRecord,
                serialNumber: pass.serialNumber,
                passTypeIdentifier: pass.passTypeIdentifier,
                createdAt: Date(),
                isActive: true
            )
            
            // Add to Wallet
            try await addPassToWallet(pass)
            
            petPasses.append(petPass)
            isGeneratingPass = false
            
            return petPass
            
        } catch {
            isGeneratingPass = false
            throw error
        }
    }
    
    // MARK: - Pass Data Generation
    
    private func generatePetIDPassData(for pet: Pet) async throws -> Data {
        let passInfo = PetIDPassInfo(
            petName: pet.name,
            species: pet.species,
            breed: pet.breed,
            age: pet.age,
            weight: pet.weight ?? 0.0,
            color: pet.profileImageURL ?? "Unknown",
            gender: pet.gender ?? "Unknown",
            microchipId: pet.microchipId,
            ownerName: "Pet Owner", // Would get from user profile
            ownerPhone: "+****************", // Would get from user profile
            emergencyContact: "Emergency Vet: +****************",
            medicalNotes: pet.bio ?? "",
            photoURL: nil // Would include pet photo
        )
        
        return try await createPassData(
            passType: .petID,
            serialNumber: "PET-\(pet.id.uuidString.prefix(8))",
            passInfo: passInfo
        )
    }
    
    private func generateVaccinationPassData(for pet: Pet, vaccination: VaccinationRecord) async throws -> Data {
        let passInfo = VaccinationPassInfo(
            petName: pet.name,
            vaccineName: vaccination.vaccineName,
            dateAdministered: vaccination.completedDate ?? vaccination.dueDate,
            nextDueDate: vaccination.isCompleted ? nil : vaccination.dueDate,
            veterinarianName: vaccination.veterinarian ?? "Unknown",
            clinicName: vaccination.veterinarian ?? "Unknown Clinic",
            batchNumber: vaccination.notes ?? "",
            manufacturer: vaccination.notes ?? "",
            notes: vaccination.notes
        )
        
        return try await createPassData(
            passType: .vaccination,
            serialNumber: "VAX-\(vaccination.id.uuidString.prefix(8))",
            passInfo: passInfo
        )
    }
    
    private func generateMedicalRecordPassData(for pet: Pet, records: [HealthRecord]) async throws -> Data {
        let passInfo = MedicalRecordPassInfo(
            petName: pet.name,
            lastCheckup: records.first?.date ?? Date(),
            allergies: records.filter { $0.type == .allergy }.map { $0.notes ?? "Unknown" },
            medications: records.filter { $0.type == .medication }.map { $0.notes ?? "Unknown" },
            conditions: records.filter { $0.type == .condition }.map { $0.notes ?? "Unknown" },
            emergencyInstructions: "Contact emergency vet immediately if pet shows signs of distress"
        )
        
        return try await createPassData(
            passType: .medicalRecord,
            serialNumber: "MED-\(pet.id.uuidString.prefix(8))",
            passInfo: passInfo
        )
    }
    
    // MARK: - Pass Creation Helper
    
    private func createPassData(passType: PetPassType, serialNumber: String, passInfo: Any) async throws -> Data {
        // This would typically call your backend service to generate the .pkpass file
        // For now, we'll create a mock pass structure
        
        let passDict: [String: Any] = [
            "formatVersion": 1,
            "passTypeIdentifier": "pass.com.petcapsule.\(passType.rawValue)",
            "serialNumber": serialNumber,
            "teamIdentifier": "YOUR_TEAM_ID",
            "organizationName": "PetCapsule",
            "description": passType.displayName,
            "logoText": "PetCapsule",
            "foregroundColor": "rgb(255, 255, 255)",
            "backgroundColor": "rgb(0, 122, 255)",
            "labelColor": "rgb(255, 255, 255)",
            "generic": createPassFields(for: passType, passInfo: passInfo),
            "barcode": [
                "message": serialNumber,
                "format": "PKBarcodeFormatQR",
                "messageEncoding": "iso-8859-1"
            ]
        ]
        
        // Convert to JSON data
        return try JSONSerialization.data(withJSONObject: passDict, options: .prettyPrinted)
    }
    
    private func createPassFields(for passType: PetPassType, passInfo: Any) -> [String: Any] {
        switch passType {
        case .petID:
            return createPetIDFields(passInfo as! PetIDPassInfo)
        case .vaccination:
            return createVaccinationFields(passInfo as! VaccinationPassInfo)
        case .medicalRecord:
            return createMedicalRecordFields(passInfo as! MedicalRecordPassInfo)
        }
    }
    
    private func createPetIDFields(_ info: PetIDPassInfo) -> [String: Any] {
        return [
            "primaryFields": [
                [
                    "key": "petName",
                    "label": "Pet Name",
                    "value": info.petName
                ]
            ],
            "secondaryFields": [
                [
                    "key": "species",
                    "label": "Species",
                    "value": info.species
                ],
                [
                    "key": "breed",
                    "label": "Breed",
                    "value": info.breed
                ]
            ],
            "auxiliaryFields": [
                [
                    "key": "age",
                    "label": "Age",
                    "value": "\(info.age) years"
                ],
                [
                    "key": "weight",
                    "label": "Weight",
                    "value": "\(info.weight) lbs"
                ]
            ],
            "backFields": [
                [
                    "key": "owner",
                    "label": "Owner",
                    "value": info.ownerName
                ],
                [
                    "key": "phone",
                    "label": "Phone",
                    "value": info.ownerPhone
                ],
                [
                    "key": "emergency",
                    "label": "Emergency Contact",
                    "value": info.emergencyContact
                ],
                [
                    "key": "microchip",
                    "label": "Microchip ID",
                    "value": info.microchipId ?? "Not Available"
                ]
            ]
        ]
    }
    
    private func createVaccinationFields(_ info: VaccinationPassInfo) -> [String: Any] {
        let dateFormatter = DateFormatter()
        dateFormatter.dateStyle = .medium
        
        return [
            "primaryFields": [
                [
                    "key": "vaccine",
                    "label": "Vaccine",
                    "value": info.vaccineName
                ]
            ],
            "secondaryFields": [
                [
                    "key": "pet",
                    "label": "Pet",
                    "value": info.petName
                ],
                [
                    "key": "date",
                    "label": "Date Given",
                    "value": dateFormatter.string(from: info.dateAdministered)
                ]
            ],
            "auxiliaryFields": [
                [
                    "key": "nextDue",
                    "label": "Next Due",
                    "value": info.nextDueDate.map { dateFormatter.string(from: $0) } ?? "N/A"
                ]
            ],
            "backFields": [
                [
                    "key": "veterinarian",
                    "label": "Veterinarian",
                    "value": info.veterinarianName
                ],
                [
                    "key": "clinic",
                    "label": "Clinic",
                    "value": info.clinicName
                ],
                [
                    "key": "batch",
                    "label": "Batch Number",
                    "value": info.batchNumber ?? "Unknown"
                ]
            ]
        ]
    }
    
    private func createMedicalRecordFields(_ info: MedicalRecordPassInfo) -> [String: Any] {
        let dateFormatter = DateFormatter()
        dateFormatter.dateStyle = .medium
        
        return [
            "primaryFields": [
                [
                    "key": "petName",
                    "label": "Pet",
                    "value": info.petName
                ]
            ],
            "secondaryFields": [
                [
                    "key": "lastCheckup",
                    "label": "Last Checkup",
                    "value": dateFormatter.string(from: info.lastCheckup)
                ]
            ],
            "backFields": [
                [
                    "key": "allergies",
                    "label": "Allergies",
                    "value": info.allergies.isEmpty ? "None known" : info.allergies.joined(separator: ", ")
                ],
                [
                    "key": "medications",
                    "label": "Current Medications",
                    "value": info.medications.isEmpty ? "None" : info.medications.joined(separator: ", ")
                ],
                [
                    "key": "conditions",
                    "label": "Medical Conditions",
                    "value": info.conditions.isEmpty ? "None known" : info.conditions.joined(separator: ", ")
                ],
                [
                    "key": "emergency",
                    "label": "Emergency Instructions",
                    "value": info.emergencyInstructions
                ]
            ]
        ]
    }
    
    // MARK: - Wallet Integration
    
    private func addPassToWallet(_ pass: PKPass) async throws {
        return try await withCheckedThrowingContinuation { continuation in
            let addPassViewController = PKAddPassesViewController(pass: pass)
            
            if let addPassViewController = addPassViewController {
                // Present the add pass view controller
                // This would typically be presented from a view controller
                continuation.resume()
            } else {
                continuation.resume(throwing: PassKitError.failedToCreatePass)
            }
        }
    }
    
    // MARK: - Pass Management
    
    func updatePass(_ petPass: PetPass, with newData: Data) async throws {
        // Remove old pass
        if let existingPass = passLibrary.pass(withPassTypeIdentifier: petPass.passTypeIdentifier, serialNumber: petPass.serialNumber) {
            passLibrary.removePass(existingPass)
        }
        
        // Add updated pass
        let updatedPass = try PKPass(data: newData)
        try await addPassToWallet(updatedPass)
        
        // Update local record
        if let index = petPasses.firstIndex(where: { $0.id == petPass.id }) {
            petPasses[index].updatedAt = Date()
        }
    }
    
    func removePass(_ petPass: PetPass) {
        if let pass = passLibrary.pass(withPassTypeIdentifier: petPass.passTypeIdentifier, serialNumber: petPass.serialNumber) {
            passLibrary.removePass(pass)
        }
        
        petPasses.removeAll { $0.id == petPass.id }
    }
    
    func refreshPasses() {
        loadExistingPasses()
    }
    
    // MARK: - Pass Sharing
    
    func sharePass(_ petPass: PetPass) -> URL? {
        guard let pass = passLibrary.pass(withPassTypeIdentifier: petPass.passTypeIdentifier, serialNumber: petPass.serialNumber) else {
            return nil
        }
        
        // Create temporary file for sharing
        let tempURL = FileManager.default.temporaryDirectory.appendingPathComponent("\(petPass.serialNumber).pkpass")
        
        do {
            // Note: PassKit writing is not available in simulator
            print("Would write pass to: \(tempURL)")
            return tempURL
        } catch {
            print("Failed to create shareable pass file: \(error)")
            return nil
        }
    }
}

// MARK: - Data Models

struct PetPass: Identifiable, Codable {
    let id: UUID
    let petId: UUID
    let passType: PetPassType
    let serialNumber: String
    let passTypeIdentifier: String
    let createdAt: Date
    var updatedAt: Date?
    let isActive: Bool
    
    static func fromPKPass(_ pkPass: PKPass) -> PetPass? {
        // Extract pet pass information from PKPass
        return PetPass(
            id: UUID(),
            petId: UUID(), // Would need to extract from pass data
            passType: .petID, // Would need to determine from pass type
            serialNumber: pkPass.serialNumber,
            passTypeIdentifier: pkPass.passTypeIdentifier,
            createdAt: Date(),
            isActive: true
        )
    }
}

enum PetPassType: String, CaseIterable, Codable {
    case petID = "petid"
    case vaccination = "vaccination"
    case medicalRecord = "medical"
    
    var displayName: String {
        switch self {
        case .petID: return "Pet ID Card"
        case .vaccination: return "Vaccination Record"
        case .medicalRecord: return "Medical Record"
        }
    }
    
    var icon: String {
        switch self {
        case .petID: return "person.crop.rectangle"
        case .vaccination: return "syringe"
        case .medicalRecord: return "doc.text"
        }
    }
}

// Pass Info Structures
struct PetIDPassInfo {
    let petName: String
    let species: String
    let breed: String
    let age: Int
    let weight: Double
    let color: String
    let gender: String
    let microchipId: String?
    let ownerName: String
    let ownerPhone: String
    let emergencyContact: String
    let medicalNotes: String
    let photoURL: String?
}

struct VaccinationPassInfo {
    let petName: String
    let vaccineName: String
    let dateAdministered: Date
    let nextDueDate: Date?
    let veterinarianName: String
    let clinicName: String
    let batchNumber: String?
    let manufacturer: String?
    let notes: String?
}

struct MedicalRecordPassInfo {
    let petName: String
    let lastCheckup: Date
    let allergies: [String]
    let medications: [String]
    let conditions: [String]
    let emergencyInstructions: String
}

enum PassKitError: Error {
    case passKitNotAvailable
    case failedToCreatePass
    case failedToAddToWallet
    case passNotFound
}

// MARK: - SwiftUI Integration

struct AddToWalletButton: View {
    let pet: Pet
    let passType: PetPassType
    @StateObject private var passKitService = PetPassKitService.shared
    @State private var showingError = false
    @State private var errorMessage = ""
    
    var body: some View {
        Button(action: {
            Task {
                do {
                    switch passType {
                    case .petID:
                        _ = try await passKitService.createPetIDPass(for: pet)
                    case .vaccination:
                        // Would need vaccination record
                        break
                    case .medicalRecord:
                        // Would need health records
                        break
                    }
                } catch {
                    errorMessage = error.localizedDescription
                    showingError = true
                }
            }
        }) {
            HStack {
                Image(systemName: "wallet.pass")
                Text("Add to Wallet")
            }
        }
        .disabled(passKitService.isGeneratingPass || !passKitService.isPassKitAvailable)
        .alert("Error", isPresented: $showingError) {
            Button("OK") { }
        } message: {
            Text(errorMessage)
        }
    }
}
