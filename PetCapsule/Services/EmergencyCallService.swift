//
//  EmergencyCallService.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 12/24/24.
//

import Foundation
import CallKit
import AVFoundation
import Contacts
import SwiftUI

@MainActor
class EmergencyCallService: NSObject, ObservableObject {
    static let shared = EmergencyCallService()
    
    @Published var isCallActive = false
    @Published var currentCall: EmergencyCall?
    @Published var callHistory: [EmergencyCall] = []
    @Published var emergencyContacts: [EmergencyContact] = []
    
    private let callController = CXCallController()
    private let provider: CXProvider
    private nonisolated(unsafe) var audioSession: AVAudioSession?
    
    override init() {
        // Configure CallKit provider
        let providerConfiguration = CXProviderConfiguration()
        // Note: localizedName is read-only, set via bundle display name
        providerConfiguration.maximumCallGroups = 1
        providerConfiguration.maximumCallsPerCallGroup = 1
        providerConfiguration.supportedHandleTypes = [.phoneNumber]
        providerConfiguration.supportsVideo = false
        
        // Set app icon for CallKit UI
        if let iconImage = UIImage(named: "AppIcon") {
            providerConfiguration.iconTemplateImageData = iconImage.pngData()
        }
        
        provider = CXProvider(configuration: providerConfiguration)
        
        super.init()
        
        provider.setDelegate(self, queue: nil)
        setupEmergencyContacts()
        setupAudioSession()
    }
    
    // MARK: - Setup
    
    private func setupEmergencyContacts() {
        emergencyContacts = [
            EmergencyContact(
                id: UUID(),
                name: "24/7 Emergency Vet",
                phoneNumber: "+15551234567",
                type: .emergency,
                isDefault: true,
                address: "123 Emergency Ave, City, State",
                notes: "24-hour emergency veterinary clinic"
            ),
            EmergencyContact(
                id: UUID(),
                name: "Pet Poison Control",
                phoneNumber: "+18884264435",
                type: .poison,
                isDefault: true,
                address: "National Hotline",
                notes: "ASPCA Animal Poison Control Center"
            ),
            EmergencyContact(
                id: UUID(),
                name: "Primary Veterinarian",
                phoneNumber: "+15559876543",
                type: .veterinarian,
                isDefault: false,
                address: "456 Main St, City, State",
                notes: "Dr. Smith - Regular vet"
            )
        ]
    }
    
    private func setupAudioSession() {
        audioSession = AVAudioSession.sharedInstance()
        
        do {
            try audioSession?.setCategory(.playAndRecord, mode: .voiceChat, options: [])
        } catch {
            print("Failed to setup audio session: \(error)")
        }
    }
    
    // MARK: - Emergency Calling
    
    func initiateEmergencyCall(to contact: EmergencyContact, for pet: Pet?, emergencyType: EmergencyType) {
        let call = EmergencyCall(
            id: UUID(),
            contact: contact,
            pet: pet,
            emergencyType: emergencyType,
            startTime: Date(),
            status: .initiating
        )
        
        currentCall = call
        
        // Create CallKit call
        let handle = CXHandle(type: .phoneNumber, value: contact.phoneNumber)
        let startCallAction = CXStartCallAction(call: call.id, handle: handle)
        
        startCallAction.contactIdentifier = contact.name
        startCallAction.isVideo = false
        
        let transaction = CXTransaction(action: startCallAction)
        
        callController.request(transaction) { [weak self] error in
            DispatchQueue.main.async {
                if let error = error {
                    print("Failed to start call: \(error)")
                    self?.currentCall?.status = .failed
                } else {
                    self?.currentCall?.status = .connecting
                    Task {
                        await self?.logEmergencyCall(call)
                    }
                }
            }
        }
    }
    
    func endCurrentCall() {
        guard let call = currentCall else { return }
        
        let endCallAction = CXEndCallAction(call: call.id)
        let transaction = CXTransaction(action: endCallAction)
        
        callController.request(transaction) { [weak self] error in
            DispatchQueue.main.async {
                if let error = error {
                    print("Failed to end call: \(error)")
                } else {
                    self?.currentCall?.endTime = Date()
                    self?.currentCall?.status = .ended
                    self?.isCallActive = false
                    
                    if let completedCall = self?.currentCall {
                        self?.callHistory.append(completedCall)
                    }
                    
                    self?.currentCall = nil
                }
            }
        }
    }
    
    // MARK: - Contact Management
    
    func addEmergencyContact(_ contact: EmergencyContact) {
        emergencyContacts.append(contact)
        saveEmergencyContacts()
    }
    
    func removeEmergencyContact(_ contact: EmergencyContact) {
        emergencyContacts.removeAll { $0.id == contact.id }
        saveEmergencyContacts()
    }
    
    func updateEmergencyContact(_ contact: EmergencyContact) {
        if let index = emergencyContacts.firstIndex(where: { $0.id == contact.id }) {
            emergencyContacts[index] = contact
            saveEmergencyContacts()
        }
    }
    
    func getDefaultContact(for type: EmergencyContactType) -> EmergencyContact? {
        return emergencyContacts.first { $0.type == type && $0.isDefault }
    }
    
    // MARK: - Quick Actions
    
    func callEmergencyVet(for pet: Pet?) {
        guard let contact = getDefaultContact(for: .emergency) else {
            print("No default emergency vet contact found")
            return
        }
        
        initiateEmergencyCall(to: contact, for: pet, emergencyType: .general)
    }
    
    func callPoisonControl(for pet: Pet?) {
        guard let contact = getDefaultContact(for: .poison) else {
            print("No poison control contact found")
            return
        }
        
        initiateEmergencyCall(to: contact, for: pet, emergencyType: .poisoning)
    }
    
    func callPrimaryVet(for pet: Pet?) {
        guard let contact = getDefaultContact(for: .veterinarian) else {
            print("No primary vet contact found")
            return
        }
        
        initiateEmergencyCall(to: contact, for: pet, emergencyType: .general)
    }
    
    // MARK: - Call History
    
    private func logEmergencyCall(_ call: EmergencyCall) async {
        // Log call for analytics and history
        print("Emergency call initiated: \(call.contact.name) for \(call.emergencyType.rawValue)")
        
        // Send analytics event
        await AnalyticsService.shared.trackEvent("emergency_call_initiated", parameters: [
            "contact_type": call.contact.type.rawValue,
            "emergency_type": call.emergencyType.rawValue,
            "has_pet": call.pet != nil
        ])
    }
    
    // MARK: - Persistence
    
    private func saveEmergencyContacts() {
        // Save to UserDefaults or Core Data
        if let data = try? JSONEncoder().encode(emergencyContacts) {
            UserDefaults.standard.set(data, forKey: "emergency_contacts")
        }
    }
    
    private func loadEmergencyContacts() {
        if let data = UserDefaults.standard.data(forKey: "emergency_contacts"),
           let contacts = try? JSONDecoder().decode([EmergencyContact].self, from: data) {
            emergencyContacts = contacts
        }
    }
}

// MARK: - CXProviderDelegate

extension EmergencyCallService: CXProviderDelegate {

    nonisolated func providerDidReset(_ provider: CXProvider) {
        // Handle provider reset
        Task { @MainActor in
            currentCall = nil
            isCallActive = false
        }
    }
    
    nonisolated func provider(_ provider: CXProvider, perform action: CXStartCallAction) {
        // Configure audio session
        do {
            try audioSession?.setActive(true)
        } catch {
            print("Failed to activate audio session: \(error)")
        }
        
        // Report call as connected
        provider.reportOutgoingCall(with: action.callUUID, startedConnectingAt: Date())
        provider.reportOutgoingCall(with: action.callUUID, connectedAt: Date())
        
        DispatchQueue.main.async {
            self.isCallActive = true
            self.currentCall?.status = .connected
        }
        
        action.fulfill()
    }
    
    nonisolated func provider(_ provider: CXProvider, perform action: CXEndCallAction) {
        // Deactivate audio session
        do {
            try audioSession?.setActive(false)
        } catch {
            print("Failed to deactivate audio session: \(error)")
        }
        
        DispatchQueue.main.async {
            self.isCallActive = false
            self.currentCall?.status = .ended
            self.currentCall?.endTime = Date()
            
            if let call = self.currentCall {
                self.callHistory.append(call)
            }
            
            self.currentCall = nil
        }
        
        action.fulfill()
    }
    
    nonisolated func provider(_ provider: CXProvider, perform action: CXSetHeldCallAction) {
        action.fulfill()
    }

    nonisolated func provider(_ provider: CXProvider, perform action: CXSetMutedCallAction) {
        action.fulfill()
    }
}

// MARK: - Data Models

struct EmergencyContact: Identifiable, Codable {
    let id: UUID
    let name: String
    let phoneNumber: String
    let type: EmergencyContactType
    let isDefault: Bool
    let address: String?
    let notes: String?
    
    var formattedPhoneNumber: String {
        // Format phone number for display
        let digits = phoneNumber.components(separatedBy: CharacterSet.decimalDigits.inverted).joined()
        
        if digits.count == 10 {
            let areaCode = String(digits.prefix(3))
            let exchange = String(digits.dropFirst(3).prefix(3))
            let number = String(digits.suffix(4))
            return "(\(areaCode)) \(exchange)-\(number)"
        }
        
        return phoneNumber
    }
}

enum EmergencyContactType: String, CaseIterable, Codable {
    case emergency = "emergency"
    case poison = "poison"
    case veterinarian = "veterinarian"
    case specialist = "specialist"
    
    var displayName: String {
        switch self {
        case .emergency: return "Emergency Vet"
        case .poison: return "Poison Control"
        case .veterinarian: return "Primary Vet"
        case .specialist: return "Specialist"
        }
    }
    
    var icon: String {
        switch self {
        case .emergency: return "cross.fill"
        case .poison: return "exclamationmark.triangle.fill"
        case .veterinarian: return "stethoscope"
        case .specialist: return "person.badge.plus"
        }
    }
    
    var color: Color {
        switch self {
        case .emergency: return .red
        case .poison: return .orange
        case .veterinarian: return .blue
        case .specialist: return .purple
        }
    }
}

struct EmergencyCall: Identifiable {
    let id: UUID
    let contact: EmergencyContact
    let pet: Pet?
    let emergencyType: EmergencyType
    let startTime: Date
    var endTime: Date?
    var status: CallStatus
    
    var duration: TimeInterval? {
        guard let endTime = endTime else { return nil }
        return endTime.timeIntervalSince(startTime)
    }
    
    var formattedDuration: String? {
        guard let duration = duration else { return nil }
        
        let minutes = Int(duration / 60)
        let seconds = Int(duration.truncatingRemainder(dividingBy: 60))
        
        if minutes > 0 {
            return "\(minutes)m \(seconds)s"
        } else {
            return "\(seconds)s"
        }
    }
}

enum CallStatus {
    case initiating
    case connecting
    case connected
    case ended
    case failed
    
    var displayName: String {
        switch self {
        case .initiating: return "Initiating"
        case .connecting: return "Connecting"
        case .connected: return "Connected"
        case .ended: return "Ended"
        case .failed: return "Failed"
        }
    }
}

// MARK: - Emergency Types
enum EmergencyType: String, CaseIterable, Codable {
    case general = "general"
    case injury = "injury"
    case poisoning = "poisoning"
    case breathing = "breathing"
    case seizure = "seizure"
    case trauma = "trauma"

    var displayName: String {
        switch self {
        case .general: return "General Emergency"
        case .injury: return "Injury"
        case .poisoning: return "Poisoning"
        case .breathing: return "Breathing Issues"
        case .seizure: return "Seizure"
        case .trauma: return "Trauma"
        }
    }

    var urgencyLevel: Int {
        switch self {
        case .trauma, .breathing, .seizure: return 3 // Critical
        case .poisoning, .injury: return 2 // High
        case .general: return 1 // Medium
        }
    }

    var icon: String {
        switch self {
        case .general: return "exclamationmark.triangle.fill"
        case .injury: return "bandage.fill"
        case .poisoning: return "drop.fill"
        case .breathing: return "lungs.fill"
        case .seizure: return "brain.head.profile"
        case .trauma: return "cross.case.fill"
        }
    }

    var color: Color {
        switch self {
        case .general: return .red
        case .injury: return .orange
        case .poisoning: return .yellow
        case .breathing: return .blue
        case .seizure: return .pink
        case .trauma: return .red
        }
    }
}
