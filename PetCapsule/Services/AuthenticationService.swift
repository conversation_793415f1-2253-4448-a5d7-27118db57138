//
//  AuthenticationService.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import Foundation
import SwiftUI
import Supabase
import AuthenticationServices
import CryptoKit
import LocalAuthentication

/// Comprehensive authentication service following Apple's security best practices
@MainActor
class AuthenticationService: NSObject, ObservableObject {

    // MARK: - Published Properties

    @Published var isAuthenticated = false
    @Published var currentUser: User?
    @Published var isLoading = false
    @Published var errorMessage: String?
    @Published var authenticationState: AuthenticationState = .unauthenticated

    // Development mode flag
    private var isDevelopmentMode = false

    // MARK: - Private Properties

    private let supabaseService = SupabaseService.shared
    private var supabase: SupabaseClient {
        return supabaseService.client
    }

    private let keychain = KeychainService()
    private let biometrics = BiometricAuthenticationService()
    private var currentNonce: String?

    // MARK: - Authentication State

    enum AuthenticationState: Equatable {
        case unauthenticated
        case authenticating
        case authenticated
        case biometricRequired
        case error(String)
    }

    // MARK: - Initialization

    override init() {
        super.init()
        Task {
            await checkAuthenticationStatus()
        }
    }

    // MARK: - Authentication Status

    func checkAuthenticationStatus() async {
        isLoading = true

        // First check if we have stored development mode
        if UserDefaults.standard.bool(forKey: "isDevelopmentMode") {
            print("🚀 Restoring development mode session")
            await MainActor.run {
                isDevelopmentMode = true
                isAuthenticated = true
                authenticationState = .authenticated
                currentUser = createDevelopmentUser()
                isLoading = false
            }
            return
        }

        // Skip Supabase authentication check if in development mode
        if isDevelopmentMode {
            print("🚀 Development mode active - maintaining session")
            isLoading = false
            return
        }

        do {
            // Check if user has valid session
            let session = try await supabase.auth.session

            let user = session.user
            await handleSuccessfulAuthentication(user: user)
        } catch {
            print("❌ Auth check failed: \(error)")
            authenticationState = .unauthenticated
        }

        isLoading = false
    }

    private func checkStoredCredentials() async {
        // Check if user has biometric authentication enabled
        if await biometrics.isBiometricAuthenticationEnabled() {
            authenticationState = .biometricRequired
        } else {
            authenticationState = .unauthenticated
        }
    }

    // MARK: - Apple Sign In

    func signInWithApple() async {
        isLoading = true
        authenticationState = .authenticating

        let request = ASAuthorizationAppleIDProvider().createRequest()
        request.requestedScopes = [.fullName, .email]

        // Generate nonce for security
        let nonce = randomNonceString()
        currentNonce = nonce
        request.nonce = sha256(nonce)

        let authorizationController = ASAuthorizationController(authorizationRequests: [request])
        authorizationController.delegate = self
        authorizationController.presentationContextProvider = self
        authorizationController.performRequests()
    }

    // MARK: - Email Authentication

    func signInWithEmail(_ email: String, password: String) async {
        isLoading = true
        authenticationState = .authenticating
        errorMessage = nil

        do {
            let session = try await supabase.auth.signIn(
                email: email,
                password: password
            )

            await handleSuccessfulAuthentication(user: session.user)

        } catch {
            await handleAuthenticationError(error)
        }
    }

    func signUpWithEmail(_ email: String, password: String, fullName: String) async {
        isLoading = true
        authenticationState = .authenticating
        errorMessage = nil

        do {
            let session = try await supabase.auth.signUp(
                email: email,
                password: password,
                data: ["full_name": .string(fullName)]
            )

            let user = session.user
            await handleSuccessfulAuthentication(user: user)

        } catch {
            await handleAuthenticationError(error)
        }
    }

    // MARK: - Biometric Authentication

    func authenticateWithBiometrics() async {
        isLoading = true

        do {
            let success = await biometrics.authenticateUser()

            if success {
                // Retrieve stored session from keychain
                if let sessionData = keychain.retrieveSessionData() {
                    try await supabase.auth.setSession(accessToken: sessionData.accessToken, refreshToken: sessionData.refreshToken)
                    let session = try await supabase.auth.session
                    await handleSuccessfulAuthentication(user: session.user)
                } else {
                    authenticationState = .unauthenticated
                }
            } else {
                authenticationState = .biometricRequired
            }
        } catch {
            await handleAuthenticationError(error)
        }

        isLoading = false
    }

    // MARK: - Development Mode

    @MainActor
    func enableDevelopmentMode(with user: User) {
        isDevelopmentMode = true
        currentUser = user
        isAuthenticated = true
        authenticationState = .authenticated
        isLoading = false

        // Persist development mode state
        UserDefaults.standard.set(true, forKey: "isDevelopmentMode")
        UserDefaults.standard.set(user.id, forKey: "developmentUserId")
        UserDefaults.standard.set(user.email, forKey: "developmentUserEmail")
        UserDefaults.standard.set(user.displayName, forKey: "developmentUserName")

        print("🚀 Development mode enabled with user: \(user.displayName)")
    }

    func disableDevelopmentMode() {
        isDevelopmentMode = false
        currentUser = nil
        isAuthenticated = false
        authenticationState = .unauthenticated

        // Clear persisted development mode state
        UserDefaults.standard.removeObject(forKey: "isDevelopmentMode")
        UserDefaults.standard.removeObject(forKey: "developmentUserId")
        UserDefaults.standard.removeObject(forKey: "developmentUserEmail")
        UserDefaults.standard.removeObject(forKey: "developmentUserName")

        print("🚀 Development mode disabled")
    }

    private func createDevelopmentUser() -> User {
        let userId = UserDefaults.standard.string(forKey: "developmentUserId") ?? "dev-user-123"
        let email = UserDefaults.standard.string(forKey: "developmentUserEmail") ?? "<EMAIL>"
        let name = UserDefaults.standard.string(forKey: "developmentUserName") ?? "Development User"

        return User(
            id: userId,
            email: email,
            displayName: name,
            subscriptionTier: .free
        )
    }

    // MARK: - Sign Out

    func signOut() async {
        isLoading = true

        // Handle development mode sign out
        if isDevelopmentMode {
            await MainActor.run {
                disableDevelopmentMode()
            }
            isLoading = false
            return
        }

        do {
            try await supabase.auth.signOut()

            // Clear stored credentials
            keychain.clearStoredCredentials()

            // Reset state
            isAuthenticated = false
            currentUser = nil
            authenticationState = .unauthenticated

        } catch {
            print("❌ Sign out error: \(error)")
        }

        isLoading = false
    }

    // MARK: - Password Reset

    func resetPassword(email: String) async {
        isLoading = true
        errorMessage = nil

        do {
            try await supabase.auth.resetPasswordForEmail(email)
            errorMessage = "Password reset email sent. Please check your inbox."
        } catch {
            await handleAuthenticationError(error)
        }

        isLoading = false
    }

    // MARK: - Private Helper Methods

    private func handleSuccessfulAuthentication(user: Supabase.User) async {
        // Create or update user profile
        await createOrUpdateUserProfile(user: user)

        // Store session securely
        if let session = try? await supabase.auth.session {
            keychain.storeSessionData(
                accessToken: session.accessToken,
                refreshToken: session.refreshToken,
                userId: user.id.uuidString
            )
        }

        // Update state
        isAuthenticated = true
        authenticationState = .authenticated
        isLoading = false

        // Trigger SupabaseService to load the current user
        Task {
            await supabaseService.loadCurrentUser()
        }

        print("✅ Authentication successful for user: \(user.email ?? "Unknown")")
    }

    private func createOrUpdateUserProfile(user: Supabase.User) async {
        do {
            // Check if user profile exists
            let existingUsers: [DatabaseUser] = try await supabase
                .from("users")
                .select()
                .eq("id", value: user.id.uuidString)
                .execute()
                .value

            if existingUsers.isEmpty {
                // Create new user profile
                let newUser = DatabaseUser(
                    id: user.id,
                    email: user.email ?? "",
                    fullName: user.userMetadata["full_name"]?.stringValue ?? "",
                    subscriptionTier: "free",
                    onboardingCompleted: false
                )

                try await supabase
                    .from("users")
                    .insert(newUser)
                    .execute()

                // Create app user model
                currentUser = User(
                    id: user.id.uuidString,
                    email: user.email ?? "",
                    displayName: user.userMetadata["full_name"]?.stringValue ?? "",
                    subscriptionTier: .free
                )

                print("✅ New user profile created")
            } else {
                // Update existing user
                let dbUser = existingUsers.first!
                currentUser = User(
                    id: dbUser.id.uuidString,
                    email: dbUser.email,
                    displayName: dbUser.fullName ?? "",
                    subscriptionTier: SubscriptionTier(rawValue: dbUser.subscriptionTier ?? "free") ?? .free
                )

                print("✅ Existing user profile loaded")
            }

        } catch {
            print("❌ Error creating/updating user profile: \(error)")
        }
    }

    private func handleAuthenticationError(_ error: Error) async {
        isLoading = false
        authenticationState = .error(error.localizedDescription)
        errorMessage = error.localizedDescription
        print("❌ Authentication error: \(error)")
    }

    // MARK: - Security Utilities

    private func randomNonceString(length: Int = 32) -> String {
        precondition(length > 0)
        let charset: [Character] = Array("0123456789ABCDEFGHIJKLMNOPQRSTUVXYZabcdefghijklmnopqrstuvwxyz-._")
        var result = ""
        var remainingLength = length

        while remainingLength > 0 {
            let randoms: [UInt8] = (0 ..< 16).map { _ in
                var random: UInt8 = 0
                let errorCode = SecRandomCopyBytes(kSecRandomDefault, 1, &random)
                if errorCode != errSecSuccess {
                    fatalError("Unable to generate nonce. SecRandomCopyBytes failed with OSStatus \(errorCode)")
                }
                return random
            }

            randoms.forEach { random in
                if remainingLength == 0 {
                    return
                }

                if random < charset.count {
                    result.append(charset[Int(random)])
                    remainingLength -= 1
                }
            }
        }

        return result
    }

    private func sha256(_ input: String) -> String {
        let inputData = Data(input.utf8)
        let hashedData = SHA256.hash(data: inputData)
        let hashString = hashedData.compactMap {
            String(format: "%02x", $0)
        }.joined()

        return hashString
    }
}

// MARK: - ASAuthorizationControllerDelegate

extension AuthenticationService: ASAuthorizationControllerDelegate {

    func authorizationController(controller: ASAuthorizationController, didCompleteWithAuthorization authorization: ASAuthorization) {
        Task {
            if let appleIDCredential = authorization.credential as? ASAuthorizationAppleIDCredential {
                guard let nonce = currentNonce else {
                    await handleAuthenticationError(NSError(domain: "AuthError", code: -1, userInfo: [NSLocalizedDescriptionKey: "Invalid state: A login callback was received, but no login request was sent."]))
                    return
                }

                guard let appleIDToken = appleIDCredential.identityToken else {
                    await handleAuthenticationError(NSError(domain: "AuthError", code: -1, userInfo: [NSLocalizedDescriptionKey: "Unable to fetch identity token"]))
                    return
                }

                guard let idTokenString = String(data: appleIDToken, encoding: .utf8) else {
                    await handleAuthenticationError(NSError(domain: "AuthError", code: -1, userInfo: [NSLocalizedDescriptionKey: "Unable to serialize token string from data"]))
                    return
                }

                do {
                    let session = try await supabase.auth.signInWithIdToken(
                        credentials: .init(
                            provider: .apple,
                            idToken: idTokenString,
                            nonce: nonce
                        )
                    )

                    await handleSuccessfulAuthentication(user: session.user)

                } catch {
                    await handleAuthenticationError(error)
                }
            }
        }
    }

    func authorizationController(controller: ASAuthorizationController, didCompleteWithError error: Error) {
        Task {
            await handleAuthenticationError(error)
        }
    }
}

// MARK: - ASAuthorizationControllerPresentationContextProviding

extension AuthenticationService: ASAuthorizationControllerPresentationContextProviding {

    func presentationAnchor(for controller: ASAuthorizationController) -> ASPresentationAnchor {
        guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
              let window = windowScene.windows.first else {
            fatalError("No window available")
        }
        return window
    }
}
