//
//  EmergencyContactsService.swift
//  PetCapsule
//
//  Service for managing emergency contacts
//

import Foundation
import SwiftUI

@MainActor
class EmergencyContactsService: ObservableObject {
    static let shared = EmergencyContactsService()
    
    @Published var contacts: [EmergencyContact] = []
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    private let userDefaults = UserDefaults.standard
    private let contactsKey = "emergency_contacts"
    
    private init() {
        loadContacts()
    }
    
    // MARK: - Contact Management
    
    func addContact(name: String, phoneNumber: String, type: EmergencyContactType, notes: String) async {
        let contact = EmergencyContact(
            id: UUID(),
            name: name,
            phoneNumber: phoneNumber,
            type: type,
            isDefault: false,
            address: nil,
            notes: notes
        )
        
        contacts.append(contact)
        saveContacts()
    }
    
    func deleteContact(_ contact: EmergencyContact) {
        contacts.removeAll { $0.id == contact.id }
        saveContacts()
    }
    
    func updateContact(_ contact: EmergencyContact) {
        if let index = contacts.firstIndex(where: { $0.id == contact.id }) {
            contacts[index] = contact
            saveContacts()
        }
    }
    
    // MARK: - Persistence
    
    private func saveContacts() {
        do {
            let data = try JSONEncoder().encode(contacts)
            userDefaults.set(data, forKey: contactsKey)
        } catch {
            errorMessage = "Failed to save contacts: \(error.localizedDescription)"
        }
    }
    
    private func loadContacts() {
        guard let data = userDefaults.data(forKey: contactsKey) else {
            // Load default emergency contacts
            loadDefaultContacts()
            return
        }
        
        do {
            contacts = try JSONDecoder().decode([EmergencyContact].self, from: data)
        } catch {
            errorMessage = "Failed to load contacts: \(error.localizedDescription)"
            loadDefaultContacts()
        }
    }
    
    private func loadDefaultContacts() {
        // Add some default emergency contacts
        contacts = []
    }
}

// MARK: - Data Models
// EmergencyContact and EmergencyContactType are defined in Services/EmergencyCallService.swift
