//
//  AIConversationService.swift
//  PetCapsule
//
//  AI Conversation History Service with Supabase Integration
//  🤖 Manages AI agent conversation persistence and retrieval
//

import Foundation
import Supabase
import PostgREST
import Combine

@MainActor
class AIConversationService: ObservableObject {
    static let shared = AIConversationService()
    
    @Published var conversations: [AIConversation] = []
    @Published var currentConversation: AIConversation?
    @Published var messages: [AIMessage] = []
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    private let supabaseService = SupabaseService.shared
    private var cancellables = Set<AnyCancellable>()
    
    private init() {
        // Listen for auth changes to load user conversations
        supabaseService.$isAuthenticated
            .sink { [weak self] isAuthenticated in
                if isAuthenticated {
                    Task {
                        await self?.loadUserConversations()
                    }
                } else {
                    self?.clearLocalData()
                }
            }
            .store(in: &cancellables)
    }
    
    // MARK: - Conversation Management
    
    func loadUserConversations() async {
        print("🔄 Loading user conversations...")
        isLoading = true

        // Get user ID - use development user if no authenticated user
        let userIdString = supabaseService.currentUser?.id ?? "550e8400-e29b-41d4-a716-************"
        guard let userId = UUID(uuidString: userIdString) else {
            print("❌ Invalid user ID format for loading conversations: \(userIdString)")
            isLoading = false
            return
        }

        print("📱 Loading conversations for user: \(userId)")
        
        isLoading = true
        defer { isLoading = false }
        
        do {
            let response: [DatabaseAIConversation] = try await supabaseService.client
                .from("ai_conversations")
                .select("""
                    id,
                    agent_id,
                    pet_id,
                    title,
                    last_message_at,
                    message_count,
                    is_active,
                    created_at,
                    updated_at,
                    ai_agents!inner(id, name, description, specialization, avatar_url)
                """)
                .eq("user_id", value: userId)
                .eq("is_active", value: true)
                .order("last_message_at", ascending: false)
                .execute()
                .value
            
            conversations = response.compactMap { dbConversation in
                AIConversation(from: dbConversation)
            }

            print("✅ Loaded \(conversations.count) conversations from database")
            
        } catch {
            print("❌ Error loading conversations: \(error)")
            errorMessage = "Failed to load conversations: \(error.localizedDescription)"
        }
    }
    
    func createConversation(agentId: UUID, petId: UUID? = nil, title: String? = nil) async -> AIConversation? {
        // Ensure user is authenticated and exists in database
        guard let currentUser = supabaseService.currentUser else {
            print("❌ No authenticated user found for conversation creation")
            return nil
        }

        guard let userId = UUID(uuidString: currentUser.id) else {
            print("❌ Invalid user ID format: \(currentUser.id)")
            return nil
        }

        print("✅ Creating conversation for authenticated user: \(currentUser.email)")

        let conversationData = DatabaseAIConversationInsert(
            userId: userId,
            agentId: agentId,
            petId: petId,
            title: title ?? "New Conversation"
        )

        do {
            // First, insert the conversation
            let insertResponse: DatabaseAIConversationBasic = try await supabaseService.client
                .from("ai_conversations")
                .insert(conversationData)
                .select("id, agent_id, pet_id, title, last_message_at, message_count, is_active, created_at, updated_at")
                .single()
                .execute()
                .value

            // Then, get the agent information separately
            let agentResponse: DatabaseAIAgent = try await supabaseService.client
                .from("ai_agents")
                .select("id, name, description, specialization, avatar_url")
                .eq("id", value: agentId.uuidString)
                .single()
                .execute()
                .value

            // Create the conversation object
            let conversation = AIConversation(
                id: insertResponse.id,
                agentId: insertResponse.agentId,
                agentName: agentResponse.name,
                petId: insertResponse.petId,
                title: insertResponse.title,
                lastMessageAt: insertResponse.lastMessageAt,
                messageCount: insertResponse.messageCount,
                isActive: insertResponse.isActive,
                createdAt: insertResponse.createdAt,
                updatedAt: insertResponse.updatedAt
            )

            conversations.insert(conversation, at: 0)
            currentConversation = conversation
            print("✅ Successfully created conversation for agent: \(agentResponse.name)")
            return conversation

        } catch {
            print("❌ Error creating conversation: \(error)")
            print("❌ Error details: \(error)")
            errorMessage = "Failed to create conversation: \(error.localizedDescription)"
            return nil
        }
    }
    
    func deleteConversation(_ conversationId: UUID) async -> Bool {
        do {
            try await supabaseService.client
                .from("ai_conversations")
                .update(["is_active": false])
                .eq("id", value: conversationId.uuidString)
                .execute()
            
            conversations.removeAll { $0.id == conversationId }
            if currentConversation?.id == conversationId {
                currentConversation = nil
                messages = []
            }
            
            return true
            
        } catch {
            print("❌ Error deleting conversation: \(error)")
            errorMessage = "Failed to delete conversation: \(error.localizedDescription)"
            return false
        }
    }
    
    // MARK: - Message Management
    
    func loadMessages(for conversationId: UUID) async {
        isLoading = true
        defer { isLoading = false }
        
        do {
            let response: [DatabaseAIMessage] = try await supabaseService.client
                .from("ai_messages")
                .select("*")
                .eq("conversation_id", value: conversationId.uuidString)
                .order("created_at", ascending: true)
                .execute()
                .value
            
            messages = response.map { AIMessage(from: $0) }
            
        } catch {
            print("❌ Error loading messages: \(error)")
            errorMessage = "Failed to load messages: \(error.localizedDescription)"
        }
    }
    
    func saveMessage(
        conversationId: UUID,
        content: String,
        isFromUser: Bool,
        messageType: String = "text",
        metadata: [String: Any]? = nil
    ) async -> AIMessage? {
        let messageData = DatabaseAIMessageInsert(
            conversationId: conversationId,
            content: content,
            isFromUser: isFromUser,
            messageType: messageType,
            metadata: metadata
        )
        
        do {
            let response: DatabaseAIMessage = try await supabaseService.client
                .from("ai_messages")
                .insert(messageData)
                .select("*")
                .single()
                .execute()
                .value
            
            let message = AIMessage(from: response)
            messages.append(message)
            
            // Update conversation's last message time and count
            await updateConversationActivity(conversationId)
            
            return message
            
        } catch {
            print("❌ Error saving message: \(error)")
            errorMessage = "Failed to save message: \(error.localizedDescription)"
            return nil
        }
    }
    
    private func updateConversationActivity(_ conversationId: UUID) async {
        do {
            try await supabaseService.client
                .from("ai_conversations")
                .update([
                    "last_message_at": ISO8601DateFormatter().string(from: Date()),
                    "message_count": String(messages.count),
                    "updated_at": ISO8601DateFormatter().string(from: Date())
                ])
                .eq("id", value: conversationId.uuidString)
                .execute()
            
            // Update local conversation object
            if let index = conversations.firstIndex(where: { $0.id == conversationId }) {
                conversations[index].lastMessageAt = Date()
                conversations[index].messageCount = messages.count
                
                // Move to top of list
                let conversation = conversations.remove(at: index)
                conversations.insert(conversation, at: 0)
            }
            
        } catch {
            print("❌ Error updating conversation activity: \(error)")
        }
    }
    
    // MARK: - Helper Methods
    
    func getOrCreateConversation(agentId: UUID, petId: UUID? = nil) async -> AIConversation? {
        // Check if there's an existing active conversation with this agent and pet
        if let existing = conversations.first(where: { 
            $0.agentId == agentId && $0.petId == petId 
        }) {
            currentConversation = existing
            await loadMessages(for: existing.id)
            return existing
        }
        
        // Create new conversation
        return await createConversation(agentId: agentId, petId: petId)
    }
    
    func clearLocalData() {
        conversations = []
        currentConversation = nil
        messages = []
        errorMessage = nil
    }
    
    func setCurrentConversation(_ conversation: AIConversation) async {
        currentConversation = conversation
        await loadMessages(for: conversation.id)
    }
}
