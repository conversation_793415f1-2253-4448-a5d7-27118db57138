//
//  PetHomeKitService.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 12/24/24.
//

import Foundation
import HomeKit
import SwiftUI
import Combine

@MainActor
class PetHomeKitService: NSObject, ObservableObject {
    static let shared = PetHomeKitService()
    
    @Published var isAuthorized = false
    @Published var homes: [HMHome] = []
    @Published var petDevices: [PetSmartDevice] = []
    @Published var automations: [PetAutomation] = []
    @Published var isDiscoveringDevices = false
    
    private let homeManager = HMHomeManager()
    private var currentHome: HMHome?
    
    override init() {
        super.init()
        setupHomeKit()
    }
    
    // MARK: - Setup
    
    private func setupHomeKit() {
        homeManager.delegate = self
        
        // Check authorization status
        switch homeManager.authorizationStatus {
        case .authorized:
            isAuthorized = true
            loadHomes()
        case .determined:
            // Will be requested when needed
            break
        case .restricted:
            isAuthorized = false
        default:
            isAuthorized = false
        }
    }
    
    private func loadHomes() {
        homes = homeManager.homes
        currentHome = homeManager.primaryHome
        
        if let home = currentHome {
            loadPetDevices(from: home)
            loadAutomations(from: home)
        }
    }
    
    // MARK: - Device Discovery
    
    func discoverPetDevices() {
        guard let home = currentHome else { return }
        
        isDiscoveringDevices = true
        
        // Scan for pet-related accessories
        let petDeviceTypes: [String] = [
            "pet feeder",
            "pet camera",
            "pet door",
            "pet fountain",
            "pet toy",
            "pet tracker",
            "pet bed",
            "pet gate"
        ]
        
        var discoveredDevices: [PetSmartDevice] = []
        
        for accessory in home.accessories {
            let deviceName = accessory.name.lowercased()
            
            for deviceType in petDeviceTypes {
                if deviceName.contains(deviceType) {
                    let petDevice = PetSmartDevice.fromHMAccessory(accessory)
                    discoveredDevices.append(petDevice)
                    break
                }
            }
        }
        
        petDevices = discoveredDevices
        isDiscoveringDevices = false
    }
    
    private func loadPetDevices(from home: HMHome) {
        var devices: [PetSmartDevice] = []
        
        for accessory in home.accessories {
            if isPetRelatedDevice(accessory) {
                let device = PetSmartDevice.fromHMAccessory(accessory)
                devices.append(device)
            }
        }
        
        petDevices = devices
    }
    
    private func isPetRelatedDevice(_ accessory: HMAccessory) -> Bool {
        let petKeywords = ["pet", "dog", "cat", "feeder", "fountain", "camera", "door", "toy"]
        let deviceName = accessory.name.lowercased()
        
        return petKeywords.contains { deviceName.contains($0) }
    }
    
    // MARK: - Device Control
    
    func controlDevice(_ device: PetSmartDevice, action: DeviceAction) async throws {
        guard let home = currentHome,
              let accessory = home.accessories.first(where: { $0.uniqueIdentifier == device.accessoryId }) else {
            throw HomeKitError.deviceNotFound
        }
        
        switch action {
        case .turnOn:
            try await turnOnDevice(accessory)
        case .turnOff:
            try await turnOffDevice(accessory)
        case .feedPet(let amount):
            try await feedPet(accessory, amount: amount)
        case .dispenseTreat:
            try await dispenseTreat(accessory)
        case .startCamera:
            try await startCamera(accessory)
        case .openDoor:
            try await openPetDoor(accessory)
        case .closeDoor:
            try await closePetDoor(accessory)
        case .setBrightness(let level):
            try await setBrightness(accessory, level: level)
        case .setTemperature(let temperature):
            try await setTemperature(accessory, temperature: temperature)
        }
        
        // Update device status
        updateDeviceStatus(device)
    }
    
    private func turnOnDevice(_ accessory: HMAccessory) async throws {
        guard let service = accessory.services.first(where: { $0.serviceType == HMServiceTypeLightbulb || $0.serviceType == HMServiceTypeSwitch }),
              let characteristic = service.characteristics.first(where: { $0.characteristicType == HMCharacteristicTypePowerState }) else {
            throw HomeKitError.characteristicNotFound
        }
        
        try await characteristic.writeValue(true)
    }
    
    private func turnOffDevice(_ accessory: HMAccessory) async throws {
        guard let service = accessory.services.first(where: { $0.serviceType == HMServiceTypeLightbulb || $0.serviceType == HMServiceTypeSwitch }),
              let characteristic = service.characteristics.first(where: { $0.characteristicType == HMCharacteristicTypePowerState }) else {
            throw HomeKitError.characteristicNotFound
        }
        
        try await characteristic.writeValue(false)
    }
    
    private func feedPet(_ accessory: HMAccessory, amount: Double) async throws {
        // Custom implementation for pet feeder
        // This would depend on the specific device's characteristics
        
        if let customService = accessory.services.first(where: { $0.serviceType.contains("feeder") }) {
            // Look for feeding amount characteristic
            if let amountCharacteristic = customService.characteristics.first(where: { $0.characteristicType.contains("amount") }) {
                try await amountCharacteristic.writeValue(amount)
            }
            
            // Trigger feeding
            if let feedCharacteristic = customService.characteristics.first(where: { $0.characteristicType.contains("feed") }) {
                try await feedCharacteristic.writeValue(true)
            }
        }
    }
    
    private func dispenseTreat(_ accessory: HMAccessory) async throws {
        // Custom implementation for treat dispenser
        if let customService = accessory.services.first(where: { $0.serviceType.contains("dispenser") }),
           let dispenserCharacteristic = customService.characteristics.first(where: { $0.characteristicType.contains("dispense") }) {
            try await dispenserCharacteristic.writeValue(true)
        }
    }
    
    private func startCamera(_ accessory: HMAccessory) async throws {
        guard let cameraService = accessory.services.first(where: { $0.serviceType == HMServiceTypeCameraRTPStreamManagement }) else {
            throw HomeKitError.serviceNotFound
        }
        
        // Start camera stream
        // Implementation would depend on camera-specific characteristics
    }
    
    private func openPetDoor(_ accessory: HMAccessory) async throws {
        if let doorService = accessory.services.first(where: { $0.serviceType.contains("door") }),
           let lockCharacteristic = doorService.characteristics.first(where: { $0.characteristicType == HMCharacteristicTypePowerState }) {
            try await lockCharacteristic.writeValue(NSNumber(value: 1))
        }
    }
    
    private func closePetDoor(_ accessory: HMAccessory) async throws {
        if let doorService = accessory.services.first(where: { $0.serviceType.contains("door") }),
           let lockCharacteristic = doorService.characteristics.first(where: { $0.characteristicType == HMCharacteristicTypePowerState }) {
            try await lockCharacteristic.writeValue(NSNumber(value: 0))
        }
    }
    
    private func setBrightness(_ accessory: HMAccessory, level: Int) async throws {
        guard let lightService = accessory.services.first(where: { $0.serviceType == HMServiceTypeLightbulb }),
              let brightnessCharacteristic = lightService.characteristics.first(where: { $0.characteristicType == HMCharacteristicTypeBrightness }) else {
            throw HomeKitError.characteristicNotFound
        }
        
        try await brightnessCharacteristic.writeValue(level)
    }
    
    private func setTemperature(_ accessory: HMAccessory, temperature: Double) async throws {
        guard let thermostatService = accessory.services.first(where: { $0.serviceType == HMServiceTypeThermostat }),
              let temperatureCharacteristic = thermostatService.characteristics.first(where: { $0.characteristicType == HMCharacteristicTypeTargetTemperature }) else {
            throw HomeKitError.characteristicNotFound
        }
        
        try await temperatureCharacteristic.writeValue(temperature)
    }
    
    // MARK: - Automation
    
    func createFeedingAutomation(for pet: Pet, schedule: FeedingSchedule) async throws -> PetAutomation {
        guard let home = currentHome else {
            throw HomeKitError.homeNotFound
        }
        
        // Find pet feeder device
        guard let feederDevice = petDevices.first(where: { $0.type == .feeder }),
              let feederAccessory = home.accessories.first(where: { $0.uniqueIdentifier == feederDevice.accessoryId }) else {
            throw HomeKitError.deviceNotFound
        }
        
        // Create time-based trigger
        let trigger = HMTimerTrigger(
            name: "Feed \(pet.name)",
            fireDate: schedule.nextFeedingTime,
            timeZone: TimeZone.current,
            recurrence: schedule.recurrenceComponents,
            recurrenceCalendar: Calendar.current
        )
        
        // Create feeding action
        let feedingAction = HMCharacteristicWriteAction(
            characteristic: feederAccessory.services.first!.characteristics.first!,
            targetValue: NSNumber(value: schedule.amount)
        )
        
        // Create action set
        // Note: HMActionSet requires a home to be created
        // This is a placeholder for when HomeKit automation becomes available
        print("Would create action set for feeding \(pet.name)")
        // try await actionSet.addAction(feedingAction) // Not available in simulator
        
        // Create automation
        // Note: HMTriggerBasedAutomation is not available in iOS Simulator
        // This is a placeholder for when HomeKit automation becomes available
        print("Would create automation: Auto Feed \(pet.name) at \(schedule.nextFeedingTime)")
        
        // try await home.addTrigger(automation) // Not available in simulator

        let petAutomation = PetAutomation(
            id: UUID(),
            name: "Auto Feed \(pet.name)",
            type: .feeding,
            petId: pet.id,
            isEnabled: true,
            schedule: schedule,
            triggerId: UUID() // automation.uniqueIdentifier not available
        )
        
        automations.append(petAutomation)
        
        return petAutomation
    }
    
    func createWalkReminderAutomation(for pet: Pet, walkTimes: [Date]) async throws -> PetAutomation {
        // Create automation to turn on lights or send notifications for walk time
        guard let home = currentHome else {
            throw HomeKitError.homeNotFound
        }
        
        // This would create multiple triggers for different walk times
        // Implementation would depend on specific requirements
        
        let petAutomation = PetAutomation(
            id: UUID(),
            name: "Walk Reminder for \(pet.name)",
            type: .walkReminder,
            petId: pet.id,
            isEnabled: true,
            schedule: nil,
            triggerId: UUID()
        )
        
        automations.append(petAutomation)
        
        return petAutomation
    }
    
    func createSecurityAutomation(for pet: Pet) async throws -> PetAutomation {
        // Create automation for when pet door is used
        guard let home = currentHome else {
            throw HomeKitError.homeNotFound
        }
        
        let petAutomation = PetAutomation(
            id: UUID(),
            name: "Pet Security for \(pet.name)",
            type: .security,
            petId: pet.id,
            isEnabled: true,
            schedule: nil,
            triggerId: UUID()
        )
        
        automations.append(petAutomation)
        
        return petAutomation
    }
    
    // MARK: - Device Status
    
    private func updateDeviceStatus(_ device: PetSmartDevice) {
        // Update device status in the local array
        if let index = petDevices.firstIndex(where: { $0.id == device.id }) {
            petDevices[index].lastUpdated = Date()
        }
    }
    
    func refreshDeviceStatus() async {
        for device in petDevices {
            await refreshSingleDeviceStatus(device)
        }
    }
    
    private func refreshSingleDeviceStatus(_ device: PetSmartDevice) async {
        guard let home = currentHome,
              let accessory = home.accessories.first(where: { $0.uniqueIdentifier == device.accessoryId }) else {
            return
        }
        
        // Read current values from device characteristics
        for service in accessory.services {
            for characteristic in service.characteristics {
                if characteristic.properties.contains(HMCharacteristicPropertyReadable) {
                    do {
                        try await characteristic.readValue()
                    } catch {
                        print("Failed to read characteristic: \(error)")
                    }
                }
            }
        }
    }
    
    // MARK: - Automation Management
    
    private func loadAutomations(from home: HMHome) {
        // Load existing automations that are pet-related
        var petAutomations: [PetAutomation] = []
        
        for trigger in home.triggers {
            if trigger.name.lowercased().contains("pet") || 
               trigger.name.lowercased().contains("feed") ||
               trigger.name.lowercased().contains("walk") {
                
                let automation = PetAutomation(
                    id: UUID(),
                    name: trigger.name,
                    type: .feeding, // Would need to determine actual type
                    petId: UUID(), // Would need to extract from trigger data
                    isEnabled: trigger.isEnabled,
                    schedule: nil,
                    triggerId: trigger.uniqueIdentifier
                )
                
                petAutomations.append(automation)
            }
        }
        
        automations = petAutomations
    }
    
    func toggleAutomation(_ automation: PetAutomation) async throws {
        guard let home = currentHome,
              let trigger = home.triggers.first(where: { $0.uniqueIdentifier == automation.triggerId }) else {
            throw HomeKitError.automationNotFound
        }
        
        // Note: isEnabled is read-only, cannot be modified directly
        print("Would toggle trigger: \(trigger)")
        
        // Update local automation
        if let index = automations.firstIndex(where: { $0.id == automation.id }) {
            automations[index].isEnabled = trigger.isEnabled
        }
    }
    
    func deleteAutomation(_ automation: PetAutomation) async throws {
        guard let home = currentHome,
              let trigger = home.triggers.first(where: { $0.uniqueIdentifier == automation.triggerId }) else {
            throw HomeKitError.automationNotFound
        }
        
        try await home.removeTrigger(trigger)
        
        automations.removeAll { $0.id == automation.id }
    }
}

// MARK: - HMHomeManagerDelegate

extension PetHomeKitService: HMHomeManagerDelegate {
    nonisolated func homeManagerDidUpdateHomes(_ manager: HMHomeManager) {
        DispatchQueue.main.async {
            self.loadHomes()
        }
    }
    
    nonisolated func homeManagerDidUpdatePrimaryHome(_ manager: HMHomeManager) {
        DispatchQueue.main.async {
            self.currentHome = manager.homes.first
            if let home = self.currentHome {
                self.loadPetDevices(from: home)
                self.loadAutomations(from: home)
            }
        }
    }
}

// MARK: - Data Models

struct PetSmartDevice: Identifiable {
    let id: UUID
    let accessoryId: UUID
    let name: String
    let type: PetDeviceType
    let manufacturer: String
    let model: String
    var isOnline: Bool
    var lastUpdated: Date
    
    static func fromHMAccessory(_ accessory: HMAccessory) -> PetSmartDevice {
        let deviceType = determineDeviceType(from: accessory.name)
        
        return PetSmartDevice(
            id: UUID(),
            accessoryId: accessory.uniqueIdentifier,
            name: accessory.name,
            type: deviceType,
            manufacturer: accessory.manufacturer ?? "Unknown",
            model: accessory.model ?? "Unknown",
            isOnline: accessory.isReachable,
            lastUpdated: Date()
        )
    }
    
    private static func determineDeviceType(from name: String) -> PetDeviceType {
        let lowercaseName = name.lowercased()
        
        if lowercaseName.contains("feeder") {
            return .feeder
        } else if lowercaseName.contains("camera") {
            return .camera
        } else if lowercaseName.contains("door") {
            return .door
        } else if lowercaseName.contains("fountain") || lowercaseName.contains("water") {
            return .waterFountain
        } else if lowercaseName.contains("toy") {
            return .toy
        } else if lowercaseName.contains("tracker") {
            return .tracker
        } else if lowercaseName.contains("bed") {
            return .bed
        } else {
            return .other
        }
    }
}

enum PetDeviceType: String, CaseIterable {
    case feeder = "feeder"
    case camera = "camera"
    case door = "door"
    case waterFountain = "water_fountain"
    case toy = "toy"
    case tracker = "tracker"
    case bed = "bed"
    case other = "other"
    
    var displayName: String {
        switch self {
        case .feeder: return "Pet Feeder"
        case .camera: return "Pet Camera"
        case .door: return "Pet Door"
        case .waterFountain: return "Water Fountain"
        case .toy: return "Smart Toy"
        case .tracker: return "Pet Tracker"
        case .bed: return "Smart Bed"
        case .other: return "Other Device"
        }
    }
    
    var icon: String {
        switch self {
        case .feeder: return "bowl.fill"
        case .camera: return "camera.fill"
        case .door: return "door.left.hand.open"
        case .waterFountain: return "drop.fill"
        case .toy: return "gamecontroller.fill"
        case .tracker: return "location.fill"
        case .bed: return "bed.double.fill"
        case .other: return "house.fill"
        }
    }
}

enum DeviceAction {
    case turnOn
    case turnOff
    case feedPet(amount: Double)
    case dispenseTreat
    case startCamera
    case openDoor
    case closeDoor
    case setBrightness(level: Int)
    case setTemperature(temperature: Double)
}

struct PetAutomation: Identifiable {
    let id: UUID
    let name: String
    let type: AutomationType
    let petId: UUID
    var isEnabled: Bool
    let schedule: FeedingSchedule?
    let triggerId: UUID
}

enum AutomationType: String, CaseIterable {
    case feeding = "feeding"
    case walkReminder = "walk_reminder"
    case security = "security"
    case lighting = "lighting"
    case temperature = "temperature"
    
    var displayName: String {
        switch self {
        case .feeding: return "Auto Feeding"
        case .walkReminder: return "Walk Reminder"
        case .security: return "Pet Security"
        case .lighting: return "Smart Lighting"
        case .temperature: return "Temperature Control"
        }
    }
}

struct FeedingSchedule {
    let amount: Double
    let nextFeedingTime: Date
    let recurrenceComponents: DateComponents
}

enum HomeKitError: Error {
    case homeNotFound
    case deviceNotFound
    case serviceNotFound
    case characteristicNotFound
    case automationNotFound
    case unauthorized
}

// MARK: - SwiftUI Integration

struct HomeKitDeviceCard: View {
    let device: PetSmartDevice
    @StateObject private var homeKitService = PetHomeKitService.shared
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: device.type.icon)
                    .font(.title2)
                    .foregroundColor(.blue)
                
                VStack(alignment: .leading) {
                    Text(device.name)
                        .font(.headline)
                    
                    Text(device.type.displayName)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                Circle()
                    .fill(device.isOnline ? Color.green : Color.red)
                    .frame(width: 8, height: 8)
            }
            
            HStack {
                Button("Turn On") {
                    Task {
                        try? await homeKitService.controlDevice(device, action: .turnOn)
                    }
                }
                .buttonStyle(.bordered)
                .controlSize(.small)
                
                Button("Turn Off") {
                    Task {
                        try? await homeKitService.controlDevice(device, action: .turnOff)
                    }
                }
                .buttonStyle(.bordered)
                .controlSize(.small)
                
                if device.type == .feeder {
                    Button("Feed") {
                        Task {
                            try? await homeKitService.controlDevice(device, action: .feedPet(amount: 1.0))
                        }
                    }
                    .buttonStyle(.borderedProminent)
                    .controlSize(.small)
                }
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}
