//
//  EnhancedAIAgentService.swift
//  PetCapsule
//
//  🚀 PHASE 1: Production-Ready AI System with Real Gemini Integration
//  🤖 Advanced multi-agent system for specialized pet support
//

import Foundation
import SwiftUI
import Speech
import AVFoundation
import Supabase

@MainActor
class EnhancedAIAgentService: ObservableObject {
    static let shared = EnhancedAIAgentService()

    @Published var isLoading = false
    @Published var currentAgent: AIAgent?
    @Published var conversationHistory: [String: [ChatMessage]] = [:]
    @Published var availableAgents: [AIAgent] = []
    @Published var lastError: String?
    @Published var agentAvailabilityStatus: [String: Bool] = [:]

    // Voice capabilities
    @Published var isListening = false
    @Published var isProcessingVoice = false

    // Conversation service integration
    private let conversationService = AIConversationService.shared

    private let geminiService = GeminiService.shared
    private let knowledgeBaseService = KnowledgeBaseService.shared
    private let webSearchService = WebSearchService.shared
    private weak var realDataService: RealDataService?
    private let responseValidator = AIResponseValidator.shared
    private let speechRecognizer = SFSpeechRecognizer()
    private let audioEngine = AVAudioEngine()
    private var recognitionRequest: SFSpeechAudioBufferRecognitionRequest?
    private var recognitionTask: SFSpeechRecognitionTask?
    private let synthesizer = AVSpeechSynthesizer()

    // Supabase for conversation history
    private let supabase = SupabaseClient(
        supabaseURL: URL(string: Config.Supabase.url)!,
        supabaseKey: Config.Supabase.anonKey
    )

    // Context management
    private var petContexts: [String: Pet] = [:]
    private var conversationContexts: [String: [String: Any]] = [:]

    // Enhanced features
    @Published var allUserPets: [Pet] = []
    @Published var agentDataCache: [String: [String: Any]] = [:]

    private init() {
        setupAgents()
        requestSpeechPermission()
        Task {
            await loadUserPets()
            await loadConversationHistory()
        }
    }

    // MARK: - Configuration
    func setRealDataService(_ service: RealDataService) {
        self.realDataService = service
    }

    // MARK: - Agent Setup

    private func setupAgents() {
        availableAgents = [
            // 🥗 Dr. Nutrition - Free
            AIAgent(
                name: "Dr. Nutrition",
                iconName: "🥗",
                description: "Expert nutritionist specializing in personalized meal plans and dietary health",
                specialty: "Nutrition",
                specialties: ["Nutrition", "Meal Planning", "Weight Management", "Food Safety"],
                gradientColors: ["#4CAF50", "#8BC34A"],
                isPremium: false,
                systemPrompt: "You are Dr. Nutrition, an expert pet nutritionist. Provide personalized meal plans, dietary advice, and nutritional guidance for pets.",
                conversationStarters: [
                    "What's the best diet for my pet?",
                    "Help me create a meal plan",
                    "My pet needs to lose weight",
                    "What supplements should I consider?"
                ],
                responseConfig: AIResponseConfig(
                    maxTokens: 800,
                    temperature: 0.7,
                    tone: "caring",
                    responseStyle: "detailed",
                    expertise: "beginner-friendly"
                )
            ),

            // 🏥 Health and Emergency - Comprehensive health care
            AIAgent(
                name: "Health and Emergency",
                iconName: "🏥",
                description: "Comprehensive health specialist providing medical guidance, emergency care, and preventive health monitoring",
                specialty: "Health",
                specialties: ["Health Monitoring", "Emergency Care", "Symptom Analysis", "Preventive Care", "First Aid", "Crisis Management"],
                gradientColors: ["#F44336", "#E91E63"],
                isPremium: false,
                systemPrompt: "You are Health and Emergency, a comprehensive AI health specialist. Provide professional health monitoring, symptom analysis, preventive care guidance, emergency response, and first aid instructions. Always emphasize seeking immediate veterinary care for emergencies and serious concerns.",
                conversationStarters: [
                    "Check my pet's symptoms",
                    "Pet emergency help needed!",
                    "Create a health monitoring plan",
                    "First aid guidance needed"
                ],
                responseConfig: AIResponseConfig(
                    maxTokens: 1000,
                    temperature: 0.4,
                    tone: "professional",
                    responseStyle: "step-by-step",
                    expertise: "advanced"
                )
            ),

            // ✂️ Style Guru - Free
            AIAgent(
                name: "Style Guru",
                iconName: "✂️",
                description: "Professional groomer offering styling tips and hygiene guidance",
                specialty: "Grooming",
                specialties: ["Grooming", "Styling", "Hygiene", "Coat Care"],
                gradientColors: ["#9C27B0", "#673AB7"],
                isPremium: false,
                systemPrompt: "You are Style Guru, a professional pet groomer. Provide styling tips, hygiene guidance, and coat care advice for all types of pets.",
                conversationStarters: [
                    "How should I groom my pet?",
                    "What's the best brushing routine?",
                    "Help with nail trimming",
                    "My pet's coat needs attention"
                ],
                responseConfig: AIResponseConfig(
                    maxTokens: 600,
                    temperature: 0.8,
                    tone: "friendly",
                    responseStyle: "step-by-step",
                    expertise: "intermediate"
                )
            ),

            // 🎾 Trainer Pro - Now Free
            AIAgent(
                name: "Trainer Pro",
                iconName: "🎾",
                description: "Professional trainer specializing in behavior modification and obedience",
                specialty: "Training",
                specialties: ["Training", "Behavior", "Obedience", "Socialization"],
                gradientColors: ["#FF9800", "#FFC107"],
                isPremium: false,
                systemPrompt: "You are Trainer Pro, a professional pet trainer. Provide behavior modification techniques, obedience training, and socialization guidance.",
                conversationStarters: [
                    "Help train my pet",
                    "Fix behavioral issues",
                    "Teach basic commands",
                    "Socialization tips needed"
                ],
                responseConfig: AIResponseConfig(
                    maxTokens: 800,
                    temperature: 0.6,
                    tone: "energetic",
                    responseStyle: "step-by-step",
                    expertise: "intermediate"
                )
            ),

            // 🛍️ Shopping Assistant - Free
            AIAgent(
                name: "Shopping Assistant",
                iconName: "🛍️",
                description: "Smart product recommendations and deal finder for pet supplies",
                specialty: "Shopping",
                specialties: ["Product Reviews", "Price Comparison", "Deals", "Safety"],
                gradientColors: ["#2196F3", "#03A9F4"],
                isPremium: false,
                systemPrompt: "You are Shopping Assistant, a smart product advisor for pet supplies. Provide product recommendations, price comparisons, and safety guidance.",
                conversationStarters: [
                    "Find the best pet food",
                    "Compare product prices",
                    "Safe toy recommendations",
                    "What supplies do I need?"
                ],
                responseConfig: AIResponseConfig(
                    maxTokens: 500,
                    temperature: 0.5,
                    tone: "helpful",
                    responseStyle: "concise",
                    expertise: "beginner-friendly"
                )
            ),









            // 🛡️ Pet Insurance Advisor - Now Free (NEW)
            AIAgent(
                name: "Pet Insurance Advisor",
                iconName: "🛡️",
                description: "Insurance specialist helping choose the best coverage for your pet",
                specialty: "Insurance",
                specialties: ["Insurance", "Coverage", "Claims", "Cost Analysis"],
                gradientColors: ["#388E3C", "#4CAF50"],
                isPremium: false,
                systemPrompt: "You are Pet Insurance Advisor, an insurance specialist. Help choose the best pet insurance coverage, explain policy options, assist with claims, and provide cost analysis.",
                conversationStarters: [
                    "Find the best pet insurance",
                    "Compare coverage options",
                    "Help with insurance claims",
                    "Cost analysis needed"
                ],
                responseConfig: AIResponseConfig(
                    maxTokens: 600,
                    temperature: 0.4,
                    tone: "helpful",
                    responseStyle: "detailed",
                    expertise: "intermediate"
                )
            ),

            // 👑 Pet Master - Ultimate AI Agent (NEW)
            AIAgent(
                name: "Pet Master",
                iconName: "👑",
                description: "Ultimate AI agent with access to all data sources, knowledge base, and internet search",
                specialty: "Master",
                specialties: ["All Specialties", "Data Integration", "Knowledge Base", "Internet Search"],
                gradientColors: ["#FF6B35", "#F7931E"],
                isPremium: false,
                systemPrompt: "You are Pet Master, the ultimate AI agent with access to all pet data, knowledge base folders, conversation history from other agents, and internet search capabilities. Provide comprehensive, well-researched answers using all available resources.",
                conversationStarters: [
                    "Give me a complete analysis of my pet",
                    "Search for the latest pet care information",
                    "What do other agents say about this?",
                    "Find comprehensive information about..."
                ],
                responseConfig: AIResponseConfig(
                    maxTokens: 1200,
                    temperature: 0.6,
                    tone: "comprehensive",
                    responseStyle: "detailed",
                    expertise: "expert"
                )
            )
        ]

        // Mark all agents as available 24/7
        markAllAgentsAsAvailable()
    }

    private func markAllAgentsAsAvailable() {
        for agent in availableAgents {
            agentAvailabilityStatus[agent.id.uuidString] = true
        }
    }

    func isAgentAvailable(_ agent: AIAgent) -> Bool {
        return agentAvailabilityStatus[agent.id.uuidString] ?? true
    }

    // MARK: - Core Chat Functions

    func sendMessage(
        to agent: AIAgent,
        message: String,
        pet: Pet? = nil,
        includeImage: UIImage? = nil
    ) async -> String {
        isLoading = true
        defer { isLoading = false }

        let agentKey = agent.id.uuidString

        // Store pet context
        if let pet = pet {
            petContexts[agentKey] = pet
        }

        // Add user message to history
        let userMessage = ChatMessage(content: message, isFromUser: true, agentId: agent.id)
        if conversationHistory[agentKey] == nil {
            conversationHistory[agentKey] = []
        }
        conversationHistory[agentKey]?.append(userMessage)

        do {
            let response: String

            if agent.name == "Pet Master" {
                // Enhanced Pet Master with all capabilities
                response = await handlePetMasterRequest(message: message, pet: pet, includeImage: includeImage)
            } else if let image = includeImage {
                // Image analysis
                response = try await geminiService.analyzeImage(
                    image: image,
                    agent: agent,
                    pet: pet,
                    analysisType: determineAnalysisType(for: agent)
                )
            } else {
                // Enhanced text chat with knowledge base integration
                let enhancedContext = await buildEnhancedContext(for: agent, message: message, pet: pet)
                let rawResponse = try await geminiService.sendMessage(
                    to: agent,
                    message: message,
                    pet: pet,
                    context: enhancedContext
                )

                // Validate and format response to ensure agent stays within specialty
                response = responseValidator.validateAndFormatResponse(
                    response: rawResponse,
                    agent: agent,
                    userMessage: message
                )
            }

            // Add AI response to history
            let aiMessage = ChatMessage(content: response, isFromUser: false, agentId: agent.id)
            conversationHistory[agentKey]?.append(aiMessage)

            // Cache agent insight for Pet Master (if not Pet Master itself)
            if agent.name != "Pet Master" {
                cacheAgentInsight(agentId: agent.id, insight: response)
            }

            // Save conversation to new AI conversation system
            await saveToConversationHistory(agent: agent, userMessage: userMessage, aiMessage: aiMessage, pet: pet)

            return response

        } catch {
            lastError = error.localizedDescription
            return "I apologize, but I'm having trouble processing your request right now. Please try again in a moment."
        }
    }

    // MARK: - Voice Integration

    func startVoiceRecognition(for agent: AIAgent) async {
        guard speechRecognizer?.isAvailable == true else {
            lastError = "Speech recognition not available"
            return
        }

        isListening = true

        do {
            try await startSpeechRecognition { [weak self] recognizedText in
                Task { @MainActor in
                    if !recognizedText.isEmpty {
                        self?.isProcessingVoice = true
                        let response = await self?.sendMessage(to: agent, message: recognizedText) ?? ""
                        await self?.speakResponse(response)
                        self?.isProcessingVoice = false
                    }
                }
            }
        } catch {
            lastError = "Voice recognition failed: \(error.localizedDescription)"
            isListening = false
        }
    }

    func stopVoiceRecognition() {
        recognitionTask?.cancel()
        recognitionTask = nil
        recognitionRequest = nil
        audioEngine.stop()
        audioEngine.inputNode.removeTap(onBus: 0)
        isListening = false
    }

    private func speakResponse(_ text: String) async {
        // Stop any current speech before starting new one
        if synthesizer.isSpeaking {
            synthesizer.stopSpeaking(at: .immediate)
        }

        let utterance = AVSpeechUtterance(string: text)
        utterance.voice = AVSpeechSynthesisVoice(language: "en-US")
        utterance.rate = 0.5
        utterance.pitchMultiplier = 1.0
        utterance.volume = 0.8

        synthesizer.speak(utterance)
    }

    // MARK: - Personalized Recommendations

    func generateRecommendations(
        for pet: Pet,
        agent: AIAgent,
        category: GeminiRecommendationCategory
    ) async -> [PersonalizedRecommendation] {
        do {
            return try await geminiService.generatePersonalizedRecommendations(
                for: pet,
                agent: agent,
                category: category
            )
        } catch {
            lastError = error.localizedDescription
            return []
        }
    }

    // MARK: - Multi-language Support

    func translateLastResponse(to language: String, for agent: AIAgent) async -> String? {
        guard let lastMessage = conversationHistory[agent.id.uuidString]?.last,
              !lastMessage.isFromUser else {
            return nil
        }

        do {
            return try await geminiService.translateResponse(
                text: lastMessage.content,
                targetLanguage: language
            )
        } catch {
            lastError = error.localizedDescription
            return nil
        }
    }

    // MARK: - Context Management

    func clearConversation(for agent: AIAgent) {
        let agentKey = agent.id.uuidString
        conversationHistory.removeValue(forKey: agentKey)
        conversationContexts.removeValue(forKey: agentKey)
        geminiService.clearConversationHistory(for: agent.id)
    }

    func updatePetContext(pet: Pet, for agent: AIAgent) {
        let agentKey = agent.id.uuidString
        petContexts[agentKey] = pet
        geminiService.updatePetContext(pet: pet, for: agent.id)
    }

    // Public method to refresh pets when new ones are added
    func refreshUserPets() async {
        await loadUserPets()
    }

    // Public method to cache agent insights for Pet Master
    func cacheAgentInsight(agentId: UUID, insight: String) {
        agentDataCache[agentId.uuidString] = ["last_insight": insight, "timestamp": Date()]
    }

    // MARK: - Helper Methods

    private func determineAnalysisType(for agent: AIAgent) -> ImageAnalysisType {
        switch agent.name {
        case "Health and Emergency":
            return .health
        case "Style Guru":
            return .grooming
        case "Trainer Pro":
            return .behavior
        case "Dr. Nutrition":
            return .nutrition
        default:
            return .general
        }
    }

    private func requestSpeechPermission() {
        SFSpeechRecognizer.requestAuthorization { status in
            DispatchQueue.main.async {
                // Handle permission status
            }
        }
    }

    private func startSpeechRecognition(completion: @escaping (String) -> Void) async throws {
        recognitionRequest = SFSpeechAudioBufferRecognitionRequest()

        guard let recognitionRequest = recognitionRequest else {
            throw NSError(domain: "SpeechError", code: 1, userInfo: [NSLocalizedDescriptionKey: "Unable to create recognition request"])
        }

        recognitionRequest.shouldReportPartialResults = true

        recognitionTask = speechRecognizer?.recognitionTask(with: recognitionRequest) { result, error in
            if let result = result {
                completion(result.bestTranscription.formattedString)
            }

            if error != nil {
                self.stopVoiceRecognition()
            }
        }

        let inputNode = audioEngine.inputNode
        let recordingFormat = inputNode.outputFormat(forBus: 0)

        inputNode.installTap(onBus: 0, bufferSize: 1024, format: recordingFormat) { buffer, _ in
            recognitionRequest.append(buffer)
        }

        audioEngine.prepare()
        try audioEngine.start()
    }

    // MARK: - Enhanced AI Features

    // Load all user pets dynamically
    private func loadUserPets() async {
        // Load pets from RealDataService
        guard let realDataService = realDataService else {
            print("⚠️ RealDataService not set, cannot load user pets")
            return
        }
        allUserPets = realDataService.pets

        // Update pet contexts for all agents when new pets are added
        for agent in availableAgents {
            for pet in allUserPets {
                updatePetContext(pet: pet, for: agent)
            }
        }
    }

    // Build enhanced context with knowledge base integration
    private func buildEnhancedContext(for agent: AIAgent, message: String, pet: Pet?) async -> [String: Any] {
        var context = conversationContexts[agent.id.uuidString] ?? [:]

        // Add all user pets context
        context["all_pets"] = allUserPets.map { pet in
            [
                "name": pet.name,
                "species": pet.species,
                "breed": pet.breed,
                "age": pet.age,
                "health_score": pet.healthScore,
                "activity_level": pet.activityLevel
            ]
        }

        // Search knowledge base for relevant information
        let knowledgeResults = knowledgeBaseService.searchDocuments(query: message)
        if !knowledgeResults.isEmpty {
            context["knowledge_base"] = knowledgeResults.map { doc in
                [
                    "title": doc.title,
                    "content": doc.content,
                    "type": doc.type.rawValue,
                    "tags": doc.tags
                ]
            }
        }

        return context
    }

    // Handle Pet Master requests with all capabilities
    private func handlePetMasterRequest(message: String, pet: Pet?, includeImage: UIImage?) async -> String {
        var enhancedPrompt = """
        You are Pet Master, the ultimate AI agent with access to comprehensive pet data and resources.

        User Query: \(message)

        Available Resources:
        """

        // Add all pets context
        if !allUserPets.isEmpty {
            enhancedPrompt += "\n\nAll User Pets:\n"
            for userPet in allUserPets {
                enhancedPrompt += "- \(userPet.name) (\(userPet.species), \(userPet.breed), Age: \(userPet.age))\n"
            }
        }

        // Add knowledge base search results
        let knowledgeResults = knowledgeBaseService.searchDocuments(query: message)
        if !knowledgeResults.isEmpty {
            enhancedPrompt += "\n\nRelevant Knowledge Base Information:\n"
            for doc in knowledgeResults.prefix(3) {
                enhancedPrompt += "- \(doc.title): \(doc.content.prefix(200))...\n"
            }
        }

        // Add data from other agents
        let agentData = await gatherDataFromOtherAgents(query: message)
        if !agentData.isEmpty {
            enhancedPrompt += "\n\nInsights from Other AI Agents:\n\(agentData)"
        }

        // Perform internet search if needed
        let internetResults = await performInternetSearch(query: message)
        if !internetResults.isEmpty {
            enhancedPrompt += "\n\nLatest Internet Information:\n\(internetResults)"
        }

        enhancedPrompt += "\n\nProvide a comprehensive response using all available information."

        do {
            let petMasterAgent = availableAgents.first { $0.name == "Pet Master" }!
            return try await geminiService.sendMessage(
                to: petMasterAgent,
                message: enhancedPrompt,
                pet: pet,
                context: [:]
            )
        } catch {
            return "I apologize, but I'm having trouble accessing all the information sources right now. Please try again in a moment."
        }
    }

    // Gather data from other agents
    private func gatherDataFromOtherAgents(query: String) async -> String {
        var agentInsights = ""

        // Check cached data from other agents
        for (agentId, data) in agentDataCache {
            if let agentName = availableAgents.first(where: { $0.id.uuidString == agentId })?.name {
                agentInsights += "From \(agentName): \(data["last_insight"] as? String ?? "")\n"
            }
        }

        return agentInsights
    }

    // Perform internet search
    private func performInternetSearch(query: String) async -> String {
        let searchResults = await webSearchService.searchPetSpecificInfo(query: query)

        if searchResults.isEmpty {
            return ""
        }

        var internetInfo = ""
        for result in searchResults.prefix(3) {
            internetInfo += "• \(result.title)\n  \(result.snippet)\n  Source: \(result.source)\n\n"
        }

        return internetInfo
    }

    // Save conversation to new AI conversation system
    private func saveToConversationHistory(agent: AIAgent, userMessage: ChatMessage, aiMessage: ChatMessage, pet: Pet?) async {
        // Get agent database ID
        guard let agentId = getAgentDatabaseId(for: agent.name) else {
            print("❌ Could not find agent database ID for: \(agent.name)")
            return
        }

        // Get or create conversation
        let conversation = await conversationService.getOrCreateConversation(
            agentId: agentId,
            petId: pet?.id
        )

        guard let conversation = conversation else {
            print("❌ Could not create conversation for agent: \(agent.name)")
            return
        }

        // Save both messages
        _ = await conversationService.saveMessage(
            conversationId: conversation.id,
            content: userMessage.content,
            isFromUser: true
        )

        _ = await conversationService.saveMessage(
            conversationId: conversation.id,
            content: aiMessage.content,
            isFromUser: false
        )
    }

    private func getAgentDatabaseId(for agentName: String) -> UUID? {
        // Map agent names to their database IDs
        switch agentName {
        case "Dr. Nutrition":
            return UUID(uuidString: "550e8400-e29b-41d4-a716-************")
        case "Health and Emergency":
            return UUID(uuidString: "550e8400-e29b-41d4-a716-************")
        case "Trainer Pro":
            return UUID(uuidString: "550e8400-e29b-41d4-a716-************")
        case "Style Guru":
            return UUID(uuidString: "550e8400-e29b-41d4-a716-************")
        case "Shopping Assistant":
            return UUID(uuidString: "550e8400-e29b-41d4-a716-************")
        case "Pet Master":
            return UUID(uuidString: "550e8400-e29b-41d4-a716-************")
        case "Pet Insurance Advisor":
            return UUID(uuidString: "550e8400-e29b-41d4-a716-************")
        default:
            return nil
        }
    }

    // Load conversation history from Supabase
    private func loadConversationHistory() async {
        do {
            let userId = getCurrentUserId()
            let response: [ConversationHistoryData] = try await supabase
                .from("conversation_history")
                .select()
                .eq("user_id", value: userId.uuidString)
                .order("timestamp", ascending: false)
                .limit(100)
                .execute()
                .value

            // Organize by agent
            for conversation in response {
                guard let agentId = UUID(uuidString: conversation.agentId) else { continue }

                let userMsg = ChatMessage(content: conversation.userMessage, isFromUser: true, agentId: agentId)
                let aiMsg = ChatMessage(content: conversation.aiResponse, isFromUser: false, agentId: agentId)

                if conversationHistory[conversation.agentId] == nil {
                    conversationHistory[conversation.agentId] = []
                }
                conversationHistory[conversation.agentId]?.append(contentsOf: [userMsg, aiMsg])
            }
        } catch {
            print("Failed to load conversation history: \(error)")
        }
    }

    private func getCurrentUserId() -> UUID {
        return UUID(uuidString: "550e8400-e29b-41d4-a716-************") ?? UUID() // Development mode
    }
}

// MARK: - Data Models for Supabase

struct ConversationHistoryData: Codable {
    let id: String
    let userId: String
    let agentId: String
    let userMessage: String
    let aiResponse: String
    let timestamp: Date
}
