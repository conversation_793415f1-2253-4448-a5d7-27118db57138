//
//  PetNetworkService.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import Foundation
import SwiftUI
import CoreLocation

class PetNetworkService: ObservableObject {
    static let shared = PetNetworkService()

    @Published var nearbyPets: [PetProfile] = []
    @Published var littermates: [PetProfile] = []
    @Published var breedCommunity: [PetProfile] = []
    @Published var friendRequests: [FriendRequest] = []
    @Published var petFriends: [PetFriendship] = []
    @Published var communityPosts: [CommunityPost] = []

    private let supabaseService = SupabaseService.shared

    private init() {}

    // MARK: - Pet Discovery

    func discoverNearbyPets(location: CLLocation, radius: Double = 10.0) async throws {
        // In a real implementation, this would use location-based queries
        let mockNearbyPets = await loadNearbyPetsFromDatabase(location: location)

        await MainActor.run {
            self.nearbyPets = mockNearbyPets
        }
    }

    func findLittermates(for pet: Pet) async throws {
        guard let lineageData = pet.lineageData else { return }

        // Parse lineage data to find potential littermates
        let littermates = try await searchLittermates(lineageData: lineageData)

        await MainActor.run {
            self.littermates = littermates
        }
    }

    func findBreedCommunity(for breed: String) async throws {
        let breedPets = try await searchByBreed(breed: breed)

        await MainActor.run {
            self.breedCommunity = breedPets
        }
    }

    // MARK: - Friend Management

    func sendFriendRequest(to petId: UUID, from currentPetId: UUID, message: String = "") async throws {
        let _ = FriendRequest(
            id: UUID(),
            fromPetId: currentPetId,
            toPetId: petId,
            message: message,
            status: .pending,
            createdAt: Date()
        )

        // In real implementation, save to Supabase
        await MainActor.run {
            // Add to mock data for demo
        }
    }

    func acceptFriendRequest(_ request: FriendRequest) async throws {
        let friendship = PetFriendship(
            id: UUID(),
            pet1Id: request.fromPetId,
            pet2Id: request.toPetId,
            connectionType: .friend,
            establishedAt: Date(),
            sharedMemories: []
        )

        await MainActor.run {
            self.petFriends.append(friendship)
            self.friendRequests.removeAll { $0.id == request.id }
        }
    }

    func declineFriendRequest(_ request: FriendRequest) async throws {
        await MainActor.run {
            self.friendRequests.removeAll { $0.id == request.id }
        }
    }

    // MARK: - Community Features

    func shareMemoryToCommunity(_ memory: Memory, caption: String, visibility: PostVisibility) async throws {
        let post = CommunityPost(
            id: UUID(),
            authorId: memory.pet?.ownerID ?? "",
            petId: memory.pet?.id ?? UUID(),
            memoryId: memory.id,
            caption: caption,
            visibility: visibility,
            likes: 0,
            comments: [],
            createdAt: Date()
        )

        await MainActor.run {
            self.communityPosts.insert(post, at: 0)
        }
    }

    func loadCommunityFeed(for userId: String) async throws {
        // Load posts from friends and community
        let posts = try await fetchCommunityPosts(userId: userId)

        await MainActor.run {
            self.communityPosts = posts
        }
    }

    func likePost(_ postId: UUID) async throws {
        await MainActor.run {
            if let index = self.communityPosts.firstIndex(where: { $0.id == postId }) {
                self.communityPosts[index].likes += 1
            }
        }
    }

    func addComment(to postId: UUID, comment: String, authorId: String) async throws {
        let newComment = PostComment(
            id: UUID(),
            postId: postId,
            authorId: authorId,
            content: comment,
            createdAt: Date()
        )

        await MainActor.run {
            if let index = self.communityPosts.firstIndex(where: { $0.id == postId }) {
                self.communityPosts[index].comments.append(newComment)
            }
        }
    }

    // MARK: - Playdates & Events

    func createPlaydate(
        title: String,
        description: String,
        location: String,
        dateTime: Date,
        maxParticipants: Int,
        invitedPets: [UUID]
    ) async throws -> Playdate {
        let playdate = Playdate(
            id: UUID(),
            title: title,
            description: description,
            location: location,
            dateTime: dateTime,
            maxParticipants: maxParticipants,
            organizerId: supabaseService.currentUser?.id ?? "",
            participants: [],
            status: .upcoming,
            createdAt: Date()
        )

        // Send invitations
        for petId in invitedPets {
            try await sendPlaydateInvitation(playdateId: playdate.id, petId: petId)
        }

        return playdate
    }

    func joinPlaydate(_ playdateId: UUID, petId: UUID) async throws {
        // Add pet to playdate participants
        // In real implementation, update Supabase
    }

    // MARK: - Memorial Support

    func createMemorialTribute(for pet: Pet, message: String, isPublic: Bool) async throws {
        let _ = NetworkMemorialTribute(
            id: UUID(),
            petId: pet.id,
            authorId: pet.ownerID,
            message: message,
            isPublic: isPublic,
            condolences: [],
            createdAt: Date()
        )

        // Save to database and notify community if public
    }

    func sendCondolence(to tributeId: UUID, message: String, authorId: String) async throws {
        let _ = NetworkCondolence(
            id: UUID(),
            tributeId: tributeId,
            authorId: authorId,
            message: message,
            createdAt: Date()
        )

        // Save condolence message
    }

    // MARK: - Private Helper Methods

    private func loadNearbyPetsFromDatabase(location: CLLocation) async -> [PetProfile] {
        // TODO: Implement real location-based pet discovery
        // This would query the database for pets within a certain radius
        // For now, return empty array until feature is fully implemented
        print("🔍 Loading nearby pets from database for location: \(location)")
        return []
    }

    private func searchLittermates(lineageData: String) async throws -> [PetProfile] {
        // Parse lineage data and search for pets with matching parent information
        // This would involve complex genetic matching algorithms
        return []
    }

    private func searchByBreed(breed: String) async throws -> [PetProfile] {
        // Search for pets of the same breed in the community
        return generateMockBreedCommunity(breed: breed)
    }

    private func generateMockBreedCommunity(breed: String) -> [PetProfile] {
        return [
            PetProfile(
                id: UUID(),
                name: "Charlie",
                breed: breed,
                age: 4,
                profileImageURL: nil,
                ownerName: "Alex Thompson",
                distance: 5.0,
                isOnline: true
            ),
            PetProfile(
                id: UUID(),
                name: "Bella",
                breed: breed,
                age: 2,
                profileImageURL: nil,
                ownerName: "Jessica Wilson",
                distance: 8.5,
                isOnline: false
            )
        ]
    }

    private func fetchCommunityPosts(userId: String) async throws -> [CommunityPost] {
        // Fetch posts from Supabase based on user's network
        return generateMockCommunityPosts()
    }

    private func generateMockCommunityPosts() -> [CommunityPost] {
        return [
            CommunityPost(
                id: UUID(),
                authorId: "user1",
                petId: UUID(),
                memoryId: UUID(),
                caption: "Beautiful day at the park with Luna! 🌞",
                visibility: .friends,
                likes: 12,
                comments: [
                    PostComment(
                        id: UUID(),
                        postId: UUID(),
                        authorId: "user2",
                        content: "So cute! 😍",
                        createdAt: Date().addingTimeInterval(-3600)
                    )
                ],
                createdAt: Date().addingTimeInterval(-7200)
            )
        ]
    }

    private func sendPlaydateInvitation(playdateId: UUID, petId: UUID) async throws {
        // Send playdate invitation notification
    }
}

// MARK: - Data Models

struct PetProfile: Identifiable {
    let id: UUID
    let name: String
    let breed: String
    let age: Int
    let profileImageURL: String?
    let ownerName: String
    let distance: Double // in kilometers
    let isOnline: Bool
}

struct FriendRequest: Identifiable {
    let id: UUID
    let fromPetId: UUID
    let toPetId: UUID
    let message: String
    let status: FriendRequestStatus
    let createdAt: Date
}

enum FriendRequestStatus: String, CaseIterable {
    case pending = "pending"
    case accepted = "accepted"
    case declined = "declined"
}

struct PetFriendship: Identifiable {
    let id: UUID
    let pet1Id: UUID
    let pet2Id: UUID
    let connectionType: ConnectionType
    let establishedAt: Date
    var sharedMemories: [UUID]
}

enum ConnectionType: String, CaseIterable {
    case friend = "friend"
    case littermate = "littermate"
    case playmate = "playmate"
    case neighbor = "neighbor"
}

struct CommunityPost: Identifiable {
    let id: UUID
    let authorId: String
    let petId: UUID
    let memoryId: UUID
    let caption: String
    let visibility: PostVisibility
    var likes: Int
    var comments: [PostComment]
    let createdAt: Date
}

enum PostVisibility: String, CaseIterable {
    case `public` = "public"
    case friends = "friends"
    case `private` = "private"
}

struct PostComment: Identifiable {
    let id: UUID
    let postId: UUID
    let authorId: String
    let content: String
    let createdAt: Date
}

struct Playdate: Identifiable {
    let id: UUID
    let title: String
    let description: String
    let location: String
    let dateTime: Date
    let maxParticipants: Int
    let organizerId: String
    var participants: [UUID]
    let status: PlaydateStatus
    let createdAt: Date
}

enum PlaydateStatus: String, CaseIterable {
    case upcoming = "upcoming"
    case ongoing = "ongoing"
    case completed = "completed"
    case cancelled = "cancelled"
}

struct NetworkMemorialTribute: Identifiable {
    let id: UUID
    let petId: UUID
    let authorId: String
    let message: String
    let isPublic: Bool
    var condolences: [NetworkCondolence]
    let createdAt: Date
}

struct NetworkCondolence: Identifiable {
    let id: UUID
    let tributeId: UUID
    let authorId: String
    let message: String
    let createdAt: Date
}
