//
//  RealDataService.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import Foundation
import Supabase
import SwiftUI

@MainActor
class RealDataService: ObservableObject {

    // MARK: - Properties

    private let supabaseService = SupabaseService.shared
    private var supabase: SupabaseClient {
        return supabaseService.client
    }

    @Published var pets: [Pet] = []
    @Published var memories: [Memory] = []
    @Published var memorialGardens: [MemorialGarden] = []
    @Published var isLoading = false
    @Published var errorMessage: String?

    // MARK: - Initialization

    init() {
        Task {
            await loadInitialData()
        }
    }

    // MARK: - Initial Data Loading

    func loadInitialData() async {
        isLoading = true
        errorMessage = nil

        // Get the authenticated user's ID (must be authenticated first)
        guard let currentUserId = getCurrentUserId() else {
            print("❌ No authenticated user found - cannot load data")
            errorMessage = "Please sign in to access your data"
            isLoading = false
            return
        }

        // Ensure user profile exists in database
        await ensureUserProfileExists(userId: currentUserId)

        // Load user's data with delay to prevent network conflicts
        await loadUserPets(userId: currentUserId)

        // Small delay to prevent concurrent request cancellations
        try? await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds

        await loadUserMemories(userId: currentUserId)

        // Another small delay
        try? await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds

        await loadMemorialGardens()

        isLoading = false
    }

    func refreshAllData() async {
        guard let currentUserId = getCurrentUserId() else {
            print("❌ No authenticated user found - cannot refresh data")
            errorMessage = "Please sign in to access your data"
            return
        }

        isLoading = true
        errorMessage = nil

        // Refresh all user data
        await loadUserPets(userId: currentUserId)
        await loadUserMemories(userId: currentUserId)
        await loadMemorialGardens()

        isLoading = false
    }

    // MARK: - User Profile Management

    private func ensureUserProfileExists(userId: UUID) async {
        do {
            // Get the authenticated user from Supabase
            guard let authUser = supabase.auth.currentUser else {
                print("❌ No authenticated user found")
                return
            }

            // Check if user profile exists in database
            let existingUsers: [DatabaseUser] = try await supabase
                .from("users")
                .select()
                .eq("id", value: userId.uuidString)
                .execute()
                .value

            if existingUsers.isEmpty {
                // Create user profile with real authenticated user data
                let userEmail = authUser.email ?? "<EMAIL>"
                let userName = authUser.userMetadata["full_name"]?.stringValue ?? "User"

                let newUser = DatabaseUser(
                    id: userId,
                    email: userEmail,
                    fullName: userName,
                    subscriptionTier: "free", // Start with free tier
                    onboardingCompleted: false // Let user complete onboarding
                )

                try await supabase
                    .from("users")
                    .insert(newUser)
                    .execute()

                print("✅ User profile created successfully for: \(userEmail)")
            } else {
                print("✅ User profile already exists for: \(userId)")
            }
        } catch {
            print("❌ Error ensuring user profile exists: \(error)")
            errorMessage = "Failed to create user profile: \(error.localizedDescription)"
        }
    }

    // MARK: - Pet Management

    func loadUserPets(userId: UUID) async {
        do {
            let databasePets: [DatabasePet] = try await supabase
                .from("pets")
                .select()
                .eq("user_id", value: userId.uuidString)
                .eq("is_active", value: true)
                .order("created_at", ascending: false)
                .execute()
                .value

            // Convert database pets to app pets
            pets = databasePets.compactMap { dbPet in
                // Calculate age from birth date
                let age: Int
                if let birthDate = dbPet.birthDate {
                    let calendar = Calendar.current
                    let ageComponents = calendar.dateComponents([.year], from: birthDate, to: Date())
                    age = ageComponents.year ?? 0
                } else {
                    age = 0
                }

                // Create Pet using the main initializer
                let pet = Pet(
                    name: dbPet.name,
                    species: dbPet.species,
                    breed: dbPet.breed ?? "Mixed",
                    age: age,
                    dateOfBirth: dbPet.birthDate,
                    profileImageURL: dbPet.profileImageUrl,
                    bio: "",
                    ownerID: dbPet.userId.uuidString
                )

                // Set additional properties
                pet.id = dbPet.id
                pet.weight = dbPet.weight
                pet.activityLevel = dbPet.activityLevel ?? "moderate"
                pet.personalityTraits = dbPet.personalityTraits ?? []
                pet.medications = dbPet.medications ?? []
                pet.vaccinations = dbPet.vaccinations ?? []
                pet.healthAlerts = dbPet.healthAlerts ?? []
                pet.aiRecommendations = dbPet.aiRecommendations ?? []
                pet.lastCheckupDate = dbPet.lastCheckupDate
                pet.chronicConditions = dbPet.healthConditions ?? []

                return pet
            }

            print("✅ Loaded \(pets.count) pets from database")

        } catch {
            // Don't show error for cancelled requests (common during app startup)
            if !error.localizedDescription.contains("cancelled") {
                errorMessage = "Failed to load pets: \(error.localizedDescription)"
            }
            print("❌ Error loading pets: \(error)")
        }
    }

    func createPet(_ pet: Pet, userId: UUID) async -> Bool {

        do {
            let databasePet = DatabasePet(
                id: pet.id,
                userId: userId,
                name: pet.name,
                species: pet.species,
                breed: pet.breed,
                birthDate: pet.dateOfBirth,
                adoptionDate: Date(), // Use current date as adoption date
                weight: pet.weight,
                activityLevel: pet.activityLevel,
                personalityTraits: pet.personalityTraits,
                healthConditions: pet.chronicConditions,
                medications: pet.medications,
                vaccinations: pet.vaccinations,
                healthAlerts: pet.healthAlerts,
                aiRecommendations: pet.aiRecommendations,
                profileImageUrl: pet.profileImageURL,
                lastCheckupDate: pet.lastCheckupDate
            )

            try await supabase
                .from("pets")
                .insert(databasePet)
                .execute()

            // Reload pets to get updated list
            await loadUserPets(userId: userId)

            print("✅ Pet created successfully: \(pet.name)")
            return true

        } catch {
            let friendlyMessage = "Unable to save your pet. Please check your internet connection and try again."
            errorMessage = friendlyMessage
            print("❌ Error creating pet: \(error)")
            print("❌ Detailed error: \(error)")
            return false
        }
    }

    func updatePet(_ pet: Pet, userId: UUID) async -> Bool {

        do {
            let databasePet = DatabasePet(
                id: pet.id,
                userId: userId,
                name: pet.name,
                species: pet.species,
                breed: pet.breed,
                birthDate: pet.dateOfBirth,
                adoptionDate: Date(), // Use current date as adoption date
                weight: pet.weight,
                activityLevel: pet.activityLevel,
                personalityTraits: pet.personalityTraits,
                healthConditions: pet.chronicConditions,
                medications: pet.medications,
                vaccinations: pet.vaccinations,
                healthAlerts: pet.healthAlerts,
                aiRecommendations: pet.aiRecommendations,
                profileImageUrl: pet.profileImageURL,
                lastCheckupDate: pet.lastCheckupDate
            )

            try await supabase
                .from("pets")
                .update(databasePet)
                .eq("id", value: pet.id.uuidString)
                .execute()

            // Reload pets to get updated list
            await loadUserPets(userId: userId)

            print("✅ Pet updated successfully: \(pet.name)")
            return true

        } catch {
            errorMessage = "Failed to update pet: \(error.localizedDescription)"
            print("❌ Error updating pet: \(error)")
            return false
        }
    }

    func deletePet(_ pet: Pet) async -> Bool {

        do {
            try await supabase
                .from("pets")
                .update(["is_active": false])
                .eq("id", value: pet.id.uuidString)
                .execute()

            // Remove from local array on main thread
            await MainActor.run {
                pets.removeAll { $0.id == pet.id }
                objectWillChange.send()
            }

            print("✅ Pet deleted successfully: \(pet.name)")
            return true

        } catch {
            await MainActor.run {
                errorMessage = "Failed to delete pet: \(error.localizedDescription)"
            }
            print("❌ Error deleting pet: \(error)")
            return false
        }
    }

    // MARK: - Memory Management

    func loadUserMemories(userId: UUID) async {
        print("🔄 Loading memories for user: \(userId.uuidString)")

        do {
            let databaseMemories: [DatabaseMemory] = try await supabase
                .from("memories")
                .select()
                .eq("user_id", value: userId.uuidString)
                .order("created_at", ascending: false)
                .execute()
                .value

            print("📊 Found \(databaseMemories.count) memories in database")

            // Convert database memories to app memories
            let convertedMemories: [Memory] = databaseMemories.compactMap { dbMemory in
                guard let memoryType = MemoryType(rawValue: dbMemory.memoryType) else {
                    print("⚠️ Unknown memory type: \(dbMemory.memoryType)")
                    return nil
                }

                let memory = Memory(
                    title: dbMemory.title,
                    content: dbMemory.content ?? "",
                    type: memoryType,
                    mediaURL: dbMemory.mediaUrl,
                    thumbnailURL: dbMemory.thumbnailUrl,
                    duration: dbMemory.duration,
                    milestone: dbMemory.aiMilestone,
                    sentiment: dbMemory.aiSentiment,
                    tags: dbMemory.aiTags ?? [],
                    isPublic: dbMemory.isPublic ?? false,
                    isFavorite: dbMemory.isFavorite ?? false
                )

                memory.id = dbMemory.id
                memory.createdAt = dbMemory.createdAt ?? Date()
                memory.updatedAt = dbMemory.updatedAt ?? Date()

                return memory
            }

            // Update memories on main thread
            await MainActor.run {
                self.memories = convertedMemories
            }

            print("✅ Successfully loaded \(convertedMemories.count) memories")

        } catch {
            await MainActor.run {
                // Don't show error for cancelled requests (common during app startup)
                if !error.localizedDescription.contains("cancelled") {
                    self.errorMessage = "Failed to load memories: \(error.localizedDescription)"
                }
            }
            print("❌ Error loading memories: \(error)")
        }
    }

    func createMemory(_ memory: Memory, petId: UUID, userId: UUID) async -> Bool {

        do {
            let databaseMemory = DatabaseMemory(
                id: memory.id,
                petId: petId,
                userId: userId,
                title: memory.title,
                content: memory.content,
                memoryType: memory.type.rawValue,
                mediaUrl: memory.mediaURL,
                thumbnailUrl: memory.thumbnailURL,
                duration: memory.duration,
                aiTags: memory.tags,
                aiSentiment: memory.sentiment,
                aiMilestone: memory.milestone,
                isPublic: memory.isPublic,
                isFavorite: memory.isFavorite
            )

            try await supabase
                .from("memories")
                .insert(databaseMemory)
                .execute()

            // Reload memories to get updated list
            await loadUserMemories(userId: userId)

            print("✅ Memory created successfully: \(memory.title)")
            return true

        } catch {
            errorMessage = "Failed to create memory: \(error.localizedDescription)"
            print("❌ Error creating memory: \(error)")
            return false
        }
    }

    func updateMemory(_ memory: Memory, userId: UUID) async -> Bool {
        do {
            // Get the original memory's pet_id from database to preserve the relationship
            let originalPetId: UUID
            if let petId = memory.pet?.id {
                originalPetId = petId
            } else {
                // Fetch the original pet_id from the database
                let response: [DatabaseMemory] = try await supabase
                    .from("memories")
                    .select("pet_id")
                    .eq("id", value: memory.id.uuidString)
                    .eq("user_id", value: userId.uuidString)
                    .execute()
                    .value

                guard let originalMemory = response.first else {
                    print("❌ Could not find original memory to get pet_id")
                    return false
                }
                originalPetId = originalMemory.petId
            }

            let databaseMemory = DatabaseMemory(
                id: memory.id,
                petId: originalPetId, // Use the original pet ID to maintain relationship
                userId: userId,
                title: memory.title,
                content: memory.content,
                memoryType: memory.type.rawValue,
                mediaUrl: memory.mediaURL,
                thumbnailUrl: memory.thumbnailURL,
                duration: memory.duration,
                aiTags: memory.tags,
                aiSentiment: memory.sentiment,
                aiMilestone: memory.milestone,
                isPublic: memory.isPublic,
                isFavorite: memory.isFavorite
            )

            try await supabase
                .from("memories")
                .update(databaseMemory)
                .eq("id", value: memory.id.uuidString)
                .eq("user_id", value: userId.uuidString)
                .execute()

            // Reload memories to get updated list
            await loadUserMemories(userId: userId)

            print("✅ Memory updated successfully: \(memory.title)")
            return true

        } catch {
            errorMessage = "Failed to update memory: \(error.localizedDescription)"
            print("❌ Error updating memory: \(error)")
            return false
        }
    }

    func deleteMemory(_ memoryId: UUID, userId: UUID) async -> Bool {
        do {
            try await supabase
                .from("memories")
                .delete()
                .eq("id", value: memoryId.uuidString)
                .eq("user_id", value: userId.uuidString)
                .execute()

            // Remove from local memories array
            await MainActor.run {
                self.memories.removeAll { $0.id == memoryId }
            }

            print("✅ Memory deleted successfully")
            return true

        } catch {
            errorMessage = "Failed to delete memory: \(error.localizedDescription)"
            print("❌ Error deleting memory: \(error)")
            return false
        }
    }

    // MARK: - Memorial Gardens

    func loadMemorialGardens() async {
        do {
            let databaseMemorials: [DatabaseMemorialGarden] = try await supabase
                .from("memorial_gardens")
                .select()
                .eq("is_public", value: true)
                .order("created_at", ascending: false)
                .limit(20)
                .execute()
                .value

            // Convert to app memorial gardens
            memorialGardens = databaseMemorials.compactMap { dbMemorial in
                guard let theme = MemorialTheme(rawValue: dbMemorial.theme ?? "peaceful") else {
                    return nil
                }

                return MemorialGarden(
                    id: dbMemorial.id,
                    petId: dbMemorial.petId,
                    petName: dbMemorial.petName,
                    petImageURL: dbMemorial.petImageUrl,
                    dateOfPassing: dbMemorial.dateOfPassing,
                    memorialMessage: dbMemorial.memorialMessage ?? "",
                    theme: theme,
                    isPublic: dbMemorial.isPublic ?? false,
                    createdBy: dbMemorial.userId.uuidString,
                    tributeCount: dbMemorial.tributeCount ?? 0,
                    visitCount: dbMemorial.visitCount ?? 0,
                    createdAt: dbMemorial.createdAt ?? Date()
                )
            }

            print("✅ Loaded \(memorialGardens.count) memorial gardens from database")

        } catch {
            // Don't show error for cancelled requests (common during app startup)
            if !error.localizedDescription.contains("cancelled") {
                errorMessage = "Failed to load memorial gardens: \(error.localizedDescription)"
            }
            print("❌ Error loading memorial gardens: \(error)")
        }
    }

    // MARK: - Helper Methods

    func getCurrentUserId() -> UUID? {
        // Return authenticated user's ID from SupabaseService
        if let currentUser = supabaseService.currentUser {
            return UUID(uuidString: currentUser.id)
        }
        // Fallback to auth user if SupabaseService user is not loaded yet
        if let authUser = supabase.auth.currentUser {
            return UUID(uuidString: authUser.id.uuidString)
        }
        // No fallback - user must be authenticated
        return nil
    }
}
