//
//  PetHealthAnalyticsService.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 12/24/24.
//

import Foundation
import SwiftUI
import Combine

@MainActor
class PetHealthAnalyticsService: ObservableObject {
    static let shared = PetHealthAnalyticsService()
    
    @Published var healthData: [HealthDataPoint] = []
    @Published var activityData: [ActivityDataPoint] = []
    @Published var medicationData: [MedicationDataPoint] = []
    @Published var isLoading = false
    
    private init() {
        generateSampleData()
    }
    
    // MARK: - Data Loading
    
    func loadHealthData(for pet: Pet) {
        isLoading = true
        
        // Simulate loading real data
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            self.generateSampleData()
            self.isLoading = false
        }
    }
    
    // MARK: - Chart Data Methods
    
    func getChartData(for metric: HealthMetric, timeRange: TimeRange) -> [HealthDataPoint] {
        let filteredData = healthData.filter { dataPoint in
            dataPoint.metric == metric && 
            dataPoint.date >= timeRange.startDate
        }
        
        return filteredData.sorted { $0.date < $1.date }
    }
    
    func getLatestValue(for metric: HealthMetric) -> String? {
        let latestData = healthData
            .filter { $0.metric == metric }
            .sorted { $0.date > $1.date }
            .first
        
        guard let data = latestData else { return nil }
        
        switch metric {
        case .weight:
            return String(format: "%.1f lbs", data.value)
        case .temperature:
            return String(format: "%.1f°F", data.value)
        case .heartRate:
            return String(format: "%.0f BPM", data.value)
        case .activity:
            return String(format: "%.0f min", data.value)
        case .sleep:
            return String(format: "%.1f hrs", data.value)
        case .appetite:
            return String(format: "%.0f%%", data.value)
        }
    }
    
    func getTrend(for metric: HealthMetric) -> HealthTrend {
        let recentData = healthData
            .filter { $0.metric == metric }
            .sorted { $0.date > $1.date }
            .prefix(7) // Last 7 data points
        
        guard recentData.count >= 2 else { return .stable }
        
        let values = Array(recentData.map { $0.value })
        let firstHalf = values.suffix(values.count / 2)
        let secondHalf = values.prefix(values.count / 2)
        
        let firstAvg = firstHalf.reduce(0, +) / Double(firstHalf.count)
        let secondAvg = secondHalf.reduce(0, +) / Double(secondHalf.count)
        
        let percentChange = abs((secondAvg - firstAvg) / firstAvg) * 100
        
        if percentChange < 5 {
            return .stable
        } else if secondAvg > firstAvg {
            return .improving
        } else {
            return .declining
        }
    }
    
    func getActivityData(for pet: Pet, timeRange: TimeRange) -> [ActivityDataPoint] {
        return activityData.filter { $0.date >= timeRange.startDate }
    }
    
    func getMedicationData(for pet: Pet, timeRange: TimeRange) -> [MedicationDataPoint] {
        return medicationData
    }
    
    func getHealthInsights(for pet: Pet) -> [HealthInsight] {
        return [
            HealthInsight(
                id: UUID(),
                category: .weight,
                title: "Weight Trend",
                description: "Stable weight over the past month",
                severity: .low,
                actionItems: ["Continue current diet", "Monitor weight weekly"],
                confidence: 0.9
            ),
            HealthInsight(
                id: UUID(),
                category: .exercise,
                title: "Activity Level",
                description: "Above average activity this week",
                severity: .low,
                actionItems: ["Maintain current exercise routine"],
                confidence: 0.85
            ),
            HealthInsight(
                id: UUID(),
                category: .general,
                title: "Sleep Quality",
                description: "Consistent sleep patterns",
                severity: .low,
                actionItems: ["Continue current sleep routine"],
                confidence: 0.8
            ),
            HealthInsight(
                id: UUID(),
                category: .general,
                title: "Appetite",
                description: "Normal eating behavior",
                severity: .low,
                actionItems: ["Continue current feeding schedule"],
                confidence: 0.9
            )
        ]
    }
    
    // MARK: - Sample Data Generation
    
    private func generateSampleData() {
        healthData = generateHealthData()
        activityData = generateActivityData()
        medicationData = generateMedicationData()
    }
    
    private func generateHealthData() -> [HealthDataPoint] {
        var data: [HealthDataPoint] = []
        let calendar = Calendar.current
        let now = Date()
        
        // Generate 30 days of data
        for i in 0..<30 {
            guard let date = calendar.date(byAdding: .day, value: -i, to: now) else { continue }
            
            // Weight data (45-50 lbs with slight variation)
            let weight = 47.5 + Double.random(in: -2.5...2.5)
            data.append(HealthDataPoint(date: date, value: weight, metric: .weight))
            
            // Temperature data (100-102°F)
            let temperature = 101.0 + Double.random(in: -1.0...1.0)
            data.append(HealthDataPoint(date: date, value: temperature, metric: .temperature))
            
            // Heart rate data (60-120 BPM)
            let heartRate = 90.0 + Double.random(in: -30.0...30.0)
            data.append(HealthDataPoint(date: date, value: heartRate, metric: .heartRate))
            
            // Activity data (30-120 minutes)
            let activity = 75.0 + Double.random(in: -45.0...45.0)
            data.append(HealthDataPoint(date: date, value: activity, metric: .activity))
            
            // Sleep data (8-12 hours)
            let sleep = 10.0 + Double.random(in: -2.0...2.0)
            data.append(HealthDataPoint(date: date, value: sleep, metric: .sleep))
            
            // Appetite data (70-100%)
            let appetite = 85.0 + Double.random(in: -15.0...15.0)
            data.append(HealthDataPoint(date: date, value: appetite, metric: .appetite))
        }
        
        return data
    }
    
    private func generateActivityData() -> [ActivityDataPoint] {
        var data: [ActivityDataPoint] = []
        let calendar = Calendar.current
        let now = Date()
        
        for i in 0..<30 {
            guard let date = calendar.date(byAdding: .day, value: -i, to: now) else { continue }
            
            let duration = Double.random(in: 30...120)
            data.append(ActivityDataPoint(date: date, duration: duration))
        }
        
        return data
    }
    
    private func generateMedicationData() -> [MedicationDataPoint] {
        return [
            MedicationDataPoint(
                name: "Heartworm",
                adherencePercentage: 95.0,
                color: .blue
            ),
            MedicationDataPoint(
                name: "Flea & Tick",
                adherencePercentage: 88.0,
                color: .green
            ),
            MedicationDataPoint(
                name: "Vitamins",
                adherencePercentage: 92.0,
                color: .orange
            )
        ]
    }
}

// MARK: - Data Models

struct HealthDataPoint: Identifiable {
    let id = UUID()
    let date: Date
    let value: Double
    let metric: HealthMetric
}

struct ActivityDataPoint: Identifiable {
    let id = UUID()
    let date: Date
    let duration: Double // minutes
}

struct MedicationDataPoint: Identifiable {
    let id = UUID()
    let name: String
    let adherencePercentage: Double
    let color: Color
}

// HealthInsight is defined in Models/AIModels.swift

// MARK: - Enums

enum HealthMetric: String, CaseIterable {
    case weight = "weight"
    case temperature = "temperature"
    case heartRate = "heartRate"
    case activity = "activity"
    case sleep = "sleep"
    case appetite = "appetite"
    
    var displayName: String {
        switch self {
        case .weight: return "Weight"
        case .temperature: return "Temperature"
        case .heartRate: return "Heart Rate"
        case .activity: return "Activity"
        case .sleep: return "Sleep"
        case .appetite: return "Appetite"
        }
    }
    
    var icon: String {
        switch self {
        case .weight: return "scalemass.fill"
        case .temperature: return "thermometer"
        case .heartRate: return "heart.fill"
        case .activity: return "figure.walk"
        case .sleep: return "moon.fill"
        case .appetite: return "bowl.fill"
        }
    }
    
    var color: Color {
        switch self {
        case .weight: return .blue
        case .temperature: return .red
        case .heartRate: return .pink
        case .activity: return .green
        case .sleep: return .purple
        case .appetite: return .orange
        }
    }
}

enum TimeRange: String, CaseIterable {
    case week = "week"
    case month = "month"
    case quarter = "quarter"
    case year = "year"
    
    var displayName: String {
        switch self {
        case .week: return "Week"
        case .month: return "Month"
        case .quarter: return "3 Months"
        case .year: return "Year"
        }
    }
    
    var startDate: Date {
        let calendar = Calendar.current
        let now = Date()
        
        switch self {
        case .week:
            return calendar.date(byAdding: .day, value: -7, to: now) ?? now
        case .month:
            return calendar.date(byAdding: .month, value: -1, to: now) ?? now
        case .quarter:
            return calendar.date(byAdding: .month, value: -3, to: now) ?? now
        case .year:
            return calendar.date(byAdding: .year, value: -1, to: now) ?? now
        }
    }
    
    var axisStride: Calendar.Component {
        switch self {
        case .week: return .day
        case .month: return .weekOfYear
        case .quarter: return .month
        case .year: return .month
        }
    }
    
    var dateFormat: Date.FormatStyle {
        switch self {
        case .week: return .dateTime.weekday(.abbreviated)
        case .month: return .dateTime.day()
        case .quarter: return .dateTime.month(.abbreviated)
        case .year: return .dateTime.month(.abbreviated)
        }
    }
}

enum HealthTrend {
    case improving
    case stable
    case declining
    
    var displayName: String {
        switch self {
        case .improving: return "Improving"
        case .stable: return "Stable"
        case .declining: return "Declining"
        }
    }
    
    var icon: String {
        switch self {
        case .improving: return "arrow.up.right"
        case .stable: return "arrow.right"
        case .declining: return "arrow.down.right"
        }
    }
    
    var color: Color {
        switch self {
        case .improving: return .green
        case .stable: return .blue
        case .declining: return .red
        }
    }
}
