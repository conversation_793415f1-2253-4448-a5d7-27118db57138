//
//  SupabaseStorageService.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 6/6/25.
//

import Foundation
import Supabase
import SwiftUI

@MainActor
class SupabaseStorageService: ObservableObject {
    static let shared = SupabaseStorageService()
    
    private let supabaseService = SupabaseService.shared
    private var supabase: SupabaseClient {
        return supabaseService.client
    }
    
    // Storage bucket names (matching actual Supabase buckets)
    private let memoryImagesBucket = "memory-media"
    private let memoryVideosBucket = "memory-media"
    private let memoryThumbnailsBucket = "thumbnails"
    private let petProfilesBucket = "pet-photos"
    
    @Published var isUploading = false
    @Published var uploadProgress: Double = 0.0
    @Published var error: String?
    
    private init() {}
    
    // MARK: - Memory Media Upload
    
    func uploadMemoryImage(_ imageData: Data, memoryId: UUID, userId: UUID) async -> String? {
        print("🔄 SupabaseStorageService: Uploading memory image - Size: \(imageData.count) bytes")
        let result = await uploadFile(
            data: imageData,
            bucket: memoryImagesBucket,
            fileName: "\(userId.uuidString.lowercased())/\(memoryId.uuidString.lowercased()).jpg",
            contentType: "image/jpeg"
        )
        print("📤 SupabaseStorageService: Memory image upload result: \(result ?? "nil")")
        return result
    }
    
    func uploadMemoryVideo(_ videoData: Data, memoryId: UUID, userId: UUID) async -> String? {
        await uploadFile(
            data: videoData,
            bucket: memoryVideosBucket,
            fileName: "\(userId.uuidString.lowercased())/\(memoryId.uuidString.lowercased()).mp4",
            contentType: "video/mp4"
        )
    }
    
    func uploadMemoryThumbnail(_ thumbnailData: Data, memoryId: UUID, userId: UUID) async -> String? {
        print("🔄 SupabaseStorageService: Uploading memory thumbnail - Size: \(thumbnailData.count) bytes")
        let result = await uploadFile(
            data: thumbnailData,
            bucket: memoryThumbnailsBucket,
            fileName: "\(userId.uuidString.lowercased())/\(memoryId.uuidString.lowercased())_thumb.jpg",
            contentType: "image/jpeg"
        )
        print("📤 SupabaseStorageService: Memory thumbnail upload result: \(result ?? "nil")")
        return result
    }
    
    func uploadPetProfile(_ imageData: Data, petId: UUID, userId: UUID) async -> String? {
        await uploadFile(
            data: imageData,
            bucket: petProfilesBucket,
            fileName: "\(userId.uuidString.lowercased())/\(petId.uuidString.lowercased()).jpg",
            contentType: "image/jpeg"
        )
    }
    
    // MARK: - Generic File Upload
    
    private func uploadFile(data: Data, bucket: String, fileName: String, contentType: String) async -> String? {
        print("🔄 SupabaseStorageService: Starting upload - Bucket: \(bucket), File: \(fileName), Size: \(data.count) bytes")
        print("🔄 SupabaseStorageService: Content Type: \(contentType)")

        // Log development mode status for debugging
        if UserDefaults.standard.bool(forKey: "isDevelopmentMode") {
            print("🚀 SupabaseStorageService: Development mode detected, but proceeding with upload")
        }

        // Check authentication status
        do {
            let session = try await supabase.auth.session
            print("🔐 SupabaseStorageService: User authenticated - ID: \(session.user.id)")
        } catch {
            print("❌ SupabaseStorageService: User not authenticated: \(error)")
            await MainActor.run {
                self.error = "Authentication required for file upload. Please sign in."
            }
            return nil
        }

        do {
            await MainActor.run {
                isUploading = true
                uploadProgress = 0.0
                error = nil
            }

            print("📤 SupabaseStorageService: Uploading to Supabase Storage...")
            print("📤 SupabaseStorageService: Full path will be: \(bucket)/\(fileName)")

            // Upload file to Supabase Storage
            try await supabase.storage
                .from(bucket)
                .upload(fileName, data: data, options: FileOptions(contentType: contentType, upsert: true))

            print("📤 SupabaseStorageService: Upload successful, getting public URL...")

            // Get public URL with CDN
            let publicURL = try supabase.storage
                .from(bucket)
                .getPublicURL(path: fileName)

            await MainActor.run {
                isUploading = false
                uploadProgress = 1.0
            }

            print("✅ File uploaded successfully: \(publicURL)")
            print("✅ Full URL: \(publicURL.absoluteString)")
            return publicURL.absoluteString

        } catch {
            await MainActor.run {
                isUploading = false
                uploadProgress = 0.0
                self.error = "Upload failed: \(error.localizedDescription)"
            }
            print("❌ SupabaseStorageService: Error uploading file to \(bucket)/\(fileName): \(error)")
            print("❌ SupabaseStorageService: Error details: \(error.localizedDescription)")
            print("❌ SupabaseStorageService: Error type: \(type(of: error))")
            if let localizedError = error as? LocalizedError {
                print("❌ SupabaseStorageService: Failure reason: \(localizedError.failureReason ?? "Unknown")")
                print("❌ SupabaseStorageService: Recovery suggestion: \(localizedError.recoverySuggestion ?? "None")")
            }
            return nil
        }
    }
    
    // MARK: - File Deletion
    
    func deleteMemoryImage(memoryId: UUID, userId: UUID) async -> Bool {
        await deleteFile(
            bucket: memoryImagesBucket,
            fileName: "\(userId.uuidString.lowercased())/\(memoryId.uuidString.lowercased()).jpg"
        )
    }

    func deleteMemoryVideo(memoryId: UUID, userId: UUID) async -> Bool {
        await deleteFile(
            bucket: memoryVideosBucket,
            fileName: "\(userId.uuidString.lowercased())/\(memoryId.uuidString.lowercased()).mp4"
        )
    }

    func deleteMemoryThumbnail(memoryId: UUID, userId: UUID) async -> Bool {
        await deleteFile(
            bucket: memoryThumbnailsBucket,
            fileName: "\(userId.uuidString.lowercased())/\(memoryId.uuidString.lowercased())_thumb.jpg"
        )
    }

    func deletePetProfile(petId: UUID, userId: UUID) async -> Bool {
        await deleteFile(
            bucket: petProfilesBucket,
            fileName: "\(userId.uuidString.lowercased())/\(petId.uuidString.lowercased()).jpg"
        )
    }
    
    private func deleteFile(bucket: String, fileName: String) async -> Bool {
        do {
            try await supabase.storage
                .from(bucket)
                .remove(paths: [fileName])
            
            print("✅ File deleted successfully: \(fileName)")
            return true
            
        } catch {
            print("❌ Error deleting file: \(error)")
            return false
        }
    }
    
    // MARK: - URL Helpers
    
    func getOptimizedImageURL(_ originalURL: String, width: Int = 400, height: Int = 400, quality: Int = 80) -> String {
        // Supabase automatically provides image optimization via CDN
        // Add transformation parameters for optimized loading
        if originalURL.contains("supabase") {
            return "\(originalURL)?width=\(width)&height=\(height)&quality=\(quality)&format=webp"
        }
        return originalURL
    }
    
    func getThumbnailURL(_ originalURL: String) -> String {
        getOptimizedImageURL(originalURL, width: 200, height: 200, quality: 70)
    }
    
    // MARK: - Storage Management
    
    func getStorageUsage(userId: UUID) async -> StorageUsage? {
        do {
            // Get file list for user across all buckets
            let buckets = [memoryImagesBucket, memoryVideosBucket, memoryThumbnailsBucket, petProfilesBucket]
            let totalSize: Int64 = 0 // TODO: Calculate actual size when Supabase API supports it
            var fileCount = 0

            for bucket in buckets {
                let files = try await supabase.storage
                    .from(bucket)
                    .list(path: userId.uuidString.lowercased())

                for _ in files {
                    // Note: Supabase storage file metadata structure may vary
                    // For now, we'll count files but size calculation needs proper API
                    fileCount += 1
                }
            }
            
            return StorageUsage(
                totalSizeBytes: totalSize,
                fileCount: fileCount,
                formattedSize: ByteCountFormatter.string(fromByteCount: totalSize, countStyle: .file)
            )
            
        } catch {
            print("❌ Error getting storage usage: \(error)")
            return nil
        }
    }
    
    func cleanupOrphanedFiles(userId: UUID, validMemoryIds: [UUID], validPetIds: [UUID]) async {
        // TODO: Implement cleanup of files that no longer have corresponding database records
        print("🧹 Cleaning up orphaned files for user: \(userId)")
    }
}

// MARK: - Storage Usage Model
struct StorageUsage {
    let totalSizeBytes: Int64
    let fileCount: Int
    let formattedSize: String
    
    var isNearLimit: Bool {
        // Assuming 1GB limit for free tier
        return totalSizeBytes > 800_000_000 // 800MB
    }
    
    var percentageUsed: Double {
        let limit: Int64 = 1_000_000_000 // 1GB
        return Double(totalSizeBytes) / Double(limit) * 100
    }
}

// MARK: - File Upload Progress
struct FileUploadProgress {
    let fileName: String
    let progress: Double
    let isComplete: Bool
    let error: String?
}
