//
//  PetTipsService.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 12/24/24.
//

import Foundation
import TipKit
import SwiftUI

// MARK: - Pet Care Tips

@available(iOS 17.0, *)
struct AddFirstPetTip: Tip {
    var title: Text {
        Text("Add Your First Pet")
    }
    
    var message: Text? {
        Text("Tap the + button to add your first pet and start tracking their health and memories.")
    }
    
    var image: Image? {
        Image(systemName: "pawprint.fill")
    }
    
    var rules: [Rule] {
        [
            #Rule(Self.$hasAddedPet) { $0 == false }
        ]
    }
    
    @Parameter
    static var hasAddedPet: Bool = false
}

@available(iOS 17.0, *)
struct AIAgentTip: Tip {
    var title: Text {
        Text("Meet Your AI Pet Experts")
    }
    
    var message: Text? {
        Text("Ask Dr. Nutrition, Health Expert, or Trainer Pro for personalized advice about your pet's care.")
    }
    
    var image: Image? {
        Image(systemName: "brain.head.profile")
    }
    
    var rules: [Rule] {
        [
            #Rule(Self.$hasUsedAIAgent) { $0 == false },
            #Rule(AddFirstPetTip.$hasAddedPet) { $0 == true }
        ]
    }
    
    @Parameter
    static var hasUsedAIAgent: Bool = false
}

@available(iOS 17.0, *)
struct MemoryCaptureTip: Tip {
    var title: Text {
        Text("Capture Precious Moments")
    }
    
    var message: Text? {
        Text("Use the camera button to capture and organize your pet's special memories with AI-powered insights.")
    }
    
    var image: Image? {
        Image(systemName: "camera.fill")
    }
    
    var rules: [Rule] {
        [
            #Rule(Self.$hasCreatedMemory) { $0 == false },
            #Rule(AddFirstPetTip.$hasAddedPet) { $0 == true }
        ]
    }
    
    @Parameter
    static var hasCreatedMemory: Bool = false
}

@available(iOS 17.0, *)
struct VoiceCommandTip: Tip {
    var title: Text {
        Text("Try Voice Commands")
    }
    
    var message: Text? {
        Text("Say 'Hey Siri, ask PetCapsule about my pet's health' for hands-free assistance.")
    }
    
    var image: Image? {
        Image(systemName: "mic.fill")
    }
    
    var rules: [Rule] {
        [
            #Rule(Self.$hasUsedVoiceCommand) { $0 == false },
            #Rule(AIAgentTip.$hasUsedAIAgent) { $0 == true }
        ]
    }
    
    @Parameter
    static var hasUsedVoiceCommand: Bool = false
}

@available(iOS 17.0, *)
struct HealthTrackingTip: Tip {
    var title: Text {
        Text("Track Health Trends")
    }
    
    var message: Text? {
        Text("View detailed health analytics and trends in the Health section to monitor your pet's wellbeing.")
    }
    
    var image: Image? {
        Image(systemName: "chart.line.uptrend.xyaxis")
    }
    
    var rules: [Rule] {
        [
            #Rule(Self.$hasViewedHealthCharts) { $0 == false },
            #Rule(AddFirstPetTip.$hasAddedPet) { $0 == true }
        ]
    }
    
    @Parameter
    static var hasViewedHealthCharts: Bool = false
}

@available(iOS 17.0, *)
struct EmergencyContactTip: Tip {
    var title: Text {
        Text("Set Up Emergency Contacts")
    }
    
    var message: Text? {
        Text("Add your veterinarian's contact information for quick access during emergencies.")
    }
    
    var image: Image? {
        Image(systemName: "phone.badge.plus")
    }
    
    var rules: [Rule] {
        [
            #Rule(Self.$hasSetupEmergencyContact) { $0 == false },
            #Rule(AddFirstPetTip.$hasAddedPet) { $0 == true }
        ]
    }
    
    @Parameter
    static var hasSetupEmergencyContact: Bool = false
}

@available(iOS 17.0, *)
struct VaccinationReminderTip: Tip {
    var title: Text {
        Text("Never Miss Vaccinations")
    }
    
    var message: Text? {
        Text("Set up vaccination reminders to keep your pet's immunizations up to date.")
    }
    
    var image: Image? {
        Image(systemName: "calendar.badge.plus")
    }
    
    var rules: [Rule] {
        [
            #Rule(Self.$hasSetupVaccination) { $0 == false },
            #Rule(AddFirstPetTip.$hasAddedPet) { $0 == true }
        ]
    }
    
    @Parameter
    static var hasSetupVaccination: Bool = false
}

@available(iOS 17.0, *)
struct WidgetTip: Tip {
    var title: Text {
        Text("Add Home Screen Widget")
    }
    
    var message: Text? {
        Text("Add the PetCapsule widget to your home screen for quick access to pet information.")
    }
    
    var image: Image? {
        Image(systemName: "rectangle.3.group")
    }
    
    var rules: [Rule] {
        [
            #Rule(Self.$hasAddedWidget) { $0 == false },
            #Rule(AddFirstPetTip.$hasAddedPet) { $0 == true }
        ]
    }
    
    @Parameter
    static var hasAddedWidget: Bool = false
}

// MARK: - Tips Service

@available(iOS 17.0, *)
@MainActor
class PetTipsService: ObservableObject {
    static let shared = PetTipsService()
    
    @Published var currentTip: (any Tip)?
    @Published var availableTips: [any Tip] = []
    
    private init() {
        setupTips()
    }
    
    func setupTips() {
        // Configure TipKit
        try? Tips.configure([
            .displayFrequency(.immediate),
            .datastoreLocation(.applicationDefault)
        ])
        
        // Initialize available tips
        availableTips = [
            AddFirstPetTip(),
            AIAgentTip(),
            MemoryCaptureTip(),
            VoiceCommandTip(),
            HealthTrackingTip(),
            EmergencyContactTip(),
            VaccinationReminderTip(),
            WidgetTip()
        ]
    }
    
    // MARK: - Tip State Management
    
    func markPetAdded() {
        AddFirstPetTip.hasAddedPet = true
    }
    
    func markAIAgentUsed() {
        AIAgentTip.hasUsedAIAgent = true
    }
    
    func markMemoryCreated() {
        MemoryCaptureTip.hasCreatedMemory = true
    }
    
    func markVoiceCommandUsed() {
        VoiceCommandTip.hasUsedVoiceCommand = true
    }
    
    func markHealthChartsViewed() {
        HealthTrackingTip.hasViewedHealthCharts = true
    }
    
    func markEmergencyContactSetup() {
        EmergencyContactTip.hasSetupEmergencyContact = true
    }
    
    func markVaccinationSetup() {
        VaccinationReminderTip.hasSetupVaccination = true
    }
    
    func markWidgetAdded() {
        WidgetTip.hasAddedWidget = true
    }
    
    // MARK: - Tip Display Helpers
    
    func shouldShowTip<T: Tip>(_ tip: T) -> Bool {
        return tip.shouldDisplay
    }
    
    func invalidateAllTips() {
        try? Tips.resetDatastore()
    }
    
    func getTipForContext(_ context: TipContext) -> (any Tip)? {
        switch context {
        case .onboarding:
            return AddFirstPetTip()
        case .aiAgents:
            return AIAgentTip()
        case .memoryCapture:
            return MemoryCaptureTip()
        case .voiceCommands:
            return VoiceCommandTip()
        case .healthTracking:
            return HealthTrackingTip()
        case .emergencySetup:
            return EmergencyContactTip()
        case .vaccinations:
            return VaccinationReminderTip()
        case .widgets:
            return WidgetTip()
        }
    }
}

// MARK: - Tip Context

enum TipContext {
    case onboarding
    case aiAgents
    case memoryCapture
    case voiceCommands
    case healthTracking
    case emergencySetup
    case vaccinations
    case widgets
}

// MARK: - Tip View Modifiers

@available(iOS 17.0, *)
struct TipViewModifier: ViewModifier {
    let tip: any Tip
    let arrowEdge: Edge
    
    func body(content: Content) -> some View {
        if #available(iOS 18.4, *) {
            content
                .popoverTip(tip, arrowEdge: arrowEdge)
        } else {
            content
        }
    }
}

@available(iOS 17.0, *)
extension View {
    func showTip<T: Tip>(_ tip: T, arrowEdge: Edge = .top) -> some View {
        self.modifier(TipViewModifier(tip: tip, arrowEdge: arrowEdge))
    }
    
    func contextualTip(_ context: TipContext, arrowEdge: Edge = .top) -> some View {
        if #available(iOS 17.0, *) {
            if let tip = PetTipsService.shared.getTipForContext(context) {
                return AnyView(self.showTip(tip, arrowEdge: arrowEdge))
            } else {
                return AnyView(self)
            }
        } else {
            return AnyView(self)
        }
    }
}

// MARK: - Tip Integration Views

@available(iOS 17.0, *)
struct TipAwareButton: View {
    let title: String
    let systemImage: String
    let tip: any Tip
    let action: () -> Void
    
    var body: some View {
        let button = Button(action: action) {
            Label(title, systemImage: systemImage)
        }

        if #available(iOS 18.4, *) {
            button.popoverTip(tip)
        } else {
            button
        }
    }
}

@available(iOS 17.0, *)
struct TipAwareNavigationLink<Destination: View>: View {
    let title: String
    let systemImage: String
    let tip: any Tip
    let destination: Destination
    
    var body: some View {
        let link = NavigationLink(destination: destination) {
            Label(title, systemImage: systemImage)
        }

        if #available(iOS 18.4, *) {
            link.popoverTip(tip)
        } else {
            link
        }
    }
}
