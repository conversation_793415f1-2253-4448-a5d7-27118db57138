//
//  PetPlannerService.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 6/6/25.
//

import Foundation
import CoreLocation
import Swift<PERSON>

@MainActor
class PetPlannerService: ObservableObject {
    static let shared = PetPlannerService()
    
    // Published properties for real-time updates
    @Published var currentWeather: WeatherData = WeatherData.sample
    @Published var airQuality: AirQualityData = AirQualityData.sample
    @Published var walkRecommendation: WalkRecommendation = WalkRecommendation.sample
    @Published var hourlyForecast: [HourlyForecast] = HourlyForecast.sampleData
    @Published var weeklyForecast: [WeeklyForecast] = WeeklyForecast.sampleData
    @Published var nearbyLocations: [PetFriendlyLocation] = []
    @Published var environmentalAlerts: [EnvironmentalAlert] = []
    @Published var walkMemories: [WalkMemory] = WalkMemory.sampleData
    @Published var communityEvents: [CommunityEvent] = CommunityEvent.sampleData
    
    // Loading states
    @Published var isLoadingWeather = false
    @Published var isLoadingLocations = false
    @Published var isLoadingAlerts = false
    
    // Services
    private let googleAPI = GoogleAPIService.shared
    private let weatherAPI = WeatherAPIService.shared
    private let locationManager = CLLocationManager()
    private var locationDelegate: LocationDelegate?
    
    // Current location
    @Published var currentLocation: CLLocationCoordinate2D?
    
    private init() {
        setupLocationManager()
        loadInitialData()
    }
    
    // MARK: - Location Management
    
    private func setupLocationManager() {
        locationDelegate = LocationDelegate(service: self)
        locationManager.delegate = locationDelegate
        locationManager.desiredAccuracy = kCLLocationAccuracyBest
        requestLocationPermission()
    }
    
    func requestLocationPermission() {
        switch locationManager.authorizationStatus {
        case .notDetermined:
            locationManager.requestWhenInUseAuthorization()
        case .denied, .restricted:
            // Use default location (San Francisco)
            currentLocation = CLLocationCoordinate2D(latitude: 37.7749, longitude: -122.4194)
            loadAllData()
        case .authorizedWhenInUse, .authorizedAlways:
            locationManager.requestLocation()
        @unknown default:
            break
        }
    }
    
    func updateLocation(_ location: CLLocationCoordinate2D) {
        currentLocation = location
        loadAllData()
    }
    
    // MARK: - Data Loading
    
    private func loadInitialData() {
        // Load sample data initially
        nearbyLocations = PetFriendlyLocation.sampleData
        environmentalAlerts = EnvironmentalAlert.sampleData
        
        // If we have location, load real data
        if currentLocation != nil {
            loadAllData()
        }
    }
    
    func loadAllData() {
        guard let location = currentLocation else { return }
        
        Task {
            await loadWeatherData(for: location)
            await loadNearbyLocations(for: location)
            await loadEnvironmentalAlerts(for: location)
        }
    }
    
    func refreshAllData() {
        guard currentLocation != nil else { return }
        loadAllData()
    }
    
    // MARK: - Weather Data
    
    private func loadWeatherData(for location: CLLocationCoordinate2D) async {
        isLoadingWeather = true
        
        do {
            // Load current weather
            let weather = try await weatherAPI.getCurrentWeather(for: location)
            let airQualityData = try await weatherAPI.getAirQuality(for: location)
            let hourlyData = try await weatherAPI.getHourlyForecast(for: location)
            
            // Generate walk recommendation
            let recommendation = weatherAPI.generateWalkRecommendation(
                weather: weather,
                airQuality: airQualityData
            )
            
            // Update UI on main thread
            await MainActor.run {
                self.currentWeather = weather
                self.airQuality = airQualityData
                self.walkRecommendation = recommendation
                self.hourlyForecast = hourlyData
                self.isLoadingWeather = false
            }
            
            print("✅ Weather data loaded successfully")
            
        } catch {
            print("❌ Failed to load weather data: \(error)")
            await MainActor.run {
                self.isLoadingWeather = false
            }
        }
    }
    
    // MARK: - Location Data
    
    private func loadNearbyLocations(for location: CLLocationCoordinate2D) async {
        isLoadingLocations = true
        
        do {
            let locations = try await googleAPI.searchPetFriendlyPlaces(near: location)
            
            await MainActor.run {
                self.nearbyLocations = locations.isEmpty ? PetFriendlyLocation.sampleData : locations
                self.isLoadingLocations = false
            }
            
            print("✅ Loaded \(locations.count) nearby locations")
            
        } catch {
            print("❌ Failed to load nearby locations: \(error)")
            await MainActor.run {
                self.nearbyLocations = PetFriendlyLocation.sampleData
                self.isLoadingLocations = false
            }
        }
    }
    
    // MARK: - Environmental Alerts
    
    private func loadEnvironmentalAlerts(for location: CLLocationCoordinate2D) async {
        isLoadingAlerts = true
        
        // Generate alerts based on current conditions
        var alerts: [EnvironmentalAlert] = []
        
        // Check air quality
        if airQuality.index > 100 {
            alerts.append(EnvironmentalAlert(
                title: "Poor Air Quality",
                message: "Air quality is unhealthy. Consider limiting outdoor activities for sensitive pets.",
                severity: .severe,
                icon: "wind.circle.fill",
                timeAgo: "Now"
            ))
        } else if airQuality.index > 50 {
            alerts.append(EnvironmentalAlert(
                title: "Moderate Air Quality",
                message: "Air quality is moderate. Sensitive pets should limit prolonged outdoor activities.",
                severity: .moderate,
                icon: "wind.circle.fill",
                timeAgo: "Now"
            ))
        }
        
        // Check temperature
        if currentWeather.temperature > 85 {
            alerts.append(EnvironmentalAlert(
                title: "High Temperature Warning",
                message: "Temperature is high. Ensure your pet has access to water and shade.",
                severity: .warning,
                icon: "thermometer.sun.fill",
                timeAgo: "Now"
            ))
        } else if currentWeather.temperature < 32 {
            alerts.append(EnvironmentalAlert(
                title: "Cold Temperature Alert",
                message: "Temperature is very cold. Consider shorter walks and protective gear.",
                severity: .warning,
                icon: "thermometer.snowflake",
                timeAgo: "Now"
            ))
        }
        
        // Check humidity
        if currentWeather.humidity > 80 {
            alerts.append(EnvironmentalAlert(
                title: "High Humidity",
                message: "Humidity levels are high. Watch for signs of overheating in your pet.",
                severity: .moderate,
                icon: "humidity.fill",
                timeAgo: "Now"
            ))
        }
        
        await MainActor.run {
            self.environmentalAlerts = alerts.isEmpty ? EnvironmentalAlert.sampleData : alerts
            self.isLoadingAlerts = false
        }
        
        print("✅ Generated \(alerts.count) environmental alerts")
    }
    
    // MARK: - Walk Memory Management
    
    func addWalkMemory(_ memory: WalkMemory) {
        walkMemories.insert(memory, at: 0)
        saveWalkMemories()
    }
    
    func toggleFavorite(for memoryId: UUID) {
        if let index = walkMemories.firstIndex(where: { $0.id == memoryId }) {
            walkMemories[index] = WalkMemory(
                location: walkMemories[index].location,
                date: walkMemories[index].date,
                duration: walkMemories[index].duration,
                distance: walkMemories[index].distance,
                imageURL: walkMemories[index].imageURL,
                isFavorite: !walkMemories[index].isFavorite,
                notes: walkMemories[index].notes
            )
            saveWalkMemories()
        }
    }
    
    private func saveWalkMemories() {
        // Save to UserDefaults or Core Data
        // For now, just keep in memory
    }
    
    // MARK: - Community Events Management
    
    func joinEvent(_ eventId: UUID) {
        if let index = communityEvents.firstIndex(where: { $0.id == eventId }) {
            let event = communityEvents[index]
            communityEvents[index] = CommunityEvent(
                title: event.title,
                description: event.description,
                organizer: event.organizer,
                date: event.date,
                location: event.location,
                category: event.category,
                attendees: event.attendees + 1,
                maxAttendees: event.maxAttendees,
                isJoined: true,
                imageURL: event.imageURL
            )
        }
    }
    
    func leaveEvent(_ eventId: UUID) {
        if let index = communityEvents.firstIndex(where: { $0.id == eventId }) {
            let event = communityEvents[index]
            communityEvents[index] = CommunityEvent(
                title: event.title,
                description: event.description,
                organizer: event.organizer,
                date: event.date,
                location: event.location,
                category: event.category,
                attendees: max(0, event.attendees - 1),
                maxAttendees: event.maxAttendees,
                isJoined: false,
                imageURL: event.imageURL
            )
        }
    }
}

// MARK: - Location Delegate

class LocationDelegate: NSObject, CLLocationManagerDelegate {
    weak var service: PetPlannerService?
    
    init(service: PetPlannerService) {
        self.service = service
    }
    
    func locationManager(_ manager: CLLocationManager, didUpdateLocations locations: [CLLocation]) {
        guard let location = locations.last else { return }
        
        Task { @MainActor in
            service?.updateLocation(location.coordinate)
        }
    }
    
    func locationManager(_ manager: CLLocationManager, didFailWithError error: Error) {
        print("❌ Location error: \(error)")
        
        Task { @MainActor in
            // Use default location (San Francisco)
            service?.updateLocation(CLLocationCoordinate2D(latitude: 37.7749, longitude: -122.4194))
        }
    }
    
    func locationManagerDidChangeAuthorization(_ manager: CLLocationManager) {
        Task { @MainActor in
            service?.requestLocationPermission()
        }
    }
}
