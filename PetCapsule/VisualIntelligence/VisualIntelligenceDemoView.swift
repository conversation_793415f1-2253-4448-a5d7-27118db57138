//
//  VisualIntelligenceDemoView.swift
//  PetCapsule
//
//  Demo view showcasing Visual Intelligence capabilities
//  Demonstrates pet identification, health analysis, and product search
//

import SwiftUI
import PhotosUI

@available(iOS 18.0, *)
struct VisualIntelligenceDemoView: View {
    @StateObject private var visualIntelligenceService = VisualIntelligenceService.shared
    @StateObject private var contentProvider = PetVisualIntelligenceContentProvider.shared
    
    @State private var selectedImage: UIImage?
    @State private var selectedPhotoItem: PhotosPickerItem?
    @State private var analysisResults: VisualIntelligenceSearchResults?
    @State private var isAnalyzing = false
    @State private var selectedAnalysisType: AnalysisType = .comprehensive
    @State private var showingImagePicker = false
    @State private var showingCamera = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    headerSection
                    
                    imageSelectionSection
                    
                    if let image = selectedImage {
                        selectedImageSection(image: image)
                    }
                    
                    analysisTypeSection
                    
                    if isAnalyzing {
                        analysisProgressSection
                    }
                    
                    if let results = analysisResults {
                        resultsSection(results: results)
                    }
                    
                    featuresOverviewSection
                }
                .padding()
            }
            .navigationTitle("Visual Intelligence")
            .navigationBarTitleDisplayMode(.large)
            .sheet(isPresented: $showingImagePicker) {
                PhotosPicker(
                    selection: $selectedPhotoItem,
                    matching: .images,
                    photoLibrary: .shared()
                ) {
                    Text("Select Photo")
                }
                .photosPickerStyle(.presentation)
            }
            .fullScreenCover(isPresented: $showingCamera) {
                CameraView { image in
                    selectedImage = image
                    showingCamera = false
                }
            }
            .onChange(of: selectedPhotoItem) { _, newItem in
                Task {
                    if let data = try? await newItem?.loadTransferable(type: Data.self),
                       let image = UIImage(data: data) {
                        selectedImage = image
                    }
                }
            }
        }
    }
    
    // MARK: - View Sections
    
    private var headerSection: some View {
        VStack(spacing: 12) {
            Image(systemName: "eye.circle.fill")
                .font(.system(size: 60))
                .foregroundStyle(.blue.gradient)
            
            Text("Visual Intelligence")
                .font(.title)
                .fontWeight(.bold)
            
            Text("Point your camera at pets, products, or scenes to get instant insights and recommendations.")
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(16)
    }
    
    private var imageSelectionSection: some View {
        VStack(spacing: 16) {
            Text("Select an Image")
                .font(.headline)
            
            HStack(spacing: 20) {
                Button(action: { showingCamera = true }) {
                    VStack {
                        Image(systemName: "camera.fill")
                            .font(.title2)
                        Text("Camera")
                            .font(.caption)
                    }
                    .frame(width: 80, height: 80)
                    .background(Color.blue.opacity(0.1))
                    .foregroundColor(.blue)
                    .cornerRadius(12)
                }
                
                Button(action: { showingImagePicker = true }) {
                    VStack {
                        Image(systemName: "photo.fill")
                            .font(.title2)
                        Text("Photos")
                            .font(.caption)
                    }
                    .frame(width: 80, height: 80)
                    .background(Color.green.opacity(0.1))
                    .foregroundColor(.green)
                    .cornerRadius(12)
                }
            }
        }
    }
    
    private func selectedImageSection(image: UIImage) -> some View {
        VStack(spacing: 16) {
            Text("Selected Image")
                .font(.headline)
            
            Image(uiImage: image)
                .resizable()
                .aspectRatio(contentMode: .fit)
                .frame(maxHeight: 200)
                .cornerRadius(12)
                .shadow(radius: 4)
            
            Button(action: { analyzeImage() }) {
                HStack {
                    Image(systemName: "eye.fill")
                    Text("Analyze with Visual Intelligence")
                }
                .font(.headline)
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .padding()
                .background(Color.blue)
                .cornerRadius(12)
            }
            .disabled(isAnalyzing)
        }
    }
    
    private var analysisTypeSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Analysis Type")
                .font(.headline)
            
            Picker("Analysis Type", selection: $selectedAnalysisType) {
                ForEach(AnalysisType.allCases, id: \.self) { type in
                    Text(type.displayName).tag(type)
                }
            }
            .pickerStyle(.segmented)
        }
    }
    
    private var analysisProgressSection: some View {
        VStack(spacing: 16) {
            ProgressView()
                .scaleEffect(1.5)
            
            Text("Analyzing with Visual Intelligence...")
                .font(.headline)
                .foregroundColor(.secondary)
            
            Text("Identifying breeds, analyzing health indicators, and finding relevant content...")
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    
    private func resultsSection(results: VisualIntelligenceSearchResults) -> some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Analysis Results")
                .font(.title2)
                .fontWeight(.bold)
            
            Text("Found \(results.totalResults) matches in \(String(format: "%.1f", results.processingTime))s")
                .font(.caption)
                .foregroundColor(.secondary)
            
            // Quick Actions
            if !results.quickActions.isEmpty {
                quickActionsView(actions: results.quickActions)
            }
            
            // Grouped Results
            ForEach(Array(results.groupedMatches.keys), id: \.self) { matchType in
                if let matches = results.groupedMatches[matchType], !matches.isEmpty {
                    matchTypeSection(type: matchType, matches: matches)
                }
            }
            
            // Suggestions
            if !results.suggestions.isEmpty {
                suggestionsView(suggestions: results.suggestions)
            }
        }
    }
    
    private func quickActionsView(actions: [QuickAction]) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Quick Actions")
                .font(.headline)
            
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 12) {
                ForEach(actions, id: \.title) { action in
                    Button(action: {
                        // Handle quick action
                    }) {
                        VStack {
                            Image(systemName: action.icon)
                                .font(.title2)
                            Text(action.title)
                                .font(.caption)
                        }
                        .frame(height: 60)
                        .frame(maxWidth: .infinity)
                        .background(Color.blue.opacity(0.1))
                        .foregroundColor(.blue)
                        .cornerRadius(8)
                    }
                }
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    
    private func matchTypeSection(type: MatchType, matches: [VisualIntelligenceMatch]) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: type.icon)
                    .foregroundColor(.blue)
                Text(type.displayName)
                    .font(.headline)
                Spacer()
                Text("\(matches.count)")
                    .font(.caption)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color.blue.opacity(0.2))
                    .cornerRadius(8)
            }
            
            ForEach(matches, id: \.title) { match in
                matchRowView(match: match)
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    
    private func matchRowView(match: VisualIntelligenceMatch) -> some View {
        HStack {
            if let image = match.thumbnailImage {
                Image(uiImage: image)
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .frame(width: 50, height: 50)
                    .cornerRadius(8)
            } else {
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color.gray.opacity(0.3))
                    .frame(width: 50, height: 50)
                    .overlay(
                        Image(systemName: match.type.icon)
                            .foregroundColor(.gray)
                    )
            }
            
            VStack(alignment: .leading, spacing: 4) {
                Text(match.title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text(match.subtitle)
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Text(match.description)
                    .font(.caption2)
                    .foregroundColor(.secondary)
                    .lineLimit(2)
            }
            
            Spacer()
            
            VStack {
                Text("\(Int(match.relevanceScore * 100))%")
                    .font(.caption)
                    .fontWeight(.bold)
                    .foregroundColor(.blue)
                
                Text("match")
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
        }
        .padding(.vertical, 4)
    }
    
    private func suggestionsView(suggestions: [String]) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Suggestions")
                .font(.headline)
            
            ForEach(suggestions, id: \.self) { suggestion in
                HStack {
                    Image(systemName: "lightbulb.fill")
                        .foregroundColor(.yellow)
                    Text(suggestion)
                        .font(.body)
                    Spacer()
                }
                .padding(.vertical, 2)
            }
        }
        .padding()
        .background(Color.yellow.opacity(0.1))
        .cornerRadius(12)
    }
    
    private var featuresOverviewSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Visual Intelligence Features")
                .font(.title2)
                .fontWeight(.bold)
            
            FeatureCard(
                icon: "pawprint.circle.fill",
                title: "Pet Breed Identification",
                description: "Instantly identify pet breeds with detailed characteristics and care instructions.",
                color: .blue
            )
            
            FeatureCard(
                icon: "heart.circle.fill",
                title: "Health Analysis",
                description: "Analyze visual health indicators and get recommendations for your pet's wellbeing.",
                color: .red
            )
            
            FeatureCard(
                icon: "bag.circle.fill",
                title: "Product Recognition",
                description: "Identify pet products and get recommendations for alternatives and accessories.",
                color: .green
            )
            
            FeatureCard(
                icon: "photo.circle.fill",
                title: "Memory Enhancement",
                description: "Enhance pet memories with contextual information and automatic tagging.",
                color: .purple
            )
        }
    }
    
    // MARK: - Actions
    
    private func analyzeImage() {
        guard let image = selectedImage else { return }
        
        isAnalyzing = true
        
        Task {
            do {
                // Create a mock semantic content descriptor for demo purposes
                // In a real implementation, this would come from the Visual Intelligence framework
                let mockDescriptor = createMockSemanticContentDescriptor(for: image)
                
                let results = try await contentProvider.generateSearchResults(for: mockDescriptor)
                
                await MainActor.run {
                    self.analysisResults = results
                    self.isAnalyzing = false
                }
            } catch {
                await MainActor.run {
                    self.isAnalyzing = false
                    // Handle error
                }
            }
        }
    }
    
    private func createMockSemanticContentDescriptor(for image: UIImage) -> SemanticContentDescriptor {
        // This is a placeholder - in a real implementation, the SemanticContentDescriptor
        // would be provided by the Visual Intelligence framework
        return SemanticContentDescriptor()
    }
}

// MARK: - Supporting Types

@available(iOS 18.0, *)
enum AnalysisType: String, CaseIterable {
    case comprehensive = "comprehensive"
    case breedOnly = "breed"
    case healthOnly = "health"
    case productsOnly = "products"
    
    var displayName: String {
        switch self {
        case .comprehensive: return "All"
        case .breedOnly: return "Breed"
        case .healthOnly: return "Health"
        case .productsOnly: return "Products"
        }
    }
}

@available(iOS 18.0, *)
struct FeatureCard: View {
    let icon: String
    let title: String
    let description: String
    let color: Color
    
    var body: some View {
        HStack(spacing: 16) {
            Image(systemName: icon)
                .font(.title)
                .foregroundColor(color)
                .frame(width: 40)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.headline)
                    .fontWeight(.medium)
                
                Text(description)
                    .font(.body)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

@available(iOS 18.0, *)
struct CameraView: UIViewControllerRepresentable {
    let onImageCaptured: (UIImage) -> Void
    
    func makeUIViewController(context: Context) -> UIImagePickerController {
        let picker = UIImagePickerController()
        picker.sourceType = .camera
        picker.delegate = context.coordinator
        return picker
    }
    
    func updateUIViewController(_ uiViewController: UIImagePickerController, context: Context) {}
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    class Coordinator: NSObject, UIImagePickerControllerDelegate, UINavigationControllerDelegate {
        let parent: CameraView
        
        init(_ parent: CameraView) {
            self.parent = parent
        }
        
        func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
            if let image = info[.originalImage] as? UIImage {
                parent.onImageCaptured(image)
            }
        }
    }
}
