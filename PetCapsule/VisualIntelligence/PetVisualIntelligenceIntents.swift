//
//  PetVisualIntelligenceIntents.swift
//  PetCapsule
//
//  App Intents for Visual Intelligence integration
//  Enables pet identification, health analysis, and contextual search
//

import Foundation
import AppIntents
import SwiftUI

// MARK: - Pet Breed Identification Intent

@available(iOS 18.0, *)
struct IdentifyPetBreedIntent: AppIntent {
    static var title: LocalizedStringResource = "Identify Pet Breed"
    static var description = IntentDescription("Identify the breed of a pet from a photo using Visual Intelligence.")
    static var openAppWhenRun: Bool = false
    
    @Parameter(title: "Visual Content")
    var visualContent: SemanticContentDescriptor
    
    @Parameter(title: "Include Care Instructions", default: true)
    var includeCareInstructions: Bool
    
    static var parameterSummary: some ParameterSummary {
        Summary("Identify pet breed from \(\.$visualContent)")
    }
    
    func perform() async throws -> some IntentResult & ProvidesDialog & ShowsSnippetView {
        let visualIntelligenceService = VisualIntelligenceService.shared
        
        do {
            let result = try await visualIntelligenceService.identifyPetBreed(from: visualContent)
            
            let dialogText = "I identified this as a \(result.primaryBreed) with \(Int(result.confidence * 100))% confidence."
            
            return .result(
                dialog: IntentDialog(dialogText),
                view: PetBreedIdentificationView(result: result, includeCareInstructions: includeCareInstructions)
            )
        } catch {
            return .result(
                dialog: IntentDialog("I couldn't identify the pet breed. Please try with a clearer image."),
                view: ErrorView(message: "Breed identification failed")
            )
        }
    }
}

// MARK: - Pet Health Analysis Intent

@available(iOS 18.0, *)
struct AnalyzePetHealthIntent: AppIntent {
    static var title: LocalizedStringResource = "Analyze Pet Health"
    static var description = IntentDescription("Analyze visual health indicators of a pet from a photo.")
    static var openAppWhenRun: Bool = false
    
    @Parameter(title: "Visual Content")
    var visualContent: SemanticContentDescriptor
    
    @Parameter(title: "Include Recommendations", default: true)
    var includeRecommendations: Bool
    
    static var parameterSummary: some ParameterSummary {
        Summary("Analyze pet health from \(\.$visualContent)")
    }
    
    func perform() async throws -> some IntentResult & ProvidesDialog & ShowsSnippetView {
        let visualIntelligenceService = VisualIntelligenceService.shared
        
        do {
            let result = try await visualIntelligenceService.analyzeHealthIndicators(from: visualContent)
            
            let riskText = result.riskLevel == .high ? "some concerns" : "good health indicators"
            let dialogText = "Health analysis complete. I found \(riskText) with \(Int(result.confidence * 100))% confidence."
            
            return .result(
                dialog: IntentDialog(dialogText),
                view: PetHealthAnalysisView(result: result, includeRecommendations: includeRecommendations)
            )
        } catch {
            return .result(
                dialog: IntentDialog("I couldn't analyze the pet's health. Please try with a clearer image."),
                view: ErrorView(message: "Health analysis failed")
            )
        }
    }
}

// MARK: - Pet Product Search Intent

@available(iOS 18.0, *)
struct SearchPetProductsIntent: AppIntent {
    static var title: LocalizedStringResource = "Search Pet Products"
    static var description = IntentDescription("Identify pet products and find recommendations.")
    static var openAppWhenRun: Bool = true
    
    @Parameter(title: "Visual Content")
    var visualContent: SemanticContentDescriptor
    
    @Parameter(title: "Show Alternatives", default: true)
    var showAlternatives: Bool
    
    static var parameterSummary: some ParameterSummary {
        Summary("Search for pet products from \(\.$visualContent)")
    }
    
    func perform() async throws -> some IntentResult & ProvidesDialog & ShowsSnippetView {
        let visualIntelligenceService = VisualIntelligenceService.shared
        
        do {
            let result = try await visualIntelligenceService.identifyPetProducts(from: visualContent)
            
            let productCount = result.identifiedProducts.count
            let dialogText = productCount > 0 
                ? "I found \(productCount) pet product(s) and have recommendations for you!"
                : "I couldn't identify specific pet products, but I can suggest some general recommendations."
            
            return .result(
                dialog: IntentDialog(dialogText),
                view: PetProductSearchView(result: result, showAlternatives: showAlternatives)
            )
        } catch {
            return .result(
                dialog: IntentDialog("I couldn't identify pet products. Please try with a clearer image."),
                view: ErrorView(message: "Product search failed")
            )
        }
    }
}

// MARK: - Enhance Memory Intent

@available(iOS 18.0, *)
struct EnhanceMemoryWithVisualIntelligenceIntent: AppIntent {
    static var title: LocalizedStringResource = "Enhance Pet Memory"
    static var description = IntentDescription("Enhance a pet memory with contextual information from Visual Intelligence.")
    static var openAppWhenRun: Bool = true
    
    @Parameter(title: "Visual Content")
    var visualContent: SemanticContentDescriptor
    
    @Parameter(title: "Memory Title")
    var memoryTitle: String?
    
    @Parameter(title: "Existing Description")
    var existingDescription: String?
    
    static var parameterSummary: some ParameterSummary {
        Summary("Enhance pet memory from \(\.$visualContent)")
    }
    
    func perform() async throws -> some IntentResult & ProvidesDialog & ShowsSnippetView {
        let visualIntelligenceService = VisualIntelligenceService.shared
        
        // Create a temporary memory object if we have existing data
        let existingMemory: Memory? = {
            if let title = memoryTitle, let description = existingDescription {
                return Memory(
                    title: title,
                    content: description,
                    type: .photo,
                    mediaURL: nil,
                    thumbnailURL: nil,
                    duration: nil,
                    milestone: nil,
                    sentiment: "positive",
                    tags: [],
                    isPublic: false,
                    isFavorite: false
                )
            }
            return nil
        }()
        
        do {
            let result = try await visualIntelligenceService.enhanceMemoryWithContext(
                from: visualContent,
                existingMemory: existingMemory
            )
            
            let dialogText = "I've enhanced your pet memory with contextual information and suggested \(result.suggestedTags.count) relevant tags!"
            
            return .result(
                dialog: IntentDialog(dialogText),
                view: EnhancedMemoryView(result: result, originalMemory: existingMemory)
            )
        } catch {
            return .result(
                dialog: IntentDialog("I couldn't enhance the memory. Please try again."),
                view: ErrorView(message: "Memory enhancement failed")
            )
        }
    }
}

// MARK: - Find Nearby Veterinarians Intent

@available(iOS 18.0, *)
struct FindNearbyVeterinariansIntent: AppIntent {
    static var title: LocalizedStringResource = "Find Nearby Veterinarians"
    static var description = IntentDescription("Find veterinarians near you based on visual context.")
    static var openAppWhenRun: Bool = true
    
    @Parameter(title: "Visual Content")
    var visualContent: SemanticContentDescriptor
    
    @Parameter(title: "Emergency Only", default: false)
    var emergencyOnly: Bool
    
    static var parameterSummary: some ParameterSummary {
        Summary("Find veterinarians near \(\.$visualContent)")
    }
    
    func perform() async throws -> some IntentResult & ProvidesDialog {
        // This would integrate with the existing veterinarian search functionality
        // and use visual context to provide more relevant results
        
        let dialogText = emergencyOnly 
            ? "Finding emergency veterinarians near you..."
            : "Finding veterinarians in your area..."
        
        return .result(dialog: IntentDialog(dialogText))
    }
}

// MARK: - Visual Intelligence Search Intent

@available(iOS 18.0, *)
struct VisualIntelligenceSearchIntent: AppIntent {
    static var title: LocalizedStringResource = "Search with Visual Intelligence"
    static var description = IntentDescription("Comprehensive pet-related search using Visual Intelligence.")
    static var openAppWhenRun: Bool = true
    
    @Parameter(title: "Visual Content")
    var visualContent: SemanticContentDescriptor
    
    @Parameter(title: "Search Type")
    var searchType: VisualSearchType
    
    static var parameterSummary: some ParameterSummary {
        Summary("Search for \(\.$searchType) from \(\.$visualContent)")
    }
    
    func perform() async throws -> some IntentResult & ProvidesDialog & ShowsSnippetView {
        let visualIntelligenceService = VisualIntelligenceService.shared
        
        switch searchType {
        case .breedIdentification:
            let result = try await visualIntelligenceService.identifyPetBreed(from: visualContent)
            return .result(
                dialog: IntentDialog("Identified as \(result.primaryBreed)"),
                view: PetBreedIdentificationView(result: result, includeCareInstructions: true)
            )
            
        case .healthAnalysis:
            let result = try await visualIntelligenceService.analyzeHealthIndicators(from: visualContent)
            return .result(
                dialog: IntentDialog("Health analysis complete"),
                view: PetHealthAnalysisView(result: result, includeRecommendations: true)
            )
            
        case .productSearch:
            let result = try await visualIntelligenceService.identifyPetProducts(from: visualContent)
            return .result(
                dialog: IntentDialog("Found \(result.identifiedProducts.count) products"),
                view: PetProductSearchView(result: result, showAlternatives: true)
            )
            
        case .memoryEnhancement:
            let result = try await visualIntelligenceService.enhanceMemoryWithContext(from: visualContent, existingMemory: nil)
            return .result(
                dialog: IntentDialog("Memory enhanced with context"),
                view: EnhancedMemoryView(result: result, originalMemory: nil)
            )
        }
    }
}

// MARK: - Supporting Types

@available(iOS 18.0, *)
enum VisualSearchType: String, AppEnum {
    case breedIdentification = "breed"
    case healthAnalysis = "health"
    case productSearch = "products"
    case memoryEnhancement = "memory"
    
    static var typeDisplayRepresentation: TypeDisplayRepresentation {
        TypeDisplayRepresentation(name: "Visual Search Type")
    }
    
    static var caseDisplayRepresentations: [VisualSearchType: DisplayRepresentation] {
        [
            .breedIdentification: DisplayRepresentation(title: "Breed Identification", subtitle: "Identify pet breeds"),
            .healthAnalysis: DisplayRepresentation(title: "Health Analysis", subtitle: "Analyze health indicators"),
            .productSearch: DisplayRepresentation(title: "Product Search", subtitle: "Find pet products"),
            .memoryEnhancement: DisplayRepresentation(title: "Memory Enhancement", subtitle: "Enhance pet memories")
        ]
    }
}

// MARK: - Placeholder Views (These would be implemented as proper SwiftUI views)

@available(iOS 18.0, *)
struct PetBreedIdentificationView: View {
    let result: PetBreedIdentificationResult
    let includeCareInstructions: Bool
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text(result.primaryBreed)
                .font(.title2)
                .fontWeight(.bold)
            
            Text("Confidence: \(Int(result.confidence * 100))%")
                .font(.caption)
                .foregroundColor(.secondary)
            
            if includeCareInstructions && !result.careInstructions.isEmpty {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Care Instructions:")
                        .font(.headline)
                    
                    ForEach(result.careInstructions, id: \.self) { instruction in
                        Text("• \(instruction)")
                            .font(.body)
                    }
                }
            }
        }
        .padding()
    }
}

@available(iOS 18.0, *)
struct PetHealthAnalysisView: View {
    let result: VisualHealthAnalysisResult
    let includeRecommendations: Bool
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Health Analysis")
                .font(.title2)
                .fontWeight(.bold)
            
            Text("Risk Level: \(result.riskLevel.rawValue.capitalized)")
                .font(.body)
                .foregroundColor(result.riskLevel == .high ? .red : .green)
            
            if includeRecommendations && !result.recommendations.isEmpty {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Recommendations:")
                        .font(.headline)
                    
                    ForEach(result.recommendations, id: \.self) { recommendation in
                        Text("• \(recommendation)")
                            .font(.body)
                    }
                }
            }
        }
        .padding()
    }
}

@available(iOS 18.0, *)
struct PetProductSearchView: View {
    let result: PetProductIdentificationResult
    let showAlternatives: Bool
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Pet Products Found")
                .font(.title2)
                .fontWeight(.bold)
            
            ForEach(result.identifiedProducts, id: \.name) { product in
                VStack(alignment: .leading, spacing: 4) {
                    Text(product.name)
                        .font(.headline)
                    Text(product.category)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding()
    }
}

@available(iOS 18.0, *)
struct EnhancedMemoryView: View {
    let result: EnhancedMemoryResult
    let originalMemory: Memory?
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Enhanced Memory")
                .font(.title2)
                .fontWeight(.bold)
            
            Text(result.enhancedDescription)
                .font(.body)
            
            if !result.suggestedTags.isEmpty {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Suggested Tags:")
                        .font(.headline)
                    
                    LazyVGrid(columns: [GridItem(.adaptive(minimum: 80))], spacing: 8) {
                        ForEach(result.suggestedTags, id: \.self) { tag in
                            Text(tag)
                                .font(.caption)
                                .padding(.horizontal, 8)
                                .padding(.vertical, 4)
                                .background(Color.blue.opacity(0.2))
                                .cornerRadius(8)
                        }
                    }
                }
            }
        }
        .padding()
    }
}

@available(iOS 18.0, *)
struct ErrorView: View {
    let message: String
    
    var body: some View {
        VStack {
            Image(systemName: "exclamationmark.triangle")
                .font(.largeTitle)
                .foregroundColor(.orange)
            
            Text(message)
                .font(.body)
                .multilineTextAlignment(.center)
        }
        .padding()
    }
}
