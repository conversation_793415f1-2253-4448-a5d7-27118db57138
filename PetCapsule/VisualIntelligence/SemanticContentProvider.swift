//
//  SemanticContentProvider.swift
//  PetCapsule
//
//  Provides semantic content descriptors for Visual Intelligence
//  Handles content matching and search result generation
//

import Foundation
import AppIntents
import SwiftUI

// Note: SemanticContentDescriptor is defined in VisualIntelligenceService.swift

// MARK: - Semantic Content Descriptor Implementation

@available(iOS 18.0, *)
extension SemanticContentDescriptor {
    
    // MARK: - Pet-Related Content Matching
    
    /// Determines if the visual content contains pet-related information
    var containsPetContent: Bool {
        // This would analyze the semantic content to determine if it contains pets
        // For now, we'll simulate this functionality
        return true
    }
    
    /// Extracts pet-specific metadata from the visual content
    var petMetadata: PetContentMetadata? {
        guard containsPetContent else { return nil }
        
        // In a real implementation, this would extract actual metadata
        // from the semantic content descriptor
        return PetContentMetadata(
            hasAnimal: true,
            animalType: "dog", // This would be detected from the content
            confidence: 0.85,
            boundingBoxes: [CGRect(x: 0.2, y: 0.3, width: 0.6, height: 0.5)],
            detectedObjects: ["dog", "collar", "toy"],
            scene: "indoor",
            lighting: "natural",
            imageQuality: .high
        )
    }
    
    /// Generates search keywords based on the visual content
    var searchKeywords: [String] {
        guard let metadata = petMetadata else { return [] }
        
        var keywords = [metadata.animalType]
        keywords.append(contentsOf: metadata.detectedObjects)
        keywords.append(metadata.scene)
        
        return keywords.filter { !$0.isEmpty }
    }
}

// MARK: - Pet Content Metadata

@available(iOS 18.0, *)
struct PetContentMetadata {
    let hasAnimal: Bool
    let animalType: String
    let confidence: Float
    let boundingBoxes: [CGRect]
    let detectedObjects: [String]
    let scene: String
    let lighting: String
    let imageQuality: ImageQuality
}

@available(iOS 18.0, *)
enum ImageQuality: String, CaseIterable {
    case low = "low"
    case medium = "medium"
    case high = "high"
    
    var description: String {
        switch self {
        case .low: return "Low quality - may affect analysis accuracy"
        case .medium: return "Medium quality - good for basic analysis"
        case .high: return "High quality - optimal for detailed analysis"
        }
    }
}

// MARK: - Visual Intelligence Content Provider

@available(iOS 18.0, *)
@MainActor
class PetVisualIntelligenceContentProvider: ObservableObject {
    static let shared = PetVisualIntelligenceContentProvider()

    private let visualIntelligenceService = VisualIntelligenceService.shared
    private nonisolated let memoryService = ProductionMemoryService.shared

    private init() {}
    
    // MARK: - Content Matching
    
    /// Finds matching content in the app based on visual intelligence input
    func findMatchingContent(for descriptor: SemanticContentDescriptor) async throws -> [VisualIntelligenceMatch] {
        var matches: [VisualIntelligenceMatch] = []
        
        // Check for pet breed matches
        if let breedMatches = try await findBreedMatches(for: descriptor) {
            matches.append(contentsOf: breedMatches)
        }
        
        // Check for health-related matches
        if let healthMatches = try await findHealthMatches(for: descriptor) {
            matches.append(contentsOf: healthMatches)
        }
        
        // Check for product matches
        if let productMatches = try await findProductMatches(for: descriptor) {
            matches.append(contentsOf: productMatches)
        }
        
        // Check for memory matches
        if let memoryMatches = try await findMemoryMatches(for: descriptor) {
            matches.append(contentsOf: memoryMatches)
        }
        
        return matches.sorted { $0.relevanceScore > $1.relevanceScore }
    }
    
    // MARK: - Specific Content Matching
    
    private func findBreedMatches(for descriptor: SemanticContentDescriptor) async throws -> [VisualIntelligenceMatch]? {
        guard descriptor.containsPetContent else { return nil }
        
        let breedResult = try await visualIntelligenceService.identifyPetBreed(from: descriptor)
        
        return [
            VisualIntelligenceMatch(
                type: .breedInformation,
                title: breedResult.primaryBreed,
                subtitle: "Breed Information",
                description: "Learn about \(breedResult.primaryBreed) characteristics and care",
                relevanceScore: breedResult.confidence,
                actionURL: URL(string: "petcapsule://breed/\(breedResult.primaryBreed.lowercased())")!,
                thumbnailImage: breedResult.image,
                metadata: [
                    "breed": breedResult.primaryBreed,
                    "confidence": String(breedResult.confidence),
                    "temperament": breedResult.temperament
                ]
            )
        ]
    }
    
    private func findHealthMatches(for descriptor: SemanticContentDescriptor) async throws -> [VisualIntelligenceMatch]? {
        guard descriptor.containsPetContent else { return nil }
        
        let healthResult = try await visualIntelligenceService.analyzeHealthIndicators(from: descriptor)
        
        guard !healthResult.visualIndicators.isEmpty else { return nil }
        
        return [
            VisualIntelligenceMatch(
                type: .healthAnalysis,
                title: "Pet Health Analysis",
                subtitle: "Visual Health Assessment",
                description: "Health indicators detected with recommendations",
                relevanceScore: healthResult.confidence,
                actionURL: URL(string: "petcapsule://health/analysis")!,
                thumbnailImage: healthResult.image,
                metadata: [
                    "riskLevel": healthResult.riskLevel.rawValue,
                    "shouldConsultVet": String(healthResult.shouldConsultVet),
                    "indicatorCount": String(healthResult.visualIndicators.count)
                ]
            )
        ]
    }
    
    private func findProductMatches(for descriptor: SemanticContentDescriptor) async throws -> [VisualIntelligenceMatch]? {
        let productResult = try await visualIntelligenceService.identifyPetProducts(from: descriptor)
        
        guard !productResult.identifiedProducts.isEmpty else { return nil }
        
        return productResult.identifiedProducts.map { product in
            VisualIntelligenceMatch(
                type: .productRecommendation,
                title: product.name,
                subtitle: product.category,
                description: "Find similar products and recommendations",
                relevanceScore: product.confidence,
                actionURL: URL(string: "petcapsule://products/search?category=\(product.category.lowercased())")!,
                thumbnailImage: productResult.image,
                metadata: [
                    "category": product.category,
                    "confidence": String(product.confidence)
                ]
            )
        }
    }
    
    private func findMemoryMatches(for descriptor: SemanticContentDescriptor) async throws -> [VisualIntelligenceMatch]? {
        // Search for similar memories in the user's collection
        let memories = try await memoryService.getAllMemories()
        
        // This would use image similarity matching in a real implementation
        let similarMemories = memories.prefix(3) // Simulate finding similar memories
        
        return similarMemories.map { memory in
            VisualIntelligenceMatch(
                type: .memoryMatch,
                title: memory.title,
                subtitle: "Similar Memory",
                description: memory.content,
                relevanceScore: 0.7, // This would be calculated based on similarity
                actionURL: URL(string: "petcapsule://memories/\(memory.id)")!,
                thumbnailImage: nil, // Would load from memory.thumbnailURL
                metadata: [
                    "memoryId": memory.id.uuidString,
                    "type": memory.type.rawValue,
                    "sentiment": memory.sentiment
                ]
            )
        }
    }
    
    // MARK: - Search Result Generation
    
    /// Generates comprehensive search results for Visual Intelligence
    func generateSearchResults(for descriptor: SemanticContentDescriptor, query: String? = nil) async throws -> VisualIntelligenceSearchResults {
        let matches = try await findMatchingContent(for: descriptor)
        
        // Group matches by type
        let groupedMatches = Dictionary(grouping: matches) { $0.type }
        
        // Generate quick actions based on content
        let quickActions = generateQuickActions(for: descriptor, matches: matches)
        
        // Generate contextual suggestions
        let suggestions = generateContextualSuggestions(for: descriptor)
        
        return VisualIntelligenceSearchResults(
            matches: matches,
            groupedMatches: groupedMatches,
            quickActions: quickActions,
            suggestions: suggestions,
            totalResults: matches.count,
            processingTime: 0.5 // This would be measured in real implementation
        )
    }
    
    private func generateQuickActions(for descriptor: SemanticContentDescriptor, matches: [VisualIntelligenceMatch]) -> [QuickAction] {
        var actions: [QuickAction] = []
        
        // Always offer breed identification if pet is detected
        if descriptor.containsPetContent {
            actions.append(
                QuickAction(
                    title: "Identify Breed",
                    icon: "pawprint.circle",
                    actionURL: URL(string: "petcapsule://visual-intelligence/breed-identification")!
                )
            )
        }
        
        // Offer health analysis
        actions.append(
            QuickAction(
                title: "Health Check",
                icon: "heart.circle",
                actionURL: URL(string: "petcapsule://visual-intelligence/health-analysis")!
            )
        )
        
        // Offer to save as memory
        actions.append(
            QuickAction(
                title: "Save Memory",
                icon: "camera.circle",
                actionURL: URL(string: "petcapsule://memories/add")!
            )
        )
        
        return actions
    }
    
    private func generateContextualSuggestions(for descriptor: SemanticContentDescriptor) -> [String] {
        guard let metadata = descriptor.petMetadata else {
            return ["Try taking a clearer photo", "Ensure good lighting"]
        }
        
        var suggestions: [String] = []
        
        if metadata.imageQuality == .low {
            suggestions.append("Try taking a clearer photo for better analysis")
        }
        
        if metadata.lighting == "poor" {
            suggestions.append("Better lighting will improve identification accuracy")
        }
        
        if metadata.hasAnimal {
            suggestions.append("I can identify the breed and provide care tips")
            suggestions.append("Would you like to analyze health indicators?")
        }
        
        return suggestions
    }
}

// MARK: - Supporting Types

@available(iOS 18.0, *)
struct VisualIntelligenceMatch {
    let type: MatchType
    let title: String
    let subtitle: String
    let description: String
    let relevanceScore: Float
    let actionURL: URL
    let thumbnailImage: UIImage?
    let metadata: [String: String]
}

@available(iOS 18.0, *)
enum MatchType: String, CaseIterable {
    case breedInformation = "breed"
    case healthAnalysis = "health"
    case productRecommendation = "product"
    case memoryMatch = "memory"
    case veterinarianRecommendation = "vet"
    
    var displayName: String {
        switch self {
        case .breedInformation: return "Breed Information"
        case .healthAnalysis: return "Health Analysis"
        case .productRecommendation: return "Product Recommendation"
        case .memoryMatch: return "Similar Memory"
        case .veterinarianRecommendation: return "Veterinarian"
        }
    }
    
    var icon: String {
        switch self {
        case .breedInformation: return "pawprint.circle"
        case .healthAnalysis: return "heart.circle"
        case .productRecommendation: return "bag.circle"
        case .memoryMatch: return "photo.circle"
        case .veterinarianRecommendation: return "cross.circle"
        }
    }
}

@available(iOS 18.0, *)
struct QuickAction {
    let title: String
    let icon: String
    let actionURL: URL
}

@available(iOS 18.0, *)
struct VisualIntelligenceSearchResults {
    let matches: [VisualIntelligenceMatch]
    let groupedMatches: [MatchType: [VisualIntelligenceMatch]]
    let quickActions: [QuickAction]
    let suggestions: [String]
    let totalResults: Int
    let processingTime: TimeInterval
}
