//
//  AppIconManager.swift
//  PetCapsule
//
//  iOS 18 App Icon customization with Light, Dark, and Tinted variants
//  Supports dynamic icon switching and theme adaptation
//

import Foundation
import SwiftUI
import UIKit

@available(iOS 18.0, *)
class AppIconManager: ObservableObject {
    static let shared = AppIconManager()
    
    @Published var currentIconName: String = "AppIcon"
    @Published var availableIcons: [AppIconVariant] = []
    @Published var supportsTinting: Bool = false
    
    private init() {
        setupAvailableIcons()
        checkTintingSupport()
        loadCurrentIcon()
    }
    
    // MARK: - Icon Management
    
    private func setupAvailableIcons() {
        availableIcons = [
            AppIconVariant(
                name: "AppIcon",
                displayName: "Default",
                description: "Classic PetCapsule icon",
                iconName: "AppIcon",
                supportsLight: true,
                supportsDark: true,
                supportsTint: true,
                category: .default
            ),
            AppIconVariant(
                name: "AppIcon-Playful",
                displayName: "Playful",
                description: "Fun and colorful design",
                iconName: "AppIcon-Playful",
                supportsLight: true,
                supportsDark: true,
                supportsTint: true,
                category: .themed
            ),
            AppIconVariant(
                name: "AppIcon-Elegant",
                displayName: "Elegant",
                description: "Sophisticated and minimal",
                iconName: "AppIcon-Elegant",
                supportsLight: true,
                supportsDark: true,
                supportsTint: true,
                category: .themed
            ),
            AppIconVariant(
                name: "AppIcon-Veterinary",
                displayName: "Veterinary",
                description: "Professional medical theme",
                iconName: "AppIcon-Veterinary",
                supportsLight: true,
                supportsDark: true,
                supportsTint: false,
                category: .professional
            ),
            AppIconVariant(
                name: "AppIcon-Paw",
                displayName: "Paw Print",
                description: "Simple paw print design",
                iconName: "AppIcon-Paw",
                supportsLight: true,
                supportsDark: true,
                supportsTint: true,
                category: .minimal
            ),
            AppIconVariant(
                name: "AppIcon-Heart",
                displayName: "Love",
                description: "Heart-themed for pet lovers",
                iconName: "AppIcon-Heart",
                supportsLight: true,
                supportsDark: true,
                supportsTint: true,
                category: .themed
            )
        ]
    }
    
    private func checkTintingSupport() {
        // Check if device supports icon tinting (iOS 18+)
        supportsTinting = ProcessInfo.processInfo.operatingSystemVersion.majorVersion >= 18
    }
    
    private func loadCurrentIcon() {
        if let iconName = UIApplication.shared.alternateIconName {
            currentIconName = iconName
        } else {
            currentIconName = "AppIcon"
        }
    }
    
    // MARK: - Icon Switching
    
    func setAppIcon(_ iconVariant: AppIconVariant) async throws {
        guard UIApplication.shared.supportsAlternateIcons else {
            throw AppIconError.notSupported
        }
        
        let iconName = iconVariant.name == "AppIcon" ? nil : iconVariant.name
        
        return try await withCheckedThrowingContinuation { continuation in
            UIApplication.shared.setAlternateIconName(iconName) { error in
                if let error = error {
                    continuation.resume(throwing: error)
                } else {
                    Task { @MainActor in
                        self.currentIconName = iconVariant.name
                    }
                    continuation.resume()
                }
            }
        }
    }
    
    // MARK: - Theme-Based Icon Selection
    
    func getRecommendedIcon(for colorScheme: ColorScheme, userPreference: IconCategory? = nil) -> AppIconVariant {
        let filteredIcons = userPreference != nil 
            ? availableIcons.filter { $0.category == userPreference }
            : availableIcons
        
        // Prefer icons that support the current color scheme
        let compatibleIcons = filteredIcons.filter { icon in
            switch colorScheme {
            case .light:
                return icon.supportsLight
            case .dark:
                return icon.supportsDark
            @unknown default:
                return true
            }
        }
        
        return compatibleIcons.first ?? availableIcons.first!
    }
    
    // MARK: - Automatic Theme Switching
    
    func enableAutomaticIconSwitching(_ enabled: Bool) {
        UserDefaults.standard.set(enabled, forKey: "automatic_icon_switching")
        
        if enabled {
            // Monitor for appearance changes
            NotificationCenter.default.addObserver(
                self,
                selector: #selector(appearanceChanged),
                name: UIApplication.didBecomeActiveNotification,
                object: nil
            )
        } else {
            NotificationCenter.default.removeObserver(self)
        }
    }
    
    @objc private func appearanceChanged() {
        guard UserDefaults.standard.bool(forKey: "automatic_icon_switching") else { return }
        
        Task { @MainActor in
            let currentScheme = UITraitCollection.current.userInterfaceStyle == .dark ? ColorScheme.dark : .light
            let recommendedIcon = getRecommendedIcon(for: currentScheme)
            
            if recommendedIcon.name != currentIconName {
                try? await setAppIcon(recommendedIcon)
            }
        }
    }
    
    // MARK: - Icon Preview Generation
    
    func generateIconPreview(for variant: AppIconVariant, size: CGSize = CGSize(width: 60, height: 60)) -> UIImage? {
        guard let iconImage = UIImage(named: variant.iconName) else {
            return generatePlaceholderIcon(for: variant, size: size)
        }
        
        let renderer = UIGraphicsImageRenderer(size: size)
        return renderer.image { context in
            let rect = CGRect(origin: .zero, size: size)
            
            // Draw rounded rectangle background
            let path = UIBezierPath(roundedRect: rect, cornerRadius: size.width * 0.2)
            context.cgContext.addPath(path.cgPath)
            context.cgContext.clip()
            
            // Draw icon
            iconImage.draw(in: rect)
        }
    }
    
    private func generatePlaceholderIcon(for variant: AppIconVariant, size: CGSize) -> UIImage {
        let renderer = UIGraphicsImageRenderer(size: size)
        return renderer.image { context in
            let rect = CGRect(origin: .zero, size: size)
            
            // Background gradient based on category
            let colors = variant.category.colors
            let gradient = CGGradient(
                colorsSpace: CGColorSpaceCreateDeviceRGB(),
                colors: [colors.0.cgColor, colors.1.cgColor] as CFArray,
                locations: [0.0, 1.0]
            )!
            
            context.cgContext.drawLinearGradient(
                gradient,
                start: CGPoint(x: 0, y: 0),
                end: CGPoint(x: size.width, y: size.height),
                options: []
            )
            
            // Draw paw print symbol
            let symbolConfig = UIImage.SymbolConfiguration(pointSize: size.width * 0.4, weight: .medium)
            let pawImage = UIImage(systemName: "pawprint.fill", withConfiguration: symbolConfig)
            
            if let pawImage = pawImage {
                let symbolRect = CGRect(
                    x: (size.width - pawImage.size.width) / 2,
                    y: (size.height - pawImage.size.height) / 2,
                    width: pawImage.size.width,
                    height: pawImage.size.height
                )
                
                UIColor.white.setFill()
                pawImage.draw(in: symbolRect, blendMode: .normal, alpha: 0.8)
            }
        }
    }
}

// MARK: - Supporting Types

@available(iOS 18.0, *)
struct AppIconVariant: Identifiable, Hashable {
    let id = UUID()
    let name: String
    let displayName: String
    let description: String
    let iconName: String
    let supportsLight: Bool
    let supportsDark: Bool
    let supportsTint: Bool
    let category: IconCategory
}

@available(iOS 18.0, *)
enum IconCategory: String, CaseIterable {
    case `default` = "default"
    case themed = "themed"
    case professional = "professional"
    case minimal = "minimal"
    case seasonal = "seasonal"
    
    var displayName: String {
        switch self {
        case .default: return "Default"
        case .themed: return "Themed"
        case .professional: return "Professional"
        case .minimal: return "Minimal"
        case .seasonal: return "Seasonal"
        }
    }
    
    var colors: (UIColor, UIColor) {
        switch self {
        case .default: return (.systemBlue, .systemPurple)
        case .themed: return (.systemPink, .systemOrange)
        case .professional: return (.systemGreen, .systemTeal)
        case .minimal: return (.systemGray, .systemGray2)
        case .seasonal: return (.systemYellow, .systemRed)
        }
    }
}

@available(iOS 18.0, *)
enum AppIconError: LocalizedError {
    case notSupported
    case invalidIcon
    case systemError(Error)
    
    var errorDescription: String? {
        switch self {
        case .notSupported:
            return "Alternate app icons are not supported on this device"
        case .invalidIcon:
            return "The selected icon is not available"
        case .systemError(let error):
            return "System error: \(error.localizedDescription)"
        }
    }
}

// MARK: - SwiftUI Integration

@available(iOS 18.0, *)
struct AppIconSelectionView: View {
    @StateObject private var iconManager = AppIconManager.shared
    @Environment(\.colorScheme) private var colorScheme
    @State private var selectedCategory: IconCategory = .default
    @State private var automaticSwitching = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // Category Picker
                Picker("Icon Category", selection: $selectedCategory) {
                    ForEach(IconCategory.allCases, id: \.self) { category in
                        Text(category.displayName)
                            .tag(category)
                    }
                }
                .pickerStyle(.segmented)
                .padding(.horizontal)
                
                // Icon Grid
                LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 16), count: 3), spacing: 16) {
                    ForEach(filteredIcons) { icon in
                        IconSelectionCard(
                            icon: icon,
                            isSelected: icon.name == iconManager.currentIconName,
                            onSelect: {
                                Task {
                                    try? await iconManager.setAppIcon(icon)
                                }
                            }
                        )
                    }
                }
                .padding(.horizontal)
                
                Spacer()
                
                // Automatic Switching Toggle
                VStack(spacing: 12) {
                    Toggle("Automatic Theme Switching", isOn: $automaticSwitching)
                        .onChange(of: automaticSwitching) { enabled in
                            iconManager.enableAutomaticIconSwitching(enabled)
                        }
                    
                    Text("Automatically switch app icon based on your device's appearance")
                        .font(.caption)
                        .foregroundStyle(.secondary)
                        .multilineTextAlignment(.center)
                }
                .padding()
                .background(.ultraThinMaterial)
                .clipShape(RoundedRectangle(cornerRadius: 12))
                .padding(.horizontal)
            }
            .navigationTitle("App Icon")
            .navigationBarTitleDisplayMode(.large)
        }
        .onAppear {
            automaticSwitching = UserDefaults.standard.bool(forKey: "automatic_icon_switching")
        }
    }
    
    private var filteredIcons: [AppIconVariant] {
        iconManager.availableIcons.filter { $0.category == selectedCategory }
    }
}

@available(iOS 18.0, *)
struct IconSelectionCard: View {
    let icon: AppIconVariant
    let isSelected: Bool
    let onSelect: () -> Void
    
    @StateObject private var iconManager = AppIconManager.shared
    
    var body: some View {
        Button(action: onSelect) {
            VStack(spacing: 8) {
                // Icon Preview
                if let iconImage = iconManager.generateIconPreview(for: icon) {
                    Image(uiImage: iconImage)
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(width: 60, height: 60)
                        .clipShape(RoundedRectangle(cornerRadius: 12))
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(isSelected ? .blue : .clear, lineWidth: 3)
                        )
                } else {
                    RoundedRectangle(cornerRadius: 12)
                        .fill(.gray.opacity(0.3))
                        .frame(width: 60, height: 60)
                        .overlay(
                            Image(systemName: "pawprint.fill")
                                .foregroundStyle(.gray)
                        )
                }
                
                // Icon Info
                VStack(spacing: 2) {
                    Text(icon.displayName)
                        .font(.caption)
                        .fontWeight(.medium)
                        .lineLimit(1)
                    
                    Text(icon.description)
                        .font(.caption2)
                        .foregroundStyle(.secondary)
                        .lineLimit(2)
                        .multilineTextAlignment(.center)
                }
                
                // Feature Indicators
                HStack(spacing: 4) {
                    if icon.supportsLight {
                        Image(systemName: "sun.max.fill")
                            .font(.caption2)
                            .foregroundStyle(.yellow)
                    }
                    
                    if icon.supportsDark {
                        Image(systemName: "moon.fill")
                            .font(.caption2)
                            .foregroundStyle(.blue)
                    }
                    
                    if icon.supportsTint && iconManager.supportsTinting {
                        Image(systemName: "paintbrush.fill")
                            .font(.caption2)
                            .foregroundStyle(.purple)
                    }
                }
            }
            .padding(12)
            .background(isSelected ? .blue.opacity(0.1) : .clear)
            .clipShape(RoundedRectangle(cornerRadius: 16))
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(isSelected ? .blue : .gray.opacity(0.3), lineWidth: 1)
            )
        }
        .buttonStyle(.plain)
    }
}
