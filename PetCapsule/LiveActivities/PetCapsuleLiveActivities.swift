//
//  PetCapsuleLiveActivities.swift
//  PetCapsule
//
//  iOS 18 Live Activities for real-time pet care updates
//  Dynamic Island and Lock Screen integration
//

import Foundation
import SwiftUI
import WidgetKit

// Note: ActivityKit may not be available in current iOS versions
// This is a forward-looking implementation for iOS 18

// MARK: - Live Activity Attributes (Simulated for iOS 18)

@available(iOS 18.0, *)
struct PetCareActivityAttributes {
    public struct ContentState: Codable, Hashable {
        var activityType: PetActivityType
        var petName: String
        var currentStatus: String
        var progress: Double
        var timeRemaining: TimeInterval?
        var nextAction: String?
        var urgencyLevel: UrgencyLevel
        var lastUpdated: Date
    }

    var petId: String
    var petName: String
    var activityTitle: String
}

@available(iOS 18.0, *)
enum PetActivityType: String, Codable, CaseIterable {
    case walk = "walk"
    case medication = "medication"
    case feeding = "feeding"
    case grooming = "grooming"
    case vetAppointment = "vet_appointment"
    case training = "training"
    case emergency = "emergency"
    
    var displayName: String {
        switch self {
        case .walk: return "Walk"
        case .medication: return "Medication"
        case .feeding: return "Feeding"
        case .grooming: return "Grooming"
        case .vetAppointment: return "Vet Visit"
        case .training: return "Training"
        case .emergency: return "Emergency"
        }
    }
    
    var icon: String {
        switch self {
        case .walk: return "figure.walk"
        case .medication: return "pills.fill"
        case .feeding: return "bowl.fill"
        case .grooming: return "scissors"
        case .vetAppointment: return "stethoscope"
        case .training: return "brain.head.profile"
        case .emergency: return "exclamationmark.triangle.fill"
        }
    }
    
    var color: Color {
        switch self {
        case .walk: return .green
        case .medication: return .blue
        case .feeding: return .orange
        case .grooming: return .purple
        case .vetAppointment: return .teal
        case .training: return .indigo
        case .emergency: return .red
        }
    }
}

@available(iOS 18.0, *)
enum UrgencyLevel: String, Codable {
    case low = "low"
    case medium = "medium"
    case high = "high"
    case critical = "critical"
    
    var color: Color {
        switch self {
        case .low: return .green
        case .medium: return .yellow
        case .high: return .orange
        case .critical: return .red
        }
    }
}

// MARK: - Live Activity Widget (Simulated for iOS 18)

@available(iOS 18.0, *)
struct PetCareActivityWidget {
    // This would be the actual Live Activity widget when ActivityKit is available
    static let kind = "PetCareActivityWidget"

    // Simulated configuration for Live Activities
    static let configuration = LiveActivityConfiguration(
        kind: kind,
        displayName: "Pet Care Activities",
        description: "Real-time updates for pet care activities"
    )
}

// MARK: - Lock Screen View (Simulated)

@available(iOS 18.0, *)
struct PetCareLockScreenView: View {
    let activity: SimulatedActivity
    
    var body: some View {
        VStack(spacing: 12) {
            // Header
            HStack {
                Image(systemName: activity.contentState.activityType.icon)
                    .foregroundStyle(activity.contentState.activityType.color)
                    .font(.title2)

                VStack(alignment: .leading, spacing: 2) {
                    Text(activity.attributes.activityTitle)
                        .font(.headline)
                        .fontWeight(.semibold)

                    Text(activity.contentState.petName)
                        .font(.caption)
                        .foregroundStyle(.secondary)
                }

                Spacer()

                // Urgency Indicator
                Circle()
                    .fill(activity.contentState.urgencyLevel.color)
                    .frame(width: 12, height: 12)
            }
            
            // Progress Bar
            ProgressView(value: activity.contentState.progress, total: 1.0)
                .progressViewStyle(LinearProgressViewStyle(tint: activity.contentState.activityType.color))
                .scaleEffect(y: 2)
            
            // Status and Actions
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Status")
                        .font(.caption2)
                        .foregroundStyle(.secondary)
                    
                    Text(activity.contentState.currentStatus)
                        .font(.caption)
                        .fontWeight(.medium)
                }

                Spacer()

                if let timeRemaining = activity.contentState.timeRemaining {
                    VStack(alignment: .trailing, spacing: 4) {
                        Text("Time Left")
                            .font(.caption2)
                            .foregroundStyle(.secondary)

                        Text(formatTimeInterval(timeRemaining))
                            .font(.caption)
                            .fontWeight(.semibold)
                            .foregroundStyle(activity.contentState.urgencyLevel.color)
                    }
                }
            }
            
            // Next Action
            if let nextAction = activity.contentState.nextAction {
                HStack {
                    Image(systemName: "arrow.right.circle.fill")
                        .foregroundStyle(.blue)
                        .font(.caption)
                    
                    Text(nextAction)
                        .font(.caption)
                        .fontWeight(.medium)
                    
                    Spacer()
                }
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(.blue.opacity(0.1))
                .clipShape(RoundedRectangle(cornerRadius: 8))
            }
        }
        .padding(16)
        .background(.ultraThinMaterial)
        .clipShape(RoundedRectangle(cornerRadius: 16))
    }

    private func formatTimeInterval(_ interval: TimeInterval) -> String {
        let minutes = Int(interval) / 60
        let seconds = Int(interval) % 60
        return String(format: "%02d:%02d", minutes, seconds)
    }
}

// MARK: - Dynamic Island Views

@available(iOS 18.0, *)
struct PetActivityLeadingView: View {
    let activity: SimulatedActivity

    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            HStack {
                Image(systemName: activity.contentState.activityType.icon)
                    .foregroundStyle(activity.contentState.activityType.color)
                    .font(.title3)

                Text(activity.contentState.activityType.displayName)
                    .font(.caption)
                    .fontWeight(.semibold)
            }

            Text(activity.contentState.petName)
                .font(.caption2)
                .foregroundStyle(.secondary)
        }
    }
}

@available(iOS 18.0, *)
struct PetActivityTrailingView: View {
    let activity: SimulatedActivity

    var body: some View {
        VStack(alignment: .trailing, spacing: 4) {
            if let timeRemaining = activity.contentState.timeRemaining {
                Text(formatTimeInterval(timeRemaining))
                    .font(.caption)
                    .fontWeight(.bold)
                    .foregroundStyle(activity.contentState.urgencyLevel.color)

                Text("remaining")
                    .font(.caption2)
                    .foregroundStyle(.secondary)
            } else {
                Text(String(format: "%.0f%%", activity.contentState.progress * 100))
                    .font(.caption)
                    .fontWeight(.bold)
                    .foregroundStyle(activity.contentState.activityType.color)

                Text("complete")
                    .font(.caption2)
                    .foregroundStyle(.secondary)
            }
        }
    }

    private func formatTimeInterval(_ interval: TimeInterval) -> String {
        let minutes = Int(interval) / 60
        let seconds = Int(interval) % 60
        return String(format: "%02d:%02d", minutes, seconds)
    }
}

@available(iOS 18.0, *)
struct PetActivityBottomView: View {
    let activity: SimulatedActivity

    var body: some View {
        VStack(spacing: 8) {
            // Progress Bar
            ProgressView(value: activity.contentState.progress, total: 1.0)
                .progressViewStyle(LinearProgressViewStyle(tint: activity.contentState.activityType.color))

            // Status
            HStack {
                Text(activity.contentState.currentStatus)
                    .font(.caption)
                    .fontWeight(.medium)

                Spacer()

                if let nextAction = activity.contentState.nextAction {
                    Text(nextAction)
                        .font(.caption2)
                        .foregroundStyle(.secondary)
                }
            }
        }
    }
}

// MARK: - Supporting Types

@available(iOS 18.0, *)
struct LiveActivityConfiguration {
    let kind: String
    let displayName: String
    let description: String
}

@available(iOS 18.0, *)
struct SimulatedActivity {
    let id: String
    let attributes: PetCareActivityAttributes
    var contentState: PetCareActivityAttributes.ContentState
}

// MARK: - Live Activity Manager (Simulated)

@available(iOS 18.0, *)
class PetLiveActivityManager: ObservableObject {
    static let shared = PetLiveActivityManager()

    @Published var activeActivities: [SimulatedActivity] = []

    private init() {
        loadActiveActivities()
    }
    
    // MARK: - Activity Management (Simulated)

    func startActivity(
        petId: String,
        petName: String,
        activityType: PetActivityType,
        title: String,
        initialStatus: String,
        duration: TimeInterval? = nil
    ) async throws -> SimulatedActivity {

        let attributes = PetCareActivityAttributes(
            petId: petId,
            petName: petName,
            activityTitle: title
        )

        let initialState = PetCareActivityAttributes.ContentState(
            activityType: activityType,
            petName: petName,
            currentStatus: initialStatus,
            progress: 0.0,
            timeRemaining: duration,
            nextAction: getNextAction(for: activityType),
            urgencyLevel: .medium,
            lastUpdated: Date()
        )

        let activity = SimulatedActivity(
            id: UUID().uuidString,
            attributes: attributes,
            contentState: initialState
        )

        await MainActor.run {
            activeActivities.append(activity)
        }

        return activity
    }
    
    func updateActivity(
        _ activity: SimulatedActivity,
        status: String,
        progress: Double,
        timeRemaining: TimeInterval? = nil,
        urgencyLevel: UrgencyLevel = .medium
    ) async {

        let updatedState = PetCareActivityAttributes.ContentState(
            activityType: activity.contentState.activityType,
            petName: activity.contentState.petName,
            currentStatus: status,
            progress: progress,
            timeRemaining: timeRemaining,
            nextAction: getNextAction(for: activity.contentState.activityType, progress: progress),
            urgencyLevel: urgencyLevel,
            lastUpdated: Date()
        )

        // Simulate activity update
        await MainActor.run {
            if let index = activeActivities.firstIndex(where: { $0.id == activity.id }) {
                activeActivities[index].contentState = updatedState
            }
        }
    }

    func endActivity(_ activity: SimulatedActivity, finalStatus: String) async {
        await MainActor.run {
            activeActivities.removeAll { $0.id == activity.id }
        }
    }
    
    // MARK: - Predefined Activities

    func startWalkActivity(petId: String, petName: String, duration: TimeInterval) async throws {
        _ = try await startActivity(
            petId: petId,
            petName: petName,
            activityType: .walk,
            title: "Walk in Progress",
            initialStatus: "Starting walk...",
            duration: duration
        )
    }

    func startMedicationReminder(petId: String, petName: String, medicationName: String) async throws {
        _ = try await startActivity(
            petId: petId,
            petName: petName,
            activityType: .medication,
            title: "Medication Time",
            initialStatus: "Time for \(medicationName)"
        )
    }

    func startVetAppointment(petId: String, petName: String, appointmentTime: Date) async throws {
        let timeUntilAppointment = appointmentTime.timeIntervalSinceNow

        _ = try await startActivity(
            petId: petId,
            petName: petName,
            activityType: .vetAppointment,
            title: "Vet Appointment",
            initialStatus: "Appointment scheduled",
            duration: timeUntilAppointment
        )
    }

    func startEmergencyAlert(petId: String, petName: String, emergencyType: String) async throws {
        _ = try await startActivity(
            petId: petId,
            petName: petName,
            activityType: .emergency,
            title: "Pet Emergency",
            initialStatus: emergencyType
        )
    }
    
    // MARK: - Utility Methods
    
    private func loadActiveActivities() {
        Task {
            // Simulate loading active activities
            await MainActor.run {
                self.activeActivities = []
            }
        }
    }
    
    private func getNextAction(for activityType: PetActivityType, progress: Double = 0.0) -> String? {
        switch activityType {
        case .walk:
            if progress < 0.5 {
                return "Continue walking"
            } else {
                return "Head back home"
            }
        case .medication:
            return "Give medication now"
        case .feeding:
            return "Prepare food"
        case .grooming:
            return "Start grooming session"
        case .vetAppointment:
            return "Prepare for appointment"
        case .training:
            return "Begin training session"
        case .emergency:
            return "Contact veterinarian"
        }
    }
}

// MARK: - SwiftUI Integration

@available(iOS 18.0, *)
struct LiveActivityControlView: View {
    @StateObject private var activityManager = PetLiveActivityManager.shared
    @State private var selectedPet: Pet?
    @State private var selectedActivityType: PetActivityType = .walk
    
    var body: some View {
        VStack(spacing: 20) {
            // Active Activities
            if !activityManager.activeActivities.isEmpty {
                VStack(alignment: .leading, spacing: 12) {
                    Text("Active Activities")
                        .font(.headline)
                        .fontWeight(.bold)
                    
                    ForEach(activityManager.activeActivities, id: \.id) { activity in
                        ActiveActivityCard(activity: activity)
                    }
                }
            }
            
            // Start New Activity
            VStack(alignment: .leading, spacing: 16) {
                Text("Start New Activity")
                    .font(.headline)
                    .fontWeight(.bold)
                
                // Activity Type Picker
                Picker("Activity Type", selection: $selectedActivityType) {
                    ForEach(PetActivityType.allCases, id: \.self) { type in
                        Label(type.displayName, systemImage: type.icon)
                            .tag(type)
                    }
                }
                .pickerStyle(.menu)
                
                // Quick Start Buttons
                LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                    QuickStartButton(
                        title: "Start Walk",
                        icon: "figure.walk",
                        color: .green
                    ) {
                        await startWalkActivity()
                    }
                    
                    QuickStartButton(
                        title: "Medication",
                        icon: "pills.fill",
                        color: .blue
                    ) {
                        await startMedicationActivity()
                    }
                    
                    QuickStartButton(
                        title: "Vet Visit",
                        icon: "stethoscope",
                        color: .teal
                    ) {
                        await startVetActivity()
                    }
                    
                    QuickStartButton(
                        title: "Emergency",
                        icon: "exclamationmark.triangle.fill",
                        color: .red
                    ) {
                        await startEmergencyActivity()
                    }
                }
            }
        }
        .padding()
    }
    
    private func startWalkActivity() async {
        guard let pet = selectedPet else { return }
        
        do {
            try await activityManager.startWalkActivity(
                petId: pet.id.uuidString,
                petName: pet.name,
                duration: 1800 // 30 minutes
            )
        } catch {
            print("Failed to start walk activity: \(error)")
        }
    }
    
    private func startMedicationActivity() async {
        guard let pet = selectedPet else { return }
        
        do {
            try await activityManager.startMedicationReminder(
                petId: pet.id.uuidString,
                petName: pet.name,
                medicationName: "Daily Vitamins"
            )
        } catch {
            print("Failed to start medication activity: \(error)")
        }
    }
    
    private func startVetActivity() async {
        guard let pet = selectedPet else { return }
        
        do {
            let appointmentTime = Calendar.current.date(byAdding: .hour, value: 2, to: Date()) ?? Date()
            try await activityManager.startVetAppointment(
                petId: pet.id.uuidString,
                petName: pet.name,
                appointmentTime: appointmentTime
            )
        } catch {
            print("Failed to start vet activity: \(error)")
        }
    }
    
    private func startEmergencyActivity() async {
        guard let pet = selectedPet else { return }
        
        do {
            try await activityManager.startEmergencyAlert(
                petId: pet.id.uuidString,
                petName: pet.name,
                emergencyType: "Urgent care needed"
            )
        } catch {
            print("Failed to start emergency activity: \(error)")
        }
    }
}

@available(iOS 18.0, *)
struct ActiveActivityCard: View {
    let activity: SimulatedActivity

    var body: some View {
        HStack {
            Image(systemName: activity.contentState.activityType.icon)
                .foregroundStyle(activity.contentState.activityType.color)
                .font(.title2)

            VStack(alignment: .leading, spacing: 4) {
                Text(activity.attributes.activityTitle)
                    .font(.subheadline)
                    .fontWeight(.semibold)

                Text(activity.contentState.currentStatus)
                    .font(.caption)
                    .foregroundStyle(.secondary)
            }

            Spacer()

            if let timeRemaining = activity.contentState.timeRemaining {
                Text(formatTimeInterval(timeRemaining))
                    .font(.caption)
                    .fontWeight(.semibold)
                    .foregroundStyle(activity.contentState.urgencyLevel.color)
            }
        }
        .padding(12)
        .background(.ultraThinMaterial)
        .clipShape(RoundedRectangle(cornerRadius: 12))
    }

    private func formatTimeInterval(_ interval: TimeInterval) -> String {
        let minutes = Int(interval) / 60
        let seconds = Int(interval) % 60
        return String(format: "%02d:%02d", minutes, seconds)
    }
}

@available(iOS 18.0, *)
struct QuickStartButton: View {
    let title: String
    let icon: String
    let color: Color
    let action: () async -> Void
    
    var body: some View {
        Button(action: {
            Task {
                await action()
            }
        }) {
            VStack(spacing: 8) {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundStyle(color)
                
                Text(title)
                    .font(.caption)
                    .fontWeight(.medium)
                    .multilineTextAlignment(.center)
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 16)
            .background(color.opacity(0.1))
            .clipShape(RoundedRectangle(cornerRadius: 12))
        }
        .buttonStyle(.plain)
    }
}
