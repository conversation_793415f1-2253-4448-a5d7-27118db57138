//
//  APIKeys.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 6/6/25.
//

import Foundation

struct APIKeys {
    // Google APIs
    static let googleMapsAPIKey = "AIzaSyBvOxKrjHYxKrjHYxKrjHYxKrjHYxKrjHYx" // Replace with actual key
    static let googlePlacesAPIKey = "AIzaSyBvOxKrjHYxKrjHYxKrjHYxKrjHYxKrjHYx" // Replace with actual key
    
    // Weather API
    static let openWeatherAPIKey = "your_openweather_api_key" // Get from openweathermap.org
    
    // Service Account Configuration
    static let googleServiceAccount = GoogleServiceAccount(
        projectId: "nira-460718",
        clientEmail: "<EMAIL>",
        privateKeyId: "16a76b0598fe88369f63f7219f0bb5a6cc992a03",
        privateKey: """
        -----<PERSON><PERSON><PERSON> PRIVATE KEY-----
        MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQDFN4UtVWihPfqc
        wavBrqZbCZasEDqn4nD/KC4s3RJprG+pTq1ArwgtD6CEzfhDpx5LZZY2XjaKwUvw
        3mkb1YVM7MQ6mhBn5oKOaEZodbq5reuWTJ9wtcDtZ5xUIy04j/zA4T/RH48zZOr1
        d6wceY6v9p7ZN95aPS2gCg9oGz2fcEWHK8UrSkBnJs+wwFrs1tAl/NIdf01UP1MA
        KiTL+3vZSygViiQLSGeNw4cOOOZYMsqrgY/rZUppJ6Aa51InIYu9qj8g1Y7q6kTg
        l3PQbJhdT/ILHkVmbJMNr5UAH3yuGRJB2NCDNPseEsfqsovQfGgDUEHq/Vgs9ReW
        5dYeCaNLAgMBAAECggEAXMvMAVG/wqb8JghG/gJkyk9PszSLtPUqgBTy7S80e76k
        Z8DaaqYxLaq9t9+Goqae7ZEmRfy8oztitTqhsHQendyGVKCreYgQBFvyTTKgIsBA
        Kt9w4kLTPaHUAA7xNz0hC8yxD4BPDDq/B7NAq8FSr19WzRSprkI9FqHFMBIpQ7xn
        nkbHxHIw0cPHNR1KpuKARWUFvEtliq9G/SAn/LAVB6Yp8lpLBJBi/63wxeymvYvP
        XQga52ZxtUfE5RwxlUiN7oLPLLsTDDAWFS3zt1SCeeppHjFxSZhUUGDSjo3RpMnl
        X+cncGp/hYD2X1/C9oi3cilCWqmGO5U3IZS8hw0XmQKBgQDnjJEFyXY/nfo35PWo
        xfMI1NnWeQAvHrlLg9Dq7zRPeu5haZqg9mmObwkqTyqvaWODoat2fzocRWUKHsh9
        uy9pUrwSxf81qscfDrxDYA1xcf/k/MLvqcIryuj5y63xI3eXsxWkZvW1GpN3AFnC
        H1BRsVcojVI6FJQHXdRAAQUoDwKBgQDaCtseZLWM2C3BY3fhQz9FPxx3DaDdp32y
        cIrT7UxAjWPYG/ca40wvcvMK6twzFEdv8TnlDzYjOafz2FS8QFxWJSgWsPDMu8gs
        376j9pmqBC+CDBGz+Czuluu9iaHzZBTYqMaMWw73YTpc8Rpyjgihd13NjAa2FQej
        wSBpj091BQKBgQDFerc87kNxonSDMzzpAlpyz8YyCuTDSzrRUszdccF1f+zLG9wL
        J92C4uQ4N2i6TfHXlAy+dWfEMNMpvHNV+E1JUIyinkc5KDvHGzQpakRM2uNaEtDW
        wjueSeJFGRdyr8MRmGT+3+jy2oWmWqAy1a4U3mDtbwoQCvjtB3M8WyJgtQKBgHd9
        WYV3dVLBKZoAc7LcEnFVW7kWm+C8qBLAwCPRuGbr1RqagLmYdKjQGscR3CiV5WxA
        SxTvWCDHuPXVyOH9d1yyMhz1TJE2G0YUO7pJKv7UGmDvUkhJKf56cKhHI5xpbU8q
        dtx2DmadPsmuS2sZ0TsDWkGJiigAsFYaQTP4GHgtAoGBAJhxZnqOm+vmrjXh8Lz8
        1Pkcr3H7d4EBpi4tsKPErWKv1FHpD8bazIkPgIb4RxqT5EcYSTXrYHJnMldgYwFK
        DNZnxjxnCt8hZnjzzwMpcPJ9uum7N8peDfvEKCzoCbmqhVVxSmRzV8lIpJmHhcQm
        6uiU4PoGBnWSUwCMTJ7o+V8h
        -----END PRIVATE KEY-----
        """
    )
    
    // Helper method to get API key for specific service
    static func getAPIKey(for service: APIService) -> String {
        switch service {
        case .googleMaps:
            return googleMapsAPIKey
        case .googlePlaces:
            return googlePlacesAPIKey
        case .openWeather:
            return openWeatherAPIKey
        }
    }
}

enum APIService {
    case googleMaps
    case googlePlaces
    case openWeather
}

struct GoogleServiceAccount {
    let projectId: String
    let clientEmail: String
    let privateKeyId: String
    let privateKey: String
    
    // Generate JWT token for service account authentication
    func generateJWT() -> String? {
        // In a real implementation, you would use a JWT library
        // For now, we'll use API key authentication
        return nil
    }
}

// MARK: - API Endpoints

struct APIEndpoints {
    // Google APIs
    static let googleMapsBase = "https://maps.googleapis.com/maps/api"
    static let googlePlacesBase = "https://places.googleapis.com/v1"
    
    // Weather APIs
    static let openWeatherBase = "https://api.openweathermap.org/data/2.5"
    static let airQualityBase = "https://api.openweathermap.org/data/2.5/air_pollution"
    
    // Specific endpoints
    struct GoogleMaps {
        static let geocoding = "\(googleMapsBase)/geocode/json"
        static let directions = "\(googleMapsBase)/directions/json"
        static let distanceMatrix = "\(googleMapsBase)/distancematrix/json"
    }
    
    struct GooglePlaces {
        static let searchNearby = "\(googlePlacesBase)/places:searchNearby"
        static let placeDetails = "\(googlePlacesBase)/places"
        static let placePhotos = "\(googlePlacesBase)/places"
    }
    
    struct OpenWeather {
        static let currentWeather = "\(openWeatherBase)/weather"
        static let forecast = "\(openWeatherBase)/forecast"
        static let airPollution = "\(airQualityBase)/current"
        static let airPollutionForecast = "\(airQualityBase)/forecast"
    }
}

// MARK: - API Configuration

struct APIConfiguration {
    // Request timeouts
    static let defaultTimeout: TimeInterval = 30.0
    static let uploadTimeout: TimeInterval = 60.0
    
    // Rate limiting
    static let maxRequestsPerMinute = 60
    static let maxRequestsPerHour = 1000
    
    // Cache settings
    static let cacheExpirationTime: TimeInterval = 300 // 5 minutes
    static let maxCacheSize = 50 * 1024 * 1024 // 50MB
    
    // Error handling
    static let maxRetryAttempts = 3
    static let retryDelay: TimeInterval = 1.0
}

// MARK: - API Response Models

struct APIResponse<T: Codable>: Codable {
    let data: T?
    let error: APIError?
    let timestamp: Date
    let requestId: String
}

struct APIError: Codable, Error {
    let code: String
    let message: String
    let details: [String: String]?
}

// MARK: - Network Manager

class NetworkManager {
    static let shared = NetworkManager()
    
    private let session: URLSession
    
    private init() {
        let config = URLSessionConfiguration.default
        config.timeoutIntervalForRequest = APIConfiguration.defaultTimeout
        config.timeoutIntervalForResource = APIConfiguration.uploadTimeout
        self.session = URLSession(configuration: config)
    }
    
    func request<T: Codable>(
        url: URL,
        method: HTTPMethod = .GET,
        headers: [String: String]? = nil,
        body: Data? = nil,
        responseType: T.Type
    ) async throws -> T {
        var request = URLRequest(url: url)
        request.httpMethod = method.rawValue
        request.httpBody = body
        
        // Add headers
        headers?.forEach { key, value in
            request.setValue(value, forHTTPHeaderField: key)
        }
        
        // Add default headers
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue("PetCapsule/1.0", forHTTPHeaderField: "User-Agent")
        
        let (data, response) = try await session.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse else {
            throw APIError(code: "INVALID_RESPONSE", message: "Invalid response type", details: nil)
        }
        
        guard 200...299 ~= httpResponse.statusCode else {
            throw APIError(
                code: "HTTP_ERROR",
                message: "HTTP error \(httpResponse.statusCode)",
                details: ["statusCode": "\(httpResponse.statusCode)"]
            )
        }
        
        do {
            return try JSONDecoder().decode(T.self, from: data)
        } catch {
            throw APIError(
                code: "DECODE_ERROR",
                message: "Failed to decode response",
                details: ["error": error.localizedDescription]
            )
        }
    }
}

enum HTTPMethod: String {
    case GET = "GET"
    case POST = "POST"
    case PUT = "PUT"
    case DELETE = "DELETE"
    case PATCH = "PATCH"
}
