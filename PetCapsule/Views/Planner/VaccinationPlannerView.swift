//
//  VaccinationPlannerView.swift
//  PetCapsule
//
//  Vaccination planning and tracking view
//

import SwiftUI

struct VaccinationPlannerView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var petService = RealDataService()
    @State private var selectedPet: Pet?
    @State private var showAddVaccination = false
    @State private var vaccinations: [VaccinationRecord] = []
    @State private var upcomingVaccinations: [VaccinationRecord] = []
    
    var body: some View {
        NavigationView {
            ScrollView {
                LazyVStack(spacing: 20) {
                    // Pet Selection
                    petSelectionSection
                    
                    if selectedPet != nil {
                        // Upcoming Vaccinations
                        upcomingVaccinationsSection
                        
                        // Vaccination History
                        vaccinationHistorySection
                        
                        // Quick Actions
                        quickActionsSection
                    }
                }
                .padding()
                .padding(.bottom, 100)
            }
            .navigationTitle("Vaccination Planner")
            .navigationBarTitleDisplayMode(.large)
            .background(Color.themeBackground)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    But<PERSON>("Close") {
                        dismiss()
                    }
                }
                
                if selectedPet != nil {
                    ToolbarItem(placement: .navigationBarTrailing) {
                        Button(action: { showAddVaccination = true }) {
                            Image(systemName: "plus")
                                .font(.title3)
                                .foregroundColor(.themeAccent)
                        }
                    }
                }
            }
        }
        .sheet(isPresented: $showAddVaccination) {
            AddVaccinationView(pet: selectedPet)
        }
        .onAppear {
            loadVaccinations()
        }
    }
    
    // MARK: - Pet Selection Section
    
    private var petSelectionSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Select Pet")
                .font(.petTitle2)
                .fontWeight(.bold)
                .foregroundColor(.themePrimary)
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(petService.pets) { pet in
                        petSelectionCard(pet: pet)
                    }
                }
                .padding(.horizontal, 4)
            }
        }
    }
    
    private func petSelectionCard(pet: Pet) -> some View {
        Button(action: {
            selectedPet = pet
            loadVaccinationsForPet(pet)
        }) {
            VStack(spacing: 8) {
                // Pet Image
                Circle()
                    .fill(selectedPet?.id == pet.id ? Color.themeAccent.opacity(0.2) : Color.gray.opacity(0.1))
                    .frame(width: 60, height: 60)
                    .overlay(
                        Text(pet.name.prefix(1))
                            .font(.title2)
                            .fontWeight(.bold)
                            .foregroundColor(selectedPet?.id == pet.id ? .themeAccent : .gray)
                    )
                
                Text(pet.name)
                    .font(.petCaption)
                    .fontWeight(.medium)
                    .foregroundColor(selectedPet?.id == pet.id ? .themeAccent : .themePrimary)
            }
            .padding(.vertical, 8)
            .padding(.horizontal, 12)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(selectedPet?.id == pet.id ? Color.themeAccent.opacity(0.1) : Color.themeCardBackground)
                    .stroke(selectedPet?.id == pet.id ? Color.themeAccent : Color.clear, lineWidth: 2)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    // MARK: - Upcoming Vaccinations Section
    
    private var upcomingVaccinationsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "calendar.badge.exclamationmark")
                    .font(.title2)
                    .foregroundColor(.orange)
                
                Text("Upcoming Vaccinations")
                    .font(.petTitle2)
                    .fontWeight(.bold)
                    .foregroundColor(.themePrimary)
                
                Spacer()
            }
            
            if upcomingVaccinations.isEmpty {
                emptyUpcomingView
            } else {
                LazyVStack(spacing: 12) {
                    ForEach(upcomingVaccinations) { vaccination in
                        upcomingVaccinationCard(vaccination)
                    }
                }
            }
        }
    }
    
    private var emptyUpcomingView: some View {
        VStack(spacing: 12) {
            Image(systemName: "checkmark.shield.fill")
                .font(.largeTitle)
                .foregroundColor(.green)
            
            Text("All Up to Date!")
                .font(.petSubheadline)
                .fontWeight(.semibold)
                .foregroundColor(.themePrimary)
            
            Text("No upcoming vaccinations scheduled")
                .font(.petCaption)
                .foregroundColor(.themeSecondary)
        }
        .padding(.vertical, 20)
        .frame(maxWidth: .infinity)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.green.opacity(0.1))
        )
    }
    
    private func upcomingVaccinationCard(_ vaccination: VaccinationRecord) -> some View {
        HStack(spacing: 16) {
            // Urgency Indicator
            Circle()
                .fill(vaccination.urgencyColor)
                .frame(width: 12, height: 12)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(vaccination.vaccineName)
                    .font(.petSubheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.themePrimary)
                
                Text("Due: \(vaccination.dueDate.formatted(date: .abbreviated, time: .omitted))")
                    .font(.petCaption)
                    .foregroundColor(.themeSecondary)
            }
            
            Spacer()
            
            Text(vaccination.daysUntilDue)
                .font(.petCaption2)
                .fontWeight(.bold)
                .foregroundColor(.white)
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(
                    Capsule()
                        .fill(vaccination.urgencyColor)
                )
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.themeCardBackground)
                .stroke(vaccination.urgencyColor.opacity(0.3), lineWidth: 1)
        )
    }
    
    // MARK: - Vaccination History Section
    
    private var vaccinationHistorySection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "list.clipboard.fill")
                    .font(.title2)
                    .foregroundColor(.blue)
                
                Text("Vaccination History")
                    .font(.petTitle2)
                    .fontWeight(.bold)
                    .foregroundColor(.themePrimary)
                
                Spacer()
            }
            
            if vaccinations.isEmpty {
                emptyHistoryView
            } else {
                LazyVStack(spacing: 12) {
                    ForEach(vaccinations) { vaccination in
                        vaccinationHistoryCard(vaccination)
                    }
                }
            }
        }
    }
    
    private var emptyHistoryView: some View {
        VStack(spacing: 12) {
            Image(systemName: "doc.text.fill")
                .font(.largeTitle)
                .foregroundColor(.gray)
            
            Text("No Records Yet")
                .font(.petSubheadline)
                .fontWeight(.semibold)
                .foregroundColor(.themePrimary)
            
            Text("Add vaccination records to track your pet's health")
                .font(.petCaption)
                .foregroundColor(.themeSecondary)
                .multilineTextAlignment(.center)
        }
        .padding(.vertical, 20)
        .frame(maxWidth: .infinity)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.gray.opacity(0.1))
        )
    }
    
    private func vaccinationHistoryCard(_ vaccination: VaccinationRecord) -> some View {
        HStack(spacing: 16) {
            // Status Icon
            Image(systemName: vaccination.isCompleted ? "checkmark.circle.fill" : "clock.fill")
                .font(.title3)
                .foregroundColor(vaccination.isCompleted ? .green : .orange)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(vaccination.vaccineName)
                    .font(.petSubheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.themePrimary)
                
                Text(vaccination.isCompleted ? 
                     "Completed: \(vaccination.completedDate?.formatted(date: .abbreviated, time: .omitted) ?? "Unknown")" :
                     "Scheduled: \(vaccination.dueDate.formatted(date: .abbreviated, time: .omitted))")
                    .font(.petCaption)
                    .foregroundColor(.themeSecondary)
                
                if let veterinarian = vaccination.veterinarian {
                    Text("Vet: \(veterinarian)")
                        .font(.petCaption2)
                        .foregroundColor(.themeSecondary)
                }
            }
            
            Spacer()
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.themeCardBackground)
                .stroke(Color.themeBorder.opacity(0.1), lineWidth: 1)
        )
    }
    
    // MARK: - Quick Actions Section
    
    private var quickActionsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Quick Actions")
                .font(.petTitle2)
                .fontWeight(.bold)
                .foregroundColor(.themePrimary)
            
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 12) {
                quickActionButton(
                    icon: "plus.circle.fill",
                    title: "Add Vaccination",
                    color: .blue,
                    action: { showAddVaccination = true }
                )
                
                quickActionButton(
                    icon: "calendar.badge.plus",
                    title: "Schedule Reminder",
                    color: .orange,
                    action: { /* Schedule reminder */ }
                )
                
                quickActionButton(
                    icon: "doc.text.fill",
                    title: "Export Records",
                    color: .green,
                    action: { /* Export records */ }
                )
                
                quickActionButton(
                    icon: "phone.fill",
                    title: "Call Vet",
                    color: .red,
                    action: { /* Call vet */ }
                )
            }
        }
    }
    
    private func quickActionButton(icon: String, title: String, color: Color, action: @escaping () -> Void) -> some View {
        Button(action: action) {
            VStack(spacing: 8) {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(color)
                
                Text(title)
                    .font(.petCaption)
                    .fontWeight(.medium)
                    .foregroundColor(.themePrimary)
                    .multilineTextAlignment(.center)
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(color.opacity(0.1))
                    .stroke(color.opacity(0.3), lineWidth: 1)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    // MARK: - Helper Methods
    
    private func loadVaccinations() {
        // Load all pets
        // In a real app, this would load from the database
        if let firstPet = petService.pets.first {
            selectedPet = firstPet
            loadVaccinationsForPet(firstPet)
        }
    }
    
    private func loadVaccinationsForPet(_ pet: Pet) {
        // Mock vaccination data
        vaccinations = [
            VaccinationRecord(
                id: UUID(),
                petId: pet.id,
                vaccineName: "Rabies",
                dueDate: Calendar.current.date(byAdding: .month, value: -2, to: Date()) ?? Date(),
                isCompleted: true,
                completedDate: Calendar.current.date(byAdding: .month, value: -2, to: Date()),
                veterinarian: "Dr. Smith"
            ),
            VaccinationRecord(
                id: UUID(),
                petId: pet.id,
                vaccineName: "DHPP",
                dueDate: Calendar.current.date(byAdding: .month, value: -1, to: Date()) ?? Date(),
                isCompleted: true,
                completedDate: Calendar.current.date(byAdding: .month, value: -1, to: Date()),
                veterinarian: "Dr. Johnson"
            )
        ]
        
        upcomingVaccinations = [
            VaccinationRecord(
                id: UUID(),
                petId: pet.id,
                vaccineName: "Bordetella",
                dueDate: Calendar.current.date(byAdding: .day, value: 15, to: Date()) ?? Date(),
                isCompleted: false
            ),
            VaccinationRecord(
                id: UUID(),
                petId: pet.id,
                vaccineName: "Flea & Tick Prevention",
                dueDate: Calendar.current.date(byAdding: .day, value: 30, to: Date()) ?? Date(),
                isCompleted: false
            )
        ]
    }
}

// MARK: - VaccinationRecord Model

struct VaccinationRecord: Identifiable, Codable {
    let id: UUID
    let petId: UUID
    let vaccineName: String
    let dueDate: Date
    let isCompleted: Bool
    let completedDate: Date?
    let veterinarian: String?
    let notes: String?
    
    init(id: UUID = UUID(), petId: UUID, vaccineName: String, dueDate: Date, isCompleted: Bool = false, completedDate: Date? = nil, veterinarian: String? = nil, notes: String? = nil) {
        self.id = id
        self.petId = petId
        self.vaccineName = vaccineName
        self.dueDate = dueDate
        self.isCompleted = isCompleted
        self.completedDate = completedDate
        self.veterinarian = veterinarian
        self.notes = notes
    }
    
    var urgencyColor: Color {
        let daysUntil = Calendar.current.dateComponents([.day], from: Date(), to: dueDate).day ?? 0
        
        if daysUntil < 0 {
            return .red // Overdue
        } else if daysUntil <= 7 {
            return .orange // Due soon
        } else if daysUntil <= 30 {
            return .yellow // Due this month
        } else {
            return .green // Future
        }
    }
    
    var daysUntilDue: String {
        let daysUntil = Calendar.current.dateComponents([.day], from: Date(), to: dueDate).day ?? 0
        
        if daysUntil < 0 {
            return "\(abs(daysUntil))d overdue"
        } else if daysUntil == 0 {
            return "Due today"
        } else {
            return "\(daysUntil)d left"
        }
    }
}

// MARK: - AddVaccinationView

struct AddVaccinationView: View {
    @Environment(\.dismiss) private var dismiss
    let pet: Pet?
    
    @State private var vaccineName = ""
    @State private var dueDate = Date()
    @State private var veterinarian = ""
    @State private var notes = ""
    
    var body: some View {
        NavigationView {
            Form {
                Section("Vaccination Details") {
                    TextField("Vaccination Name", text: $vaccineName)
                    DatePicker("Due Date", selection: $dueDate, displayedComponents: .date)
                    TextField("Veterinarian (Optional)", text: $veterinarian)
                }
                
                Section("Notes") {
                    TextField("Additional notes...", text: $notes, axis: .vertical)
                        .lineLimit(3...6)
                }
            }
            .navigationTitle("Add Vaccination")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Save") {
                        // Save vaccination record
                        dismiss()
                    }
                    .disabled(vaccineName.isEmpty)
                }
            }
        }
    }
}

#Preview {
    VaccinationPlannerView()
        .environmentObject(RealDataService())
}
