//
//  PetPlannerView.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 6/6/25.
//

import SwiftUI

struct PetPlannerView: View {
    @StateObject private var plannerService = PetPlannerService.shared
    @State private var selectedTab: PlannerTab = .walkPlanner
    @State private var animateCards = false
    
    enum PlannerTab: String, CaseIterable {
        case walkPlanner = "Smart Walk Planner"
        case locations = "Pet-Friendly Places"
        
        var icon: String {
            switch self {
            case .walkPlanner: return "figure.walk"
            case .locations: return "location.fill"
            }
        }
        
        var color: Color {
            switch self {
            case .walkPlanner: return .blue
            case .locations: return .green
            }
        }
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Custom Tab Bar
                customTabBar
                
                // Content Area
                TabView(selection: $selectedTab) {
                    SmartWalkPlannerView()
                        .environmentObject(plannerService)
                        .tag(PlannerTab.walkPlanner)
                    
                    LocationSuggestionsView()
                        .environmentObject(plannerService)
                        .tag(PlannerTab.locations)
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
                .animation(.easeInOut(duration: 0.3), value: selectedTab)
            }
            .background(EuropeanDesign.Gradients.elegantBackground)
            .navigationTitle("Pet Planner")
            .navigationBarTitleDisplayMode(.large)
        }
        .onAppear {
            withAnimation(.spring(response: 0.8, dampingFraction: 0.8)) {
                animateCards = true
            }
            plannerService.refreshAllData()
        }
        .refreshable {
            plannerService.refreshAllData()
        }
    }
    
    private var customTabBar: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                ForEach(PlannerTab.allCases, id: \.self) { tab in
                    tabButton(for: tab)
                }
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 16)
        }
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(.ultraThinMaterial)
                .shadow(color: .black.opacity(0.1), radius: 10, x: 0, y: 5)
        )
        .padding(.horizontal, 16)
        .padding(.top, 8)
    }
    
    private func tabButton(for tab: PlannerTab) -> some View {
        Button(action: {
            withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                selectedTab = tab
            }
        }) {
            HStack(spacing: 8) {
                Image(systemName: tab.icon)
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(selectedTab == tab ? .white : tab.color)
                
                if selectedTab == tab {
                    Text(tab.rawValue)
                        .font(.system(size: 14, weight: .semibold))
                        .foregroundColor(.white)
                        .transition(.opacity.combined(with: .scale))
                }
            }
            .padding(.horizontal, selectedTab == tab ? 16 : 12)
            .padding(.vertical, 10)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(selectedTab == tab ? tab.color : tab.color.opacity(0.1))
                    .shadow(
                        color: selectedTab == tab ? tab.color.opacity(0.3) : .clear,
                        radius: 8,
                        x: 0,
                        y: 4
                    )
            )
        }
        .scaleEffect(animateCards ? 1.0 : 0.8)
        .opacity(animateCards ? 1.0 : 0.0)
        .animation(
            .spring(response: 0.6, dampingFraction: 0.8)
            .delay(Double(PlannerTab.allCases.firstIndex(of: tab) ?? 0) * 0.1),
            value: animateCards
        )
    }
}

// MARK: - Smart Walk Planner View
struct SmartWalkPlannerView: View {
    @EnvironmentObject var plannerService: PetPlannerService
    
    var body: some View {
        ScrollView {
            LazyVStack(spacing: 20) {
                // Current Conditions Card
                currentConditionsCard
                
                // Walk Recommendation Card
                walkRecommendationCard
                
                // Environmental Alerts Section
                environmentalAlertsSection
                
                // Pollen Map Section
                pollenMapSection
                
                // Hourly Forecast
                hourlyForecastCard
                
                // Weekly Outlook
                weeklyOutlookCard
            }
            .padding(.horizontal, 16)
            .padding(.top, 20)
        }
    }

    private var currentConditionsCard: some View {
        EuropeanCard {
            VStack(alignment: .leading, spacing: 16) {
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Current Conditions")
                            .font(EuropeanDesign.Typography.headline)
                            .foregroundColor(EuropeanDesign.Colors.textPrimary)

                        Text("Perfect for walking!")
                            .font(EuropeanDesign.Typography.caption)
                            .foregroundColor(EuropeanDesign.Colors.textSecondary)
                    }

                    Spacer()

                    Image(systemName: "sun.max.fill")
                        .font(.system(size: 32))
                        .foregroundColor(.orange)
                }

                HStack(spacing: 20) {
                    conditionItem(
                        icon: "thermometer",
                        title: "Temperature",
                        value: "\(plannerService.currentWeather.temperature)°F",
                        color: .orange
                    )

                    conditionItem(
                        icon: "wind",
                        title: "Air Quality",
                        value: plannerService.airQuality.description,
                        color: plannerService.airQuality.color
                    )

                    conditionItem(
                        icon: "humidity.fill",
                        title: "Humidity",
                        value: "\(plannerService.currentWeather.humidity)%",
                        color: .blue
                    )
                }
            }
            .padding(20)
        }
    }

    private var walkRecommendationCard: some View {
        EuropeanCard {
            VStack(alignment: .leading, spacing: 16) {
                HStack {
                    Image(systemName: "figure.walk")
                        .font(.system(size: 24, weight: .semibold))
                        .foregroundColor(.green)

                    VStack(alignment: .leading, spacing: 4) {
                        Text("Walk Recommendation")
                            .font(EuropeanDesign.Typography.headline)
                            .foregroundColor(EuropeanDesign.Colors.textPrimary)

                        Text(plannerService.walkRecommendation.timeSlot)
                            .font(EuropeanDesign.Typography.caption)
                            .foregroundColor(EuropeanDesign.Colors.textSecondary)
                    }

                    Spacer()

                    VStack {
                        Text("\(plannerService.walkRecommendation.score)")
                            .font(.system(size: 24, weight: .bold))
                            .foregroundColor(.green)

                        Text("Score")
                            .font(.caption2)
                            .foregroundColor(EuropeanDesign.Colors.textSecondary)
                    }
                }

                Text(plannerService.walkRecommendation.reason)
                    .font(EuropeanDesign.Typography.body)
                    .foregroundColor(EuropeanDesign.Colors.textSecondary)
                    .multilineTextAlignment(.leading)

                EuropeanButton("Plan Walk", icon: "figure.walk") {
                    // Plan walk action
                }
            }
            .padding(20)
        }
    }

    private var environmentalAlertsSection: some View {
        EuropeanCard {
            VStack(alignment: .leading, spacing: 16) {
                HStack {
                    Image(systemName: "exclamationmark.triangle.fill")
                        .font(.system(size: 20))
                        .foregroundColor(.orange)

                    Text("Environmental Alerts")
                        .font(EuropeanDesign.Typography.headline)
                        .foregroundColor(EuropeanDesign.Colors.textPrimary)

                    Spacer()

                    if plannerService.environmentalAlerts.isEmpty {
                        HStack(spacing: 4) {
                            Circle()
                                .fill(.green)
                                .frame(width: 8, height: 8)
                            Text("All Clear")
                                .font(.caption)
                                .foregroundColor(.green)
                        }
                    }
                }

                if plannerService.environmentalAlerts.isEmpty {
                    HStack {
                        Image(systemName: "checkmark.circle.fill")
                            .foregroundColor(.green)
                        Text("No environmental concerns for your pet today!")
                            .font(EuropeanDesign.Typography.body)
                            .foregroundColor(EuropeanDesign.Colors.textSecondary)
                    }
                    .padding(.vertical, 8)
                } else {
                    VStack(spacing: 12) {
                        ForEach(plannerService.environmentalAlerts, id: \.id) { alert in
                            alertCard(alert)
                        }
                    }
                }
            }
            .padding(20)
        }
    }

    private var pollenMapSection: some View {
        EuropeanCard {
            VStack(alignment: .leading, spacing: 16) {
                HStack {
                    Image(systemName: "leaf.fill")
                        .font(.system(size: 20))
                        .foregroundColor(.green)

                    Text("Pollen Map")
                        .font(EuropeanDesign.Typography.headline)
                        .foregroundColor(EuropeanDesign.Colors.textPrimary)

                    Spacer()

                    Button("View Full Map") {
                        // Open full pollen map
                    }
                    .font(.caption)
                    .foregroundColor(.blue)
                }

                // Pollen Map Placeholder
                RoundedRectangle(cornerRadius: 12)
                    .fill(EuropeanDesign.Colors.surfaceSecondary)
                    .frame(height: 200)
                    .overlay(
                        VStack(spacing: 12) {
                            Image(systemName: "map.fill")
                                .font(.system(size: 40))
                                .foregroundColor(EuropeanDesign.Colors.textSecondary)

                            Text("Interactive Pollen Map")
                                .font(EuropeanDesign.Typography.subheadline)
                                .foregroundColor(EuropeanDesign.Colors.textPrimary)

                            Text("Real-time pollen levels in your area")
                                .font(.caption)
                                .foregroundColor(EuropeanDesign.Colors.textSecondary)
                                .multilineTextAlignment(.center)
                        }
                    )

                // Pollen Level Indicators
                HStack(spacing: 16) {
                    pollenIndicator(type: "Tree", level: "Low", color: .green)
                    pollenIndicator(type: "Grass", level: "Moderate", color: .yellow)
                    pollenIndicator(type: "Weed", level: "High", color: .orange)
                }
            }
            .padding(20)
        }
    }

    private var hourlyForecastCard: some View {
        EuropeanCard {
            VStack(alignment: .leading, spacing: 16) {
                Text("Hourly Forecast")
                    .font(EuropeanDesign.Typography.headline)
                    .foregroundColor(EuropeanDesign.Colors.textPrimary)

                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 16) {
                        ForEach(plannerService.hourlyForecast, id: \.hour) { forecast in
                            hourlyForecastItem(forecast)
                        }
                    }
                    .padding(.horizontal, 4)
                }
            }
            .padding(20)
        }
    }

    private var weeklyOutlookCard: some View {
        EuropeanCard {
            VStack(alignment: .leading, spacing: 16) {
                Text("Weekly Outlook")
                    .font(EuropeanDesign.Typography.headline)
                    .foregroundColor(EuropeanDesign.Colors.textPrimary)

                VStack(spacing: 12) {
                    ForEach(plannerService.weeklyForecast, id: \.day) { forecast in
                        weeklyForecastItem(forecast)
                    }
                }
            }
            .padding(20)
        }
    }

    // MARK: - Helper Functions

    private func conditionItem(icon: String, title: String, value: String, color: Color) -> some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.system(size: 20))
                .foregroundColor(color)

            Text(value)
                .font(.system(size: 14, weight: .semibold))
                .foregroundColor(EuropeanDesign.Colors.textPrimary)

            Text(title)
                .font(.caption2)
                .foregroundColor(EuropeanDesign.Colors.textSecondary)
        }
        .frame(maxWidth: .infinity)
    }

    private func alertCard(_ alert: EnvironmentalAlert) -> some View {
        HStack(spacing: 12) {
            Image(systemName: alert.icon)
                .font(.system(size: 16))
                .foregroundColor(alert.severity.color)
                .frame(width: 24)

            VStack(alignment: .leading, spacing: 4) {
                Text(alert.title)
                    .font(.system(size: 14, weight: .semibold))
                    .foregroundColor(EuropeanDesign.Colors.textPrimary)

                Text(alert.message)
                    .font(.caption)
                    .foregroundColor(EuropeanDesign.Colors.textSecondary)
                    .multilineTextAlignment(.leading)
            }

            Spacer()

            Text(alert.timeAgo)
                .font(.caption2)
                .foregroundColor(EuropeanDesign.Colors.textSecondary)
        }
        .padding(12)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(alert.severity.color.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(alert.severity.color.opacity(0.3), lineWidth: 1)
                )
        )
    }

    private func pollenIndicator(type: String, level: String, color: Color) -> some View {
        VStack(spacing: 4) {
            Circle()
                .fill(color)
                .frame(width: 12, height: 12)

            Text(type)
                .font(.caption2)
                .foregroundColor(EuropeanDesign.Colors.textPrimary)

            Text(level)
                .font(.caption2)
                .foregroundColor(color)
        }
        .frame(maxWidth: .infinity)
    }

    private func hourlyForecastItem(_ forecast: HourlyForecast) -> some View {
        VStack(spacing: 8) {
            Text(forecast.hour)
                .font(.caption)
                .foregroundColor(EuropeanDesign.Colors.textSecondary)

            Image(systemName: forecast.icon)
                .font(.system(size: 20))
                .foregroundColor(forecast.color)

            Text("\(forecast.temperature)°")
                .font(.system(size: 14, weight: .semibold))
                .foregroundColor(EuropeanDesign.Colors.textPrimary)

            Circle()
                .fill(forecast.walkQuality.color)
                .frame(width: 8, height: 8)
        }
        .padding(.vertical, 8)
        .frame(width: 60)
    }

    private func weeklyForecastItem(_ forecast: WeeklyForecast) -> some View {
        HStack {
            Text(forecast.day)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(EuropeanDesign.Colors.textPrimary)
                .frame(width: 60, alignment: .leading)

            Image(systemName: forecast.icon)
                .font(.system(size: 16))
                .foregroundColor(forecast.color)
                .frame(width: 30)

            Spacer()

            HStack(spacing: 4) {
                Circle()
                    .fill(forecast.walkQuality.color)
                    .frame(width: 8, height: 8)

                Text(forecast.walkQuality.description)
                    .font(.caption)
                    .foregroundColor(EuropeanDesign.Colors.textSecondary)
            }

            Text("\(forecast.highTemp)°/\(forecast.lowTemp)°")
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(EuropeanDesign.Colors.textPrimary)
                .frame(width: 60, alignment: .trailing)
        }
        .padding(.vertical, 4)
    }
}

// MARK: - Location Suggestions View
struct LocationSuggestionsView: View {
    @EnvironmentObject var plannerService: PetPlannerService
    @State private var searchText = ""

    var body: some View {
        ScrollView {
            LazyVStack(spacing: 20) {
                // Search Bar
                searchBar

                // Featured Locations
                featuredLocationsSection

                // Nearby Locations
                nearbyLocationsSection
            }
            .padding(.horizontal, 16)
            .padding(.top, 20)
        }
    }

    private var searchBar: some View {
        HStack {
            Image(systemName: "magnifyingglass")
                .foregroundColor(EuropeanDesign.Colors.textSecondary)

            TextField("Search pet-friendly places...", text: $searchText)
                .font(EuropeanDesign.Typography.body)
        }
        .padding(12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(EuropeanDesign.Colors.surfaceSecondary)
        )
    }

    private var featuredLocationsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Featured Locations")
                .font(EuropeanDesign.Typography.headline)
                .foregroundColor(EuropeanDesign.Colors.textPrimary)

            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 16) {
                    ForEach(plannerService.nearbyLocations.prefix(3), id: \.id) { location in
                        featuredLocationCard(location)
                    }
                }
                .padding(.horizontal, 4)
            }
        }
    }

    private var nearbyLocationsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Nearby Locations")
                .font(EuropeanDesign.Typography.headline)
                .foregroundColor(EuropeanDesign.Colors.textPrimary)

            LazyVStack(spacing: 12) {
                ForEach(plannerService.nearbyLocations, id: \.id) { location in
                    locationCard(location)
                }
            }
        }
    }

    private func featuredLocationCard(_ location: PetFriendlyLocation) -> some View {
        EuropeanCard {
            VStack(alignment: .leading, spacing: 12) {
                Rectangle()
                    .fill(EuropeanDesign.Colors.surfaceSecondary)
                    .frame(width: 200, height: 120)
                    .overlay(
                        Image(systemName: "photo")
                            .foregroundColor(EuropeanDesign.Colors.textSecondary)
                    )
                    .cornerRadius(12)

                VStack(alignment: .leading, spacing: 4) {
                    Text(location.name)
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(EuropeanDesign.Colors.textPrimary)
                        .lineLimit(1)

                    HStack {
                        ForEach(0..<5) { index in
                            Image(systemName: index < Int(location.rating) ? "star.fill" : "star")
                                .foregroundColor(.yellow)
                                .font(.caption)
                        }

                        Text("\(location.distance, specifier: "%.1f") mi")
                            .font(.caption)
                            .foregroundColor(EuropeanDesign.Colors.textSecondary)
                    }
                }
                .padding(.horizontal, 12)
                .padding(.bottom, 12)
            }
        }
        .frame(width: 200)
    }

    private func locationCard(_ location: PetFriendlyLocation) -> some View {
        EuropeanCard {
            HStack(spacing: 16) {
                Rectangle()
                    .fill(EuropeanDesign.Colors.surfaceSecondary)
                    .frame(width: 80, height: 80)
                    .overlay(
                        Image(systemName: "photo")
                            .foregroundColor(EuropeanDesign.Colors.textSecondary)
                    )
                    .cornerRadius(12)

                VStack(alignment: .leading, spacing: 8) {
                    Text(location.name)
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(EuropeanDesign.Colors.textPrimary)
                        .lineLimit(2)

                    Text(location.address)
                        .font(.caption)
                        .foregroundColor(EuropeanDesign.Colors.textSecondary)
                        .lineLimit(1)

                    HStack {
                        ForEach(0..<5) { index in
                            Image(systemName: index < Int(location.rating) ? "star.fill" : "star")
                                .foregroundColor(.yellow)
                                .font(.caption2)
                        }

                        Text("\(location.distance, specifier: "%.1f") mi")
                            .font(.caption)
                            .foregroundColor(EuropeanDesign.Colors.textSecondary)

                        Spacer()

                        if location.isOpen {
                            Text("Open")
                                .font(.caption)
                                .foregroundColor(.green)
                        } else {
                            Text("Closed")
                                .font(.caption)
                                .foregroundColor(.red)
                        }
                    }
                }

                Spacer()
            }
            .padding(16)
        }
    }
}

// MARK: - Preview
#Preview {
    PetPlannerView()
        .environmentObject(PetPlannerService.shared)
}
