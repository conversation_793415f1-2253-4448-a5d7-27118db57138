//
//  MemoryDetailView.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 6/6/25.
//

import SwiftUI

struct MemoryDetailView: View {
    let memory: Memory
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject var realDataService: RealDataService
    @State private var showingShareSheet = false
    @State private var showingEditView = false
    @State private var isLiked = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    // Media Section
                    mediaSection
                    
                    // Content Section
                    contentSection
                    
                    // Tags Section
                    if !memory.tags.isEmpty {
                        tagsSection
                    }
                    
                    // Metadata Section
                    metadataSection
                    
                    // Actions Section
                    actionsSection
                }
                .padding(.horizontal, 16)
                .padding(.bottom, 100)
            }
            .navigationTitle(memory.title)
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Close") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Menu {
                        Button(action: { showingEditView = true }) {
                            Label("Edit Memory", systemImage: "pencil")
                        }
                        
                        Button(action: { showingShareSheet = true }) {
                            Label("Share", systemImage: "square.and.arrow.up")
                        }
                        
                        Button(action: { toggleFavorite() }) {
                            Label(memory.isFavorite ? "Remove from Favorites" : "Add to Favorites", 
                                  systemImage: memory.isFavorite ? "heart.fill" : "heart")
                        }
                        
                        Divider()
                        
                        Button(role: .destructive, action: { deleteMemory() }) {
                            Label("Delete Memory", systemImage: "trash")
                        }
                    } label: {
                        Image(systemName: "ellipsis.circle")
                    }
                }
            }
        }
        .onAppear {
            isLiked = memory.isFavorite
        }
        .sheet(isPresented: $showingEditView) {
            EditMemoryView(memory: memory)
                .environmentObject(realDataService)
        }
        .sheet(isPresented: $showingShareSheet) {
            // TODO: Add share sheet
            Text("Share Sheet")
        }
    }
    
    private var mediaSection: some View {
        Group {
            if let mediaURL = memory.mediaURL {
                switch memory.type {
                case .photo:
                    AsyncImage(url: URL(string: mediaURL)) { image in
                        image
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                    } placeholder: {
                        Rectangle()
                            .fill(Color.gray.opacity(0.3))
                            .frame(height: 300)
                            .overlay(
                                ProgressView()
                            )
                    }
                    .frame(maxHeight: 400)
                    .cornerRadius(12)
                    
                case .video:
                    Rectangle()
                        .fill(Color.black)
                        .frame(height: 300)
                        .overlay(
                            VStack {
                                Image(systemName: "play.circle.fill")
                                    .font(.system(size: 60))
                                    .foregroundColor(.white)
                                
                                Text("Video")
                                    .foregroundColor(.white)
                                    .font(.caption)
                            }
                        )
                        .cornerRadius(12)
                        .onTapGesture {
                            // TODO: Play video
                        }
                    
                case .audio:
                    Rectangle()
                        .fill(Color.blue.opacity(0.1))
                        .frame(height: 150)
                        .overlay(
                            VStack {
                                Image(systemName: "waveform.circle.fill")
                                    .font(.system(size: 40))
                                    .foregroundColor(.blue)
                                
                                Text("Audio Recording")
                                    .foregroundColor(.blue)
                                    .font(.caption)
                                
                                if let duration = memory.duration {
                                    Text(formatDuration(duration))
                                        .foregroundColor(.secondary)
                                        .font(.caption2)
                                }
                            }
                        )
                        .cornerRadius(12)
                        .onTapGesture {
                            // TODO: Play audio
                        }
                    
                default:
                    EmptyView()
                }
            } else {
                // Default placeholder for memories without media
                Rectangle()
                    .fill(Color.gray.opacity(0.1))
                    .frame(height: 200)
                    .overlay(
                        VStack {
                            Image(systemName: memory.type.systemImage)
                                .font(.system(size: 40))
                                .foregroundColor(.gray)
                            
                            Text(memory.type.displayName)
                                .foregroundColor(.gray)
                                .font(.caption)
                        }
                    )
                    .cornerRadius(12)
            }
        }
    }
    
    private var contentSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            if !memory.content.isEmpty {
                Text("Description")
                    .font(.headline)
                    .foregroundColor(.primary)
                
                Text(memory.content)
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.leading)
            }
            
            if let milestone = memory.milestone {
                VStack(alignment: .leading, spacing: 8) {
                    Text("Milestone")
                        .font(.headline)
                        .foregroundColor(.primary)
                    
                    HStack {
                        Image(systemName: "star.fill")
                            .foregroundColor(.orange)
                        
                        Text(milestone)
                            .font(.subheadline)
                            .foregroundColor(.orange)
                    }
                    .padding(.horizontal, 12)
                    .padding(.vertical, 8)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(Color.orange.opacity(0.1))
                    )
                }
            }
            
            if let sentiment = memory.sentiment {
                VStack(alignment: .leading, spacing: 8) {
                    Text("AI Sentiment")
                        .font(.headline)
                        .foregroundColor(.primary)
                    
                    Text(sentiment.capitalized)
                        .font(.subheadline)
                        .foregroundColor(.purple)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 8)
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .fill(Color.purple.opacity(0.1))
                        )
                }
            }
        }
    }
    
    private var tagsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Tags")
                .font(.headline)
                .foregroundColor(.primary)
            
            LazyVGrid(columns: [
                GridItem(.adaptive(minimum: 80))
            ], spacing: 8) {
                ForEach(memory.tags, id: \.self) { tag in
                    Text(tag)
                        .font(.caption)
                        .foregroundColor(.blue)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(
                            RoundedRectangle(cornerRadius: 6)
                                .fill(Color.blue.opacity(0.1))
                        )
                }
            }
        }
    }
    
    private var metadataSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Details")
                .font(.headline)
                .foregroundColor(.primary)
            
            VStack(spacing: 8) {
                metadataRow(title: "Created", value: memory.createdAt.formatted(date: .abbreviated, time: .shortened))
                metadataRow(title: "Type", value: memory.type.displayName)
                metadataRow(title: "Visibility", value: memory.isPublic ? "Public" : "Private")
                
                if let pet = memory.pet {
                    metadataRow(title: "Pet", value: pet.name)
                }
            }
        }
    }
    
    private var actionsSection: some View {
        VStack(spacing: 16) {
            HStack(spacing: 20) {
                Button(action: { toggleFavorite() }) {
                    VStack {
                        Image(systemName: memory.isFavorite ? "heart.fill" : "heart")
                            .font(.title2)
                            .foregroundColor(memory.isFavorite ? .red : .gray)
                        
                        Text(memory.isFavorite ? "Favorited" : "Favorite")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                
                Button(action: { showingShareSheet = true }) {
                    VStack {
                        Image(systemName: "square.and.arrow.up")
                            .font(.title2)
                            .foregroundColor(.blue)
                        
                        Text("Share")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                
                Button(action: { showingEditView = true }) {
                    VStack {
                        Image(systemName: "pencil")
                            .font(.title2)
                            .foregroundColor(.green)
                        
                        Text("Edit")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                
                Spacer()
            }
            .padding(.horizontal)
        }
    }
    
    private func metadataRow(title: String, value: String) -> some View {
        HStack {
            Text(title)
                .font(.subheadline)
                .foregroundColor(.secondary)
            
            Spacer()
            
            Text(value)
                .font(.subheadline)
                .foregroundColor(.primary)
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(Color.gray.opacity(0.1))
        )
    }
    
    private func formatDuration(_ duration: TimeInterval) -> String {
        let minutes = Int(duration) / 60
        let seconds = Int(duration) % 60
        return String(format: "%d:%02d", minutes, seconds)
    }
    
    private func toggleFavorite() {
        // TODO: Update memory favorite status in database
        withAnimation {
            // memory.isFavorite.toggle() // This would need to be done through the service
        }
    }
    
    private func deleteMemory() {
        // TODO: Delete memory from database
        dismiss()
    }
}

// MARK: - Preview
#Preview {
    MemoryDetailView(memory: Memory(
        title: "First Day Home",
        content: "Buddy's first day in our home was magical. He was a bit nervous at first but quickly warmed up to his new surroundings.",
        type: .photo,
        milestone: "First Day Home",
        sentiment: "joyful",
        tags: ["home", "first day", "nervous", "happy"],
        isFavorite: true
    ))
    .environmentObject(RealDataService())
}
