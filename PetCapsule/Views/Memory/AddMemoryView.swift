//
//  AddMemoryView.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI
import PhotosUI

struct AddMemoryView: View {

    @EnvironmentObject var realDataService: RealDataService
    @Environment(\.dismiss) private var dismiss

    @State private var selectedPet: Pet?
    @State private var memoryTitle = ""
    @State private var memoryDescription = ""
    @State private var memoryType: AddMemoryView.MemoryType = .photo
    @State private var selectedPhotos: [PhotosPickerItem] = []
    @State private var memoryImages: [UIImage] = []
    @State private var selectedVideo: PhotosPickerItem?
    @State private var memoryVideoURL: URL?
    @State private var isPublic = false
    @State private var isFavorite = false
    @State private var isSaving = false
    @State private var showError = false
    @State private var errorMessage = ""

    enum MemoryType: String, CaseIterable {
        case photo = "Photo"
        case video = "Video"
        case milestone = "Milestone"
        case text = "Text Note"
    }

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // Pet Selection
                    petSelectionSection

                    // Memory Type
                    memoryTypeSection

                    // Photo/Video Selection
                    if memoryType == .photo || memoryType == .video {
                        mediaSelectionSection
                    }

                    // Memory Details
                    memoryDetailsSection

                    // Options
                    memoryOptionsSection
                }
                .padding()
            }
            .navigationTitle("Add Memory")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: saveMemory) {
                        if isSaving {
                            ProgressView()
                                .scaleEffect(0.8)
                        } else {
                            Text("Save")
                                .fontWeight(.semibold)
                        }
                    }
                    .disabled(!isFormValid || isSaving)
                }
            }
        }
        .alert("Error", isPresented: $showError) {
            Button("OK") { }
        } message: {
            Text(errorMessage)
        }
    }

    private var petSelectionSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Select Pet")
                .font(.petSubheadline)
                .fontWeight(.semibold)

            if realDataService.pets.isEmpty {
                Text("No pets available. Add a pet first.")
                    .font(.petSubheadline)
                    .foregroundColor(.secondary)
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color(.systemGray6))
                    )
            } else {
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 12) {
                        ForEach(realDataService.pets, id: \.id) { pet in
                            petSelectionCard(pet: pet)
                        }
                    }
                    .padding(.horizontal)
                }
            }
        }
    }

    private func petSelectionCard(pet: Pet) -> some View {
        VStack(spacing: 8) {
            AsyncImage(url: URL(string: pet.profileImageURL ?? "")) { image in
                image
                    .resizable()
                    .aspectRatio(contentMode: .fill)
            } placeholder: {
                ZStack {
                    Circle()
                        .fill(Color.purple.opacity(0.2))

                    Text(pet.species == "dog" ? "🐕" : pet.species == "cat" ? "🐱" : "🐾")
                        .font(.title2)
                }
            }
            .frame(width: 60, height: 60)
            .clipShape(Circle())
            .overlay(
                Circle()
                    .stroke(selectedPet?.id == pet.id ? Color.purple : Color.clear, lineWidth: 3)
            )

            Text(pet.name)
                .font(.petCaption)
                .fontWeight(.semibold)
                .foregroundColor(selectedPet?.id == pet.id ? .purple : .primary)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(selectedPet?.id == pet.id ? Color.purple.opacity(0.1) : Color(.systemGray6))
        )
        .onTapGesture {
            selectedPet = pet
        }
    }

    private var memoryTypeSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Memory Type")
                .font(.petSubheadline)
                .fontWeight(.semibold)

            Picker("Memory Type", selection: $memoryType) {
                ForEach(MemoryType.allCases, id: \.self) { type in
                    Text(type.rawValue).tag(type)
                }
            }
            .pickerStyle(SegmentedPickerStyle())
        }
    }

    private var mediaSelectionSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text(memoryType == .photo ? "Select Photos" : "Select Video")
                .font(.petSubheadline)
                .fontWeight(.semibold)

            if memoryType == .photo {
                // Multiple Photos Support
                if !memoryImages.isEmpty {
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: 12) {
                            ForEach(Array(memoryImages.enumerated()), id: \.offset) { index, image in
                                ZStack(alignment: .topTrailing) {
                                    Image(uiImage: image)
                                        .resizable()
                                        .aspectRatio(contentMode: .fill)
                                        .frame(width: 120, height: 120)
                                        .clipShape(RoundedRectangle(cornerRadius: 12))
                                        .shadow(radius: 3)

                                    Button(action: {
                                        memoryImages.remove(at: index)
                                        selectedPhotos.remove(at: index)
                                    }) {
                                        Image(systemName: "xmark.circle.fill")
                                            .foregroundColor(.red)
                                            .background(Color.white, in: Circle())
                                    }
                                    .offset(x: 8, y: -8)
                                }
                            }
                        }
                        .padding(.horizontal)
                    }
                } else {
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color(.systemGray6))
                        .frame(height: 200)
                        .overlay(
                            VStack {
                                Image(systemName: "photo.fill.on.rectangle.fill")
                                    .font(.system(size: 40))
                                    .foregroundColor(.purple)

                                Text("Add Photos")
                                    .font(.petSubheadline)
                                    .foregroundColor(.purple)

                                Text("Select multiple photos")
                                    .font(.petCaption)
                                    .foregroundColor(.secondary)
                            }
                        )
                }

                PhotosPicker(selection: $selectedPhotos, maxSelectionCount: 10, matching: .images) {
                    Text(memoryImages.isEmpty ? "Choose Photos" : "Add More Photos")
                        .font(.petSubheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.white)
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color.purple)
                        )
                }
                .onChange(of: selectedPhotos) { _, newValue in
                    Task {
                        memoryImages.removeAll()
                        for item in newValue {
                            if let data = try? await item.loadTransferable(type: Data.self),
                               let image = UIImage(data: data) {
                                await MainActor.run {
                                    memoryImages.append(image)
                                }
                            }
                        }
                    }
                }
            } else {
                // Video Support
                if let videoURL = memoryVideoURL {
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color(.systemGray6))
                        .frame(height: 200)
                        .overlay(
                            VStack {
                                Image(systemName: "checkmark.circle.fill")
                                    .font(.system(size: 40))
                                    .foregroundColor(.green)

                                Text("Video Selected")
                                    .font(.petSubheadline)
                                    .foregroundColor(.primary)

                                Text(videoURL.lastPathComponent)
                                    .font(.petCaption)
                                    .foregroundColor(.secondary)
                                    .lineLimit(1)
                            }
                        )
                } else {
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color(.systemGray6))
                        .frame(height: 200)
                        .overlay(
                            VStack {
                                Image(systemName: "video.fill")
                                    .font(.system(size: 40))
                                    .foregroundColor(.purple)

                                Text("Add Video")
                                    .font(.petSubheadline)
                                    .foregroundColor(.purple)
                            }
                        )
                }

                PhotosPicker(selection: $selectedVideo, matching: .videos) {
                    Text(memoryVideoURL == nil ? "Choose Video" : "Change Video")
                        .font(.petSubheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.white)
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color.purple)
                        )
                }
                .onChange(of: selectedVideo) { _, newValue in
                    Task {
                        if let data = try? await newValue?.loadTransferable(type: Data.self) {
                            // Create temporary URL for video
                            let tempURL = FileManager.default.temporaryDirectory
                                .appendingPathComponent(UUID().uuidString)
                                .appendingPathExtension("mov")

                            try? data.write(to: tempURL)
                            await MainActor.run {
                                memoryVideoURL = tempURL
                            }
                        }
                    }
                }
            }
        }
    }

    private var memoryDetailsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Memory Details")
                .font(.petSubheadline)
                .fontWeight(.semibold)

            VStack(alignment: .leading, spacing: 8) {
                Text("Title")
                    .font(.petSubheadline)
                    .fontWeight(.medium)

                TextField("Enter memory title", text: $memoryTitle)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
            }

            VStack(alignment: .leading, spacing: 8) {
                Text("Description")
                    .font(.petSubheadline)
                    .fontWeight(.medium)

                TextField("Describe this memory...", text: $memoryDescription, axis: .vertical)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .lineLimit(3...6)
            }
        }
    }

    private var memoryOptionsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Options")
                .font(.petSubheadline)
                .fontWeight(.semibold)

            VStack(spacing: 12) {
                HStack {
                    Image(systemName: isPublic ? "globe" : "lock.fill")
                        .foregroundColor(isPublic ? .blue : .gray)

                    VStack(alignment: .leading, spacing: 2) {
                        Text("Make Public")
                            .font(.petSubheadline)
                            .fontWeight(.medium)

                        Text("Share with the pet community")
                            .font(.petCaption)
                            .foregroundColor(.secondary)
                    }

                    Spacer()

                    Toggle("", isOn: $isPublic)
                }

                HStack {
                    Image(systemName: isFavorite ? "heart.fill" : "heart")
                        .foregroundColor(isFavorite ? .red : .gray)

                    VStack(alignment: .leading, spacing: 2) {
                        Text("Mark as Favorite")
                            .font(.petSubheadline)
                            .fontWeight(.medium)

                        Text("Add to your favorites collection")
                            .font(.petCaption)
                            .foregroundColor(.secondary)
                    }

                    Spacer()

                    Toggle("", isOn: $isFavorite)
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemGray6))
            )
        }
    }

    private var isFormValid: Bool {
        guard selectedPet != nil && !memoryTitle.isEmpty else { return false }

        // Check media requirements based on type
        switch memoryType {
        case .photo:
            return !memoryImages.isEmpty
        case .video:
            return memoryVideoURL != nil
        case .milestone, .text:
            return true // No media required
        }
    }

    private func saveMemory() {
        guard let selectedPet = selectedPet else {
            errorMessage = "Please select a pet"
            showError = true
            return
        }

        guard !memoryTitle.isEmpty else {
            errorMessage = "Please enter a memory title"
            showError = true
            return
        }

        isSaving = true

        Task {
                // Convert AddMemoryView.MemoryType to global MemoryType
                let modelMemoryType: PetCapsule.MemoryType
                switch memoryType {
                case .photo:
                    modelMemoryType = .photo
                case .video:
                    modelMemoryType = .video
                case .milestone:
                    modelMemoryType = .milestone
                case .text:
                    modelMemoryType = .text
                }

                // Create new memory object
                let newMemory = Memory(
                    title: memoryTitle,
                    content: memoryDescription,
                    type: modelMemoryType,
                    mediaURL: nil, // Will be set after media upload
                    thumbnailURL: nil,
                    isPublic: isPublic,
                    isFavorite: isFavorite
                )

                // Get user ID
                guard let userId = realDataService.getCurrentUserId() else {
                    await MainActor.run {
                        if UserDefaults.standard.bool(forKey: "isDevelopmentMode") {
                            errorMessage = "Development mode detected. For image uploads, please sign out and sign in with a real account."
                        } else {
                            errorMessage = "No authenticated user found. Please log in again."
                        }
                        showError = true
                        isSaving = false
                    }
                    return
                }

                // Upload media if available
                var mediaURL: String? = nil
                var thumbnailURL: String? = nil

                if modelMemoryType == .photo && !memoryImages.isEmpty {
                    print("🔄 Uploading \(memoryImages.count) images for memory: \(memoryTitle)")
                    print("🔄 Memory ID: \(newMemory.id)")
                    print("🔄 User ID: \(userId)")

                    // Upload first image as main media
                    if let firstImage = memoryImages.first {
                        print("📸 First image size: \(firstImage.size)")

                        if let imageData = firstImage.optimizedForUpload() {
                            print("📤 Uploading main image - Data size: \(imageData.count) bytes")

                            mediaURL = await SupabaseStorageService.shared.uploadMemoryImage(
                                imageData,
                                memoryId: newMemory.id,
                                userId: userId
                            )

                            print("📤 Main image upload result: \(mediaURL ?? "nil")")

                            // Create thumbnail (smaller version)
                            if let thumbnailImage = firstImage.thumbnail(),
                               let thumbnailData = thumbnailImage.compressedData(quality: 0.6) {

                                print("📤 Uploading thumbnail - Data size: \(thumbnailData.count) bytes")
                                thumbnailURL = await SupabaseStorageService.shared.uploadMemoryThumbnail(
                                    thumbnailData,
                                    memoryId: newMemory.id,
                                    userId: userId
                                )

                                print("📤 Thumbnail upload result: \(thumbnailURL ?? "nil")")
                            } else {
                                // Use main image URL as thumbnail if resize fails
                                thumbnailURL = mediaURL
                                print("⚠️ Using main image as thumbnail")
                            }
                        } else {
                            print("❌ Failed to optimize image for upload")
                        }
                    } else {
                        print("❌ No first image found in memoryImages array")
                    }
                } else if modelMemoryType == .video && memoryVideoURL != nil {
                    // Upload video
                    if let videoURL = memoryVideoURL,
                       let videoData = try? Data(contentsOf: videoURL) {

                        print("📤 Uploading video...")
                        mediaURL = await SupabaseStorageService.shared.uploadMemoryVideo(
                            videoData,
                            memoryId: newMemory.id,
                            userId: userId
                        )

                        print("📤 Video uploaded: \(mediaURL ?? "nil")")
                    }
                }

                // CRITICAL: Set the URLs on the memory object before saving
                newMemory.mediaURL = mediaURL
                newMemory.thumbnailURL = thumbnailURL

                print("🔗 Memory URLs set - Media: \(mediaURL ?? "nil"), Thumbnail: \(thumbnailURL ?? "nil")")

                // Update memory with uploaded URLs
                newMemory.mediaURL = mediaURL
                newMemory.thumbnailURL = thumbnailURL

                print("🔗 Final URLs - Media: \(mediaURL ?? "nil"), Thumbnail: \(thumbnailURL ?? "nil")")

                print("🔄 Saving memory: \(memoryTitle) for pet: \(selectedPet.name)")
                print("📊 Memory details - Type: \(modelMemoryType), Public: \(isPublic), Favorite: \(isFavorite)")

                let success = await realDataService.createMemory(newMemory, petId: selectedPet.id, userId: userId)

                await MainActor.run {
                    isSaving = false
                    if success {
                        print("✅ Memory saved successfully!")

                        // Also update ProductionMemoryService to keep them in sync
                        if let savedMemory = realDataService.memories.first(where: { $0.title == memoryTitle }) {
                            ProductionMemoryService.shared.memories.insert(savedMemory, at: 0)
                            print("✅ Added memory to ProductionMemoryService: \(ProductionMemoryService.shared.memories.count) total")
                        }

                        dismiss()
                    } else {
                        errorMessage = "Failed to save memory. Please try again."
                        showError = true
                    }
                }


        }
    }
}



// MARK: - AI Analysis Results View

struct AIAnalysisResultsView: View {
    let results: PetHealthAnalysisResult
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    Text("AI Analysis Results for \(results.pet.name)")
                        .font(.petTitle2)
                        .fontWeight(.bold)
                        .padding()

                    Text("Comprehensive AI analysis results will be displayed here")
                        .font(.petSubheadline)
                        .foregroundColor(.secondary)
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color(.systemGray6))
                        )
                }
                .padding()
            }
            .navigationTitle("AI Analysis")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}
