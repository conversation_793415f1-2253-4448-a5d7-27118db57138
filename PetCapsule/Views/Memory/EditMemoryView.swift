//
//  EditMemoryView.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 6/6/25.
//

import SwiftUI
import PhotosUI

struct EditMemoryView: View {
    let memory: Memory
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject var realDataService: RealDataService
    @StateObject private var productionMemoryService = ProductionMemoryService.shared

    @State private var title: String
    @State private var content: String
    @State private var isPublic: Bool
    @State private var isFavorite: Bool
    @State private var tags: [String]
    @State private var newTag: String = ""

    // Image editing states
    @State private var selectedPhotos: [PhotosPickerItem] = []
    @State private var memoryImages: [UIImage] = []
    @State private var selectedVideo: PhotosPickerItem?
    @State private var memoryVideoURL: URL?
    @State private var hasMediaChanges = false
    @State private var showingPhotoPicker = false
    @State private var showingRemoveMediaAlert = false
    @State private var maxImages = 5 // Allow up to 5 images

    // UI states
    @State private var isSaving = false
    @State private var showError = false
    @State private var errorMessage = ""
    
    init(memory: Memory) {
        self.memory = memory
        self._title = State(initialValue: memory.title)
        self._content = State(initialValue: memory.content)
        self._isPublic = State(initialValue: memory.isPublic)
        self._isFavorite = State(initialValue: memory.isFavorite)
        self._tags = State(initialValue: memory.tags)
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // Media Preview
                    mediaPreviewSection
                    
                    // Form Content
                    formContent
                }
                .padding(.horizontal, 20)
                .padding(.bottom, 100)
            }
            .navigationTitle("Edit Memory")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Save") {
                        saveChanges()
                    }
                    .disabled(title.isEmpty || isSaving)
                    .fontWeight(.semibold)
                }
            }
        }
        .alert("Error", isPresented: $showError) {
            Button("OK") { }
        } message: {
            Text(errorMessage)
        }
        .overlay {
            if isSaving {
                savingOverlay
            }
        }
    }
    
    private var mediaPreviewSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("Media")
                    .font(.headline)
                    .foregroundColor(.primary)

                if !memoryImages.isEmpty {
                    Text("(\(memoryImages.count)/\(maxImages))")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                Spacer()

                // Media editing buttons
                HStack(spacing: 8) {
                    if memory.mediaURL != nil {
                        Button("Remove") {
                            showingRemoveMediaAlert = true
                        }
                        .font(.caption)
                        .foregroundColor(.red)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(
                            RoundedRectangle(cornerRadius: 6)
                                .fill(.ultraThinMaterial)
                        )
                    }

                    if memory.type == .photo {
                        PhotosPicker(
                            selection: $selectedPhotos,
                            maxSelectionCount: maxImages,
                            matching: .images
                        ) {
                            Text(memoryImages.isEmpty && memory.mediaURL == nil ? "Add Photos" : "Add More Photos")
                                .font(.caption)
                                .foregroundColor(.blue)
                                .padding(.horizontal, 8)
                                .padding(.vertical, 4)
                                .background(
                                    RoundedRectangle(cornerRadius: 6)
                                        .fill(.ultraThinMaterial)
                                )
                        }
                        .disabled(memoryImages.count >= maxImages)
                    }
                }
            }

            ZStack {
                // Show new images if selected
                if !memoryImages.isEmpty {
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: 8) {
                            ForEach(Array(memoryImages.enumerated()), id: \.offset) { index, image in
                                ZStack {
                                    Image(uiImage: image)
                                        .resizable()
                                        .aspectRatio(contentMode: .fill)
                                        .frame(width: memoryImages.count == 1 ? nil : 150, height: 180)
                                        .clipShape(RoundedRectangle(cornerRadius: 8))

                                    // Remove button for individual images
                                    if memoryImages.count > 1 {
                                        VStack {
                                            HStack {
                                                Spacer()
                                                Button(action: { removeImage(at: index) }) {
                                                    Image(systemName: "xmark.circle.fill")
                                                        .foregroundColor(.white)
                                                        .background(Circle().fill(.black.opacity(0.6)))
                                                }
                                                .padding(4)
                                            }
                                            Spacer()
                                        }
                                    }
                                }
                            }
                        }
                        .padding(.horizontal, memoryImages.count > 1 ? 8 : 0)
                    }
                } else if let mediaURL = memory.mediaURL, let url = URL(string: mediaURL) {
                    AsyncImage(url: url) { image in
                        image
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                    } placeholder: {
                        LinearGradient(
                            colors: [.purple.opacity(0.3), .blue.opacity(0.3)],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                        .overlay(
                            ProgressView()
                                .tint(.white)
                        )
                    }
                } else {
                    LinearGradient(
                        colors: [.purple.opacity(0.3), .blue.opacity(0.3)],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                    .overlay(
                        VStack {
                            Image(systemName: memory.type.systemImage)
                                .font(.title)
                                .foregroundColor(.white)
                            Text("No media")
                                .font(.caption)
                                .foregroundColor(.white.opacity(0.8))
                        }
                    )
                }

                // Memory type indicator
                VStack {
                    HStack {
                        Spacer()
                        HStack(spacing: 4) {
                            Image(systemName: memory.type.systemImage)
                                .font(.caption2)
                            Text(memory.type.displayName)
                                .font(.caption2)
                                .fontWeight(.medium)
                        }
                        .foregroundColor(.white)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(
                            Capsule()
                                .fill(.ultraThinMaterial)
                                .environment(\.colorScheme, .dark)
                        )
                    }
                    Spacer()

                    // Show "Changed" indicator if media has been modified
                    if hasMediaChanges {
                        HStack {
                            Spacer()
                            HStack(spacing: 4) {
                                Image(systemName: "checkmark.circle.fill")
                                    .font(.caption2)
                                Text("Changed")
                                    .font(.caption2)
                                    .fontWeight(.medium)
                            }
                            .foregroundColor(.green)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(
                                Capsule()
                                    .fill(.ultraThinMaterial)
                                    .environment(\.colorScheme, .dark)
                            )
                        }
                    }
                }
                .padding(12)
            }
            .frame(height: 200)
            .clipShape(RoundedRectangle(cornerRadius: 16))
        }
        .onChange(of: selectedPhotos) { _, newPhotos in
            Task {
                await loadSelectedPhotos(newPhotos)
            }
        }
        .alert("Remove Media", isPresented: $showingRemoveMediaAlert) {
            Button("Cancel", role: .cancel) { }
            Button("Remove", role: .destructive) {
                removeCurrentMedia()
            }
        } message: {
            Text("Are you sure you want to remove the current media from this memory?")
        }
    }
    
    private var formContent: some View {
        VStack(alignment: .leading, spacing: 24) {
            // Title
            VStack(alignment: .leading, spacing: 8) {
                Text("Title")
                    .font(.headline)
                    .foregroundColor(.primary)
                
                TextField("Enter memory title", text: $title)
                    .textFieldStyle(GlassTextFieldStyle())
            }
            
            // Content
            VStack(alignment: .leading, spacing: 8) {
                Text("Story")
                    .font(.headline)
                    .foregroundColor(.primary)
                
                TextField("Tell the story of this memory...", text: $content, axis: .vertical)
                    .textFieldStyle(GlassTextFieldStyle())
                    .lineLimit(5...10)
            }
            
            // Tags
            VStack(alignment: .leading, spacing: 12) {
                Text("Tags")
                    .font(.headline)
                    .foregroundColor(.primary)
                
                // Existing tags
                if !tags.isEmpty {
                    LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: 8) {
                        ForEach(tags, id: \.self) { tag in
                            HStack {
                                Text(tag)
                                    .font(.caption)
                                    .foregroundColor(.primary)
                                
                                Button(action: { removeTag(tag) }) {
                                    Image(systemName: "xmark.circle.fill")
                                        .font(.caption2)
                                        .foregroundColor(.secondary)
                                }
                            }
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(
                                RoundedRectangle(cornerRadius: 8)
                                    .fill(.ultraThinMaterial)
                            )
                        }
                    }
                }
                
                // Add new tag
                HStack {
                    TextField("Add tag", text: $newTag)
                        .textFieldStyle(GlassTextFieldStyle())
                        .onSubmit {
                            addTag()
                        }
                    
                    Button("Add", action: addTag)
                        .disabled(newTag.isEmpty)
                        .buttonStyle(GlassButtonStyle())
                }
            }
            
            // Settings
            VStack(alignment: .leading, spacing: 16) {
                Text("Settings")
                    .font(.headline)
                    .foregroundColor(.primary)
                
                VStack(spacing: 12) {
                    HStack {
                        Image(systemName: "heart.fill")
                            .foregroundColor(.pink)
                            .frame(width: 24)
                        
                        Text("Favorite")
                            .font(.body)
                            .foregroundColor(.primary)
                        
                        Spacer()
                        
                        Toggle("", isOn: $isFavorite)
                            .toggleStyle(SwitchToggleStyle(tint: .pink))
                    }
                    
                    HStack {
                        Image(systemName: "globe")
                            .foregroundColor(.blue)
                            .frame(width: 24)
                        
                        Text("Public")
                            .font(.body)
                            .foregroundColor(.primary)
                        
                        Spacer()
                        
                        Toggle("", isOn: $isPublic)
                            .toggleStyle(SwitchToggleStyle(tint: .blue))
                    }
                }
                .padding(16)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(.ultraThinMaterial)
                )
            }
        }
    }
    
    private var savingOverlay: some View {
        ZStack {
            Color.black.opacity(0.3)
                .ignoresSafeArea()
            
            VStack(spacing: 16) {
                ProgressView()
                    .tint(.white)
                
                Text("Saving changes...")
                    .font(.subheadline)
                    .foregroundColor(.white)
            }
            .padding(24)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(.ultraThinMaterial)
                    .environment(\.colorScheme, .dark)
            )
        }
    }
    
    private func addTag() {
        let trimmedTag = newTag.trimmingCharacters(in: .whitespacesAndNewlines)
        if !trimmedTag.isEmpty && !tags.contains(trimmedTag) {
            tags.append(trimmedTag)
            newTag = ""
        }
    }
    
    private func removeTag(_ tag: String) {
        tags.removeAll { $0 == tag }
    }

    private func loadSelectedPhotos(_ photos: [PhotosPickerItem]) async {
        // Don't remove all images, append new ones (up to max limit)
        for photo in photos {
            if memoryImages.count >= maxImages { break }

            if let data = try? await photo.loadTransferable(type: Data.self),
               let image = UIImage(data: data) {
                await MainActor.run {
                    memoryImages.append(image)
                    hasMediaChanges = true
                }
            }
        }

        // Clear the selection after loading
        await MainActor.run {
            selectedPhotos.removeAll()
        }
    }

    private func removeImage(at index: Int) {
        guard index < memoryImages.count else { return }
        memoryImages.remove(at: index)
        hasMediaChanges = true
    }

    private func removeCurrentMedia() {
        memory.mediaURL = nil
        memory.thumbnailURL = nil
        memoryImages.removeAll()
        hasMediaChanges = true
    }
    
    private func saveChanges() {
        isSaving = true

        Task {
            // Update memory properties
            memory.title = title
            memory.content = content
            memory.isPublic = isPublic
            memory.isFavorite = isFavorite
            memory.tags = tags
            memory.updatedAt = Date()

            // Get current user ID
            guard let userId = realDataService.getCurrentUserId() else {
                await MainActor.run {
                    isSaving = false
                    errorMessage = "No authenticated user found"
                    showError = true
                }
                return
            }

            // Handle media changes if any
            if hasMediaChanges {
                if !memoryImages.isEmpty {
                    // Upload new images
                    if let firstImage = memoryImages.first,
                       let imageData = firstImage.optimizedForUpload() {

                        print("📤 Uploading updated image for memory: \(memory.title)")

                        // Upload main image
                        let mediaURL = await SupabaseStorageService.shared.uploadMemoryImage(
                            imageData,
                            memoryId: memory.id,
                            userId: userId
                        )

                        // Upload thumbnail
                        if let thumbnailImage = firstImage.thumbnail(),
                           let thumbnailData = thumbnailImage.compressedData(quality: 0.6) {
                            let thumbnailURL = await SupabaseStorageService.shared.uploadMemoryThumbnail(
                                thumbnailData,
                                memoryId: memory.id,
                                userId: userId
                            )
                            memory.thumbnailURL = thumbnailURL
                        }

                        memory.mediaURL = mediaURL
                        print("📤 Updated media URLs - Media: \(mediaURL ?? "nil"), Thumbnail: \(memory.thumbnailURL ?? "nil")")
                    }
                }
                // If memoryImages is empty and hasMediaChanges is true, it means media was removed
                // The URLs are already set to nil in removeCurrentMedia()
            }

            // Update in database
            let success = await realDataService.updateMemory(memory, userId: userId)

            await MainActor.run {
                isSaving = false
                if success {
                    // Update in ProductionMemoryService
                    if let index = productionMemoryService.memories.firstIndex(where: { $0.id == memory.id }) {
                        productionMemoryService.memories[index] = memory
                    }
                    dismiss()
                } else {
                    errorMessage = "Failed to save changes"
                    showError = true
                }
            }
        }
    }
}

// MARK: - Custom Styles
struct GlassTextFieldStyle: TextFieldStyle {
    func _body(configuration: TextField<Self._Label>) -> some View {
        configuration
            .padding(12)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(.ultraThinMaterial)
            )
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(.white.opacity(0.2), lineWidth: 1)
            )
    }
}

struct GlassButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .padding(.horizontal, 16)
            .padding(.vertical, 8)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(.ultraThinMaterial)
            )
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
            .animation(.spring(response: 0.3), value: configuration.isPressed)
    }
}

// MARK: - Preview
#Preview {
    let sampleMemory = Memory(
        title: "Beautiful sunset at the beach",
        content: "A wonderful evening watching the sunset with my beloved pet.",
        type: .photo,
        mediaURL: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4",
        tags: ["sunset", "beach", "peaceful"],
        isFavorite: true
    )
    
    return EditMemoryView(memory: sampleMemory)
        .environmentObject(RealDataService())
}
