//
//  MemoryVaultView.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI

struct MemoryVaultView: View {
    @EnvironmentObject var realDataService: RealDataService
    @EnvironmentObject var subscriptionService: SubscriptionService
    @StateObject private var productionMemoryService = ProductionMemoryService.shared
    @StateObject private var advancedMemoryService = AdvancedMemoryService.shared

    @StateObject private var vaultService = SecureVaultService.shared
    @State private var selectedTab = 0
    @State private var showAddMemory = false
    @State private var showCreateVault = false


    @State private var searchText = ""
    @State private var selectedFilter: MemoryFilter = .all
    @State private var animateCards = false
    @State private var showPremiumUpgrade = false
    @State private var showErrorAlert = false
    @State private var showNetworkError = false
    @State private var isOfflineMode = false
    @State private var lastFailedOperation: (() -> Void)?
    @State private var selectedMemory: Memory?
    @State private var showMemoryDetail = false
    @State private var showWalkMemories = false

    enum MemoryFilter: String, CaseIterable {
        case all = "All"
        case photos = "Photos"
        case videos = "Videos"
        case milestones = "Milestones"
        case favorites = "Favorites"
    }

    // MARK: - Computed Properties

    private var filteredMemories: [Memory] {
        var memories = realDataService.memories

        // Apply filter
        switch selectedFilter {
        case .all:
            break
        case .photos:
            memories = memories.filter { $0.type == .photo }
        case .videos:
            memories = memories.filter { $0.type == .video }
        case .milestones:
            memories = memories.filter { $0.type == .milestone || $0.milestone != nil }
        case .favorites:
            memories = memories.filter { $0.isFavorite }
        }

        // Apply search
        if !searchText.isEmpty {
            memories = memories.filter { memory in
                memory.title.localizedCaseInsensitiveContains(searchText) ||
                memory.content.localizedCaseInsensitiveContains(searchText) ||
                memory.tags.contains { $0.localizedCaseInsensitiveContains(searchText) }
            }
        }

        return memories
    }

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Tab Selector
                tabSelectorSection

                // Content based on selected tab
                TabView(selection: $selectedTab) {
                    memoriesTab
                        .tag(0)

                    vaultsTab
                        .tag(1)
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
            }
            .navigationTitle("Memories & Vault")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button(action: { showWalkMemories = true }) {
                        HStack(spacing: 6) {
                            Image(systemName: "figure.walk")
                                .font(.title3)
                            Text("Walk Memories")
                                .font(.petSubheadline)
                                .fontWeight(.medium)
                        }
                        .foregroundColor(.themeAccent)
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Menu {
                        // Basic Features
                        Button(action: { showAddMemory = true }) {
                            Label("Add Memory", systemImage: "photo.badge.plus")
                        }

                        Button(action: { showCreateVault = true }) {
                            Label("Create Vault", systemImage: "folder.badge.plus")
                        }





                    } label: {
                        Image(systemName: "plus.circle.fill")
                            .font(.title2)
                            .foregroundColor(.purple)
                    }
                }
            }
            .sheet(isPresented: $showAddMemory) {
                AddMemoryView()
                    .environmentObject(realDataService)
            }
            .sheet(isPresented: $showCreateVault) {
                CreateVaultView()
                    .environmentObject(SecureVaultService.shared)
            }
            .sheet(isPresented: $showWalkMemories) {
                WalkMemoriesView()
                    .environmentObject(PetPlannerService.shared)
            }


            .sheet(isPresented: $showPremiumUpgrade) {
                PremiumUpgradeView(feature: "Advanced Memory Features")
                    .environmentObject(subscriptionService)
            }
            .sheet(isPresented: $showMemoryDetail) {
                if let selectedMemory = selectedMemory {
                    EnhancedMemoryDetailView(memory: selectedMemory)
                        .environmentObject(realDataService)
                }
            }
            .alert("Error", isPresented: $showErrorAlert) {
                Button("OK") { }
            } message: {
                Text(productionMemoryService.error?.localizedDescription ?? "An unknown error occurred")
            }
            .overlay {
                if productionMemoryService.isUploading {
                    uploadProgressOverlay
                }
            }
            .onAppear {
                withAnimation(.spring(response: 0.8, dampingFraction: 0.8)) {
                    animateCards = true
                }

                // Ensure ProductionMemoryService is connected to RealDataService
                productionMemoryService.setRealDataService(realDataService)

                // Load memories only if not already loaded
                if realDataService.memories.isEmpty && !realDataService.isLoading {
                    Task {
                        await realDataService.refreshAllData()
                    }
                }
            }
            .onChange(of: productionMemoryService.error) { _, error in
                if error != nil {
                    showErrorAlert = true
                }
            }
            .alert("Error", isPresented: $showErrorAlert) {
                Button("Retry") {
                    productionMemoryService.loadMemories()
                }
                Button("Cancel", role: .cancel) { }
            } message: {
                Text(productionMemoryService.error?.localizedDescription ?? "An unknown error occurred")
            }
            .alert("Network Error", isPresented: $showNetworkError) {
                Button("Retry") {
                    retryFailedOperation()
                }
                Button("Offline Mode") {
                    enableOfflineMode()
                }
                Button("Cancel", role: .cancel) { }
            } message: {
                Text("Unable to connect to the server. You can continue in offline mode or retry when connection is restored.")
            }
        }
    }

    // MARK: - Upload Progress Overlay

    private var uploadProgressOverlay: some View {
        ZStack {
            Color.black.opacity(0.4)
                .ignoresSafeArea()

            VStack(spacing: 20) {
                ProgressView(value: productionMemoryService.uploadProgress)
                    .progressViewStyle(LinearProgressViewStyle(tint: .purple))
                    .frame(width: 200)

                Text(productionMemoryService.processingStatus)
                    .font(.petSubheadline)
                    .foregroundColor(.white)
                    .multilineTextAlignment(.center)

                Text("\(Int(productionMemoryService.uploadProgress * 100))%")
                    .font(.petTitle3)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
            }
            .padding(30)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(.ultraThinMaterial)
            )
        }
    }

    // MARK: - Tab Selector Section

    private var tabSelectorSection: some View {
        HStack(spacing: 0) {
            tabButton(title: "Memories", icon: "photo.fill", index: 0)
            tabButton(title: "Vaults", icon: "archivebox.fill", index: 1)
        }
        .padding(.horizontal)
        .background(Color(.systemBackground))
    }

    private func tabButton(title: String, icon: String, index: Int) -> some View {
        Button(action: { selectedTab = index }) {
            VStack(spacing: 6) {
                Image(systemName: icon)
                    .font(.caption)
                    .foregroundColor(selectedTab == index ? .purple : .secondary)

                Text(title)
                    .font(.petCaption)
                    .fontWeight(.medium)
                    .foregroundColor(selectedTab == index ? .purple : .secondary)
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(selectedTab == index ? Color.purple.opacity(0.1) : Color.clear)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }

    // MARK: - Memories Tab

    private var memoriesTab: some View {
        VStack(spacing: 0) {
            // Search and Filter
            searchAndFilterSection

            // Memories Grid
            if realDataService.isLoading {
                loadingMemoriesView
            } else if filteredMemories.isEmpty {
                emptyMemoriesView
            } else {
                memoriesGridView
            }
        }
    }

    private var searchAndFilterSection: some View {
        VStack(spacing: 16) {
            // Search Bar
            HStack {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(.secondary)

                TextField("Search memories...", text: $searchText)
                    .textFieldStyle(PlainTextFieldStyle())

                if !searchText.isEmpty {
                    Button(action: { searchText = "" }) {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundColor(.secondary)
                    }
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.themeSecondaryBackground)
            )

            // Filter Chips
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(MemoryFilter.allCases, id: \.self) { filter in
                        filterChip(filter: filter)
                    }
                }
                .padding(.horizontal)
            }
        }
        .padding()
        .background(Color.themeBackground)
    }

    private func filterChip(filter: MemoryFilter) -> some View {
        Button(action: { selectedFilter = filter }) {
            Text(filter.rawValue)
                .font(.petSubheadline)
                .fontWeight(.medium)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .fill(selectedFilter == filter ? Color.themeAccent : Color.themeSecondaryBackground)
                )
                .foregroundColor(selectedFilter == filter ? .white : Color.themePrimary)
        }
        .buttonStyle(PlainButtonStyle())
    }

    private var memoriesGridView: some View {
        ScrollView {
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                ForEach(Array(filteredMemories.enumerated()), id: \.element.id) { index, memory in
                    GlassMemoryCard(
                        memory: memory,
                        onEdit: {
                            selectedMemory = memory
                            showMemoryDetail = true
                        },
                        onDelete: {
                            deleteMemory(memory)
                        },
                        onTap: {
                            selectedMemory = memory
                            showMemoryDetail = true
                        }
                    )
                    .scaleEffect(animateCards ? 1.0 : 0.9)
                    .opacity(animateCards ? 1.0 : 0.0)
                    .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(Double(index) * 0.05), value: animateCards)
                    .onAppear {
                        // Only preload if we have many memories and this is actually the last one
                        if filteredMemories.count > 10 && memory == filteredMemories.last {
                            loadMoreMemoriesIfNeeded()
                        }
                    }
                }

                // Loading indicator for pagination
                if realDataService.isLoading && !filteredMemories.isEmpty {
                    VStack {
                        ProgressView()
                            .tint(.purple)
                        Text("Loading more memories...")
                            .font(.petCaption)
                            .foregroundColor(.secondary)
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .gridCellColumns(2)
                }
            }
            .padding()
            .padding(.bottom, 100)
        }
        .refreshable {
            // Only refresh if not already loading
            guard !realDataService.isLoading else {
                print("⚠️ Already loading memories, skipping refresh...")
                return
            }
            print("🔄 Manual refresh triggered...")
            await realDataService.refreshAllData()
        }
    }

    // MARK: - Performance Optimization Methods

    private func loadMoreMemoriesIfNeeded() {
        // Implement pagination logic with proper guards
        guard !productionMemoryService.isLoading else {
            print("⚠️ Already loading memories, skipping pagination...")
            return
        }

        // Only load more if we actually have a reasonable number of memories
        guard filteredMemories.count >= 10 else {
            print("⚠️ Not enough memories to trigger pagination (have \(filteredMemories.count))")
            return
        }

        print("📄 Loading more memories (pagination)...")
        Task {
            // Load next batch of memories
            await productionMemoryService.loadMoreMemories()
        }
    }

    private func memoryCard(memory: Memory) -> some View {
        VStack(spacing: 0) {
            // Memory Image/Video
            ZStack {
                if let mediaURL = memory.mediaURL, let url = URL(string: mediaURL) {
                    AsyncImage(url: url) { image in
                        image
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                    } placeholder: {
                        RoundedRectangle(cornerRadius: 12)
                            .fill(
                                LinearGradient(
                                    colors: [.purple.opacity(0.3), .blue.opacity(0.3)],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                            .overlay(
                                ProgressView()
                                    .tint(.white)
                            )
                    }
                    .frame(height: 150)
                    .clipShape(RoundedRectangle(cornerRadius: 12))
                } else {
                    RoundedRectangle(cornerRadius: 12)
                        .fill(
                            LinearGradient(
                                colors: [.purple.opacity(0.3), .blue.opacity(0.3)],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .aspectRatio(1, contentMode: .fit)
                        .overlay(
                            VStack {
                                Image(systemName: memory.type.systemImage)
                                    .font(.title)
                                    .foregroundColor(.white)

                                Text(memory.type.displayName)
                                    .font(.petCaption)
                                    .foregroundColor(.white)
                            }
                        )
                }

                // Video duration overlay
                if memory.type == .video, let duration = memory.formattedDuration {
                    VStack {
                        HStack {
                            Spacer()
                            Text(duration)
                                .font(.caption2)
                                .fontWeight(.semibold)
                                .foregroundColor(.white)
                                .padding(.horizontal, 6)
                                .padding(.vertical, 2)
                                .background(
                                    RoundedRectangle(cornerRadius: 4)
                                        .fill(Color.black.opacity(0.7))
                                )
                                .padding(8)
                        }
                        Spacer()
                    }
                }

                // AI Analysis indicator
                if !memory.tags.isEmpty || memory.milestone != nil {
                    VStack {
                        Spacer()
                        HStack {
                            Spacer()
                            Image(systemName: "brain.head.profile")
                                .font(.caption2)
                                .foregroundColor(.white)
                                .padding(.horizontal, 6)
                                .padding(.vertical, 2)
                                .background(
                                    RoundedRectangle(cornerRadius: 4)
                                        .fill(Color.purple.opacity(0.8))
                                )
                                .padding(8)
                        }
                    }
                }
            }

            // Memory Info
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text(memory.title)
                        .font(.petSubheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                        .lineLimit(1)

                    Spacer()

                    if let pet = memory.pet {
                        Text(pet.name)
                            .font(.petCaption)
                            .foregroundColor(.secondary)
                    }
                }

                Text(timeAgoString(from: memory.createdAt))
                    .font(.petCaption)
                    .foregroundColor(.secondary)

                // AI Analysis tags
                if !memory.tags.isEmpty {
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: 4) {
                            ForEach(memory.tags.prefix(3), id: \.self) { tag in
                                Text(tag)
                                    .font(.caption2)
                                    .foregroundColor(.purple)
                                    .padding(.horizontal, 6)
                                    .padding(.vertical, 2)
                                    .background(
                                        RoundedRectangle(cornerRadius: 4)
                                            .fill(Color.purple.opacity(0.1))
                                    )
                            }
                        }
                    }
                }

                // Milestone indicator
                if let milestone = memory.milestone {
                    HStack {
                        Image(systemName: "star.fill")
                            .font(.caption2)
                            .foregroundColor(.orange)

                        Text(milestone)
                            .font(.caption2)
                            .foregroundColor(.orange)
                            .lineLimit(1)
                    }
                    .padding(.horizontal, 6)
                    .padding(.vertical, 2)
                    .background(
                        RoundedRectangle(cornerRadius: 4)
                            .fill(Color.orange.opacity(0.1))
                    )
                }
            }
            .padding(12)
        }
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
        .onTapGesture {
            selectedMemory = memory
            showMemoryDetail = true
        }
    }

    // MARK: - Helper Methods

    private func timeAgoString(from date: Date) -> String {
        let formatter = RelativeDateTimeFormatter()
        formatter.unitsStyle = .abbreviated
        return formatter.localizedString(for: date, relativeTo: Date())
    }

    private var loadingMemoriesView: some View {
        VStack(spacing: 20) {
            ProgressView()
                .scaleEffect(1.5)
                .tint(.purple)

            Text("Loading memories...")
                .font(.petSubheadline)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding()
    }

    private var emptyMemoriesView: some View {
        VStack(spacing: 24) {
            Spacer()

            Image(systemName: "photo.circle.fill")
                .font(.system(size: 80))
                .foregroundColor(.purple.opacity(0.6))

            VStack(spacing: 8) {
                Text("No memories yet")
                    .font(.petTitle2)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)

                Text("Start capturing precious moments with your pets")
                    .font(.petSubheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }

            VStack(spacing: 12) {
                Button(action: { showAddMemory = true }) {
                    HStack {
                        Image(systemName: "plus.circle.fill")
                        Text("Add First Memory")
                    }
                    .font(.petSubheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color.purple)
                    )
                }


            }

            Spacer()
        }
        .padding()
    }

    // MARK: - Vaults Tab

    private var vaultsTab: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                if vaultService.vaults.isEmpty {
                    emptyVaultsView
                } else {
                    ForEach(Array(vaultService.vaults.enumerated()), id: \.element.id) { index, vault in
                        realVaultCard(vault: vault)
                            .scaleEffect(animateCards ? 1.0 : 0.9)
                            .opacity(animateCards ? 1.0 : 0.0)
                            .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(Double(index) * 0.1), value: animateCards)
                    }
                }
            }
            .padding()
            .padding(.bottom, 100)
        }
    }

    private func realVaultCard(vault: SecureVault) -> some View {
        HStack(spacing: 16) {
            // Vault Icon
            ZStack {
                RoundedRectangle(cornerRadius: 12)
                    .fill(LinearGradient(
                        gradient: Gradient(colors: [.purple, .blue]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ))
                    .frame(width: 60, height: 60)

                Image(systemName: vault.isLocked ? "lock.fill" : "lock.open.fill")
                    .font(.title2)
                    .foregroundColor(.white)
            }

            // Vault Info
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text(vault.name)
                        .font(.petSubheadline)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)

                    Spacer()

                    if vault.isLocked {
                        Text("LOCKED")
                            .font(.caption2)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(
                                RoundedRectangle(cornerRadius: 4)
                                    .fill(Color.red)
                            )
                    } else {
                        Text("UNLOCKED")
                            .font(.caption2)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(
                                RoundedRectangle(cornerRadius: 4)
                                    .fill(Color.green)
                            )
                    }
                }

                Text("\(vault.memoryCount) memories • Created \(formatDate(vault.createdAt))")
                    .font(.petCaption)
                    .foregroundColor(.secondary)

                Text(vault.description)
                    .font(.petCaption)
                    .foregroundColor(.secondary)
                    .lineLimit(2)
            }

            // Access Button
            Button(action: {
                // TODO: Show unlock vault view
            }) {
                Image(systemName: "chevron.right")
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 2)
        )
    }

    private func formatDate(_ date: Date) -> String {
        let formatter = RelativeDateTimeFormatter()
        formatter.unitsStyle = .abbreviated
        return formatter.localizedString(for: date, relativeTo: Date())
    }

    private func vaultCard(index: Int) -> some View {
        HStack(spacing: 16) {
            // Vault Icon
            ZStack {
                RoundedRectangle(cornerRadius: 12)
                    .fill(
                        LinearGradient(
                            colors: [.orange.opacity(0.3), .red.opacity(0.3)],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 60, height: 60)

                Image(systemName: "archivebox.fill")
                    .font(.title2)
                    .foregroundColor(.white)
            }

            // Vault Info
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text("Vault \(index + 1)")
                        .font(.petSubheadline)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)

                    Spacer()

                    if index % 3 == 0 {
                        Text("LOCKED")
                            .font(.caption2)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(
                                RoundedRectangle(cornerRadius: 4)
                                    .fill(Color.orange)
                            )
                    }
                }

                Text("\(Int.random(in: 5...50)) memories • Created \(Int.random(in: 1...365)) days ago")
                    .font(.petCaption)
                    .foregroundColor(.secondary)

                Text("A collection of special moments from \(realDataService.pets.randomElement()?.name ?? "your pet")")
                    .font(.petCaption)
                    .foregroundColor(.secondary)
                    .lineLimit(2)
            }

            // Chevron
            Image(systemName: "chevron.right")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
        .onTapGesture {
            // Navigate to vault detail
        }
    }

    private var emptyVaultsView: some View {
        VStack(spacing: 24) {
            Spacer()

            Image(systemName: "archivebox.circle.fill")
                .font(.system(size: 80))
                .foregroundColor(.orange.opacity(0.6))

            VStack(spacing: 8) {
                Text("No vaults created")
                    .font(.petTitle2)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)

                Text("Create time capsules to preserve special moments")
                    .font(.petSubheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }

            Button(action: { showCreateVault = true }) {
                HStack {
                    Image(systemName: "plus.circle.fill")
                    Text("Create First Vault")
                }
                .font(.petSubheadline)
                .fontWeight(.semibold)
                .foregroundColor(.white)
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.orange)
                )
            }

            Spacer()
        }
        .padding()
    }











    // MARK: - Error Handling Methods

    private func retryFailedOperation() {
        lastFailedOperation?()
    }

    private func enableOfflineMode() {
        isOfflineMode = true
        showNetworkError = false
        // TODO: Implement offline mode functionality
    }

    private func handleNetworkError(operation: @escaping () -> Void) {
        lastFailedOperation = operation
        showNetworkError = true
    }

    private func deleteMemory(_ memory: Memory) {
        Task {
            guard let userId = realDataService.getCurrentUserId() else {
                print("❌ No authenticated user found for memory deletion")
                return
            }

            let success = await realDataService.deleteMemory(memory.id, userId: userId)

            await MainActor.run {
                if success {
                    // Remove from ProductionMemoryService as well to keep them in sync
                    productionMemoryService.memories.removeAll { $0.id == memory.id }
                    print("✅ Memory deleted successfully")
                } else {
                    print("❌ Failed to delete memory")
                    // TODO: Show error alert to user
                }
            }
        }
    }
}
