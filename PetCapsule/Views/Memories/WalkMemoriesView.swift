//
//  WalkMemoriesView.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 6/6/25.
//

import SwiftUI

struct WalkMemoriesView: View {
    @EnvironmentObject var plannerService: PetPlannerService
    @State private var selectedFilter: MemoryFilter = .all
    
    enum MemoryFilter: String, CaseIterable {
        case all = "All"
        case thisWeek = "This Week"
        case thisMonth = "This Month"
        case favorites = "Favorites"
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                LazyVStack(spacing: 16) {
                    // Filter Tabs
                    filterTabs
                    
                    // Memory Grid
                    memoryGrid
                }
                .padding(.horizontal, 16)
                .padding(.top, 20)
            }
            .navigationTitle("Walk Memories")
            .navigationBarTitleDisplayMode(.large)
            .background(EuropeanDesign.Gradients.elegantBackground)
        }
    }
    
    private var filterTabs: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                ForEach(MemoryFilter.allCases, id: \.self) { filter in
                    filterButton(for: filter)
                }
            }
            .padding(.horizontal, 4)
        }
    }
    
    private var memoryGrid: some View {
        LazyVGrid(columns: [
            GridItem(.flexible(), spacing: 8),
            GridItem(.flexible(), spacing: 8)
        ], spacing: 16) {
            ForEach(filteredMemories, id: \.id) { memory in
                memoryCard(memory)
            }
        }
    }
    
    private var filteredMemories: [WalkMemory] {
        switch selectedFilter {
        case .all:
            return plannerService.walkMemories
        case .thisWeek:
            return plannerService.walkMemories.filter { Calendar.current.isDate($0.date, equalTo: Date(), toGranularity: .weekOfYear) }
        case .thisMonth:
            return plannerService.walkMemories.filter { Calendar.current.isDate($0.date, equalTo: Date(), toGranularity: .month) }
        case .favorites:
            return plannerService.walkMemories.filter { $0.isFavorite }
        }
    }
    
    private func filterButton(for filter: MemoryFilter) -> some View {
        Button(action: {
            withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                selectedFilter = filter
            }
        }) {
            Text(filter.rawValue)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(selectedFilter == filter ? .white : .blue)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(selectedFilter == filter ? .blue : .blue.opacity(0.1))
                )
        }
    }
    
    private func memoryCard(_ memory: WalkMemory) -> some View {
        EuropeanCard {
            VStack(alignment: .leading, spacing: 12) {
                Rectangle()
                    .fill(EuropeanDesign.Colors.surfaceSecondary)
                    .overlay(
                        Image(systemName: "photo")
                            .foregroundColor(EuropeanDesign.Colors.textSecondary)
                    )
                    .frame(height: 120)
                    .clipped()
                    .cornerRadius(12)
                
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Text(memory.location)
                            .font(.system(size: 14, weight: .semibold))
                            .foregroundColor(EuropeanDesign.Colors.textPrimary)
                            .lineLimit(1)
                        
                        Spacer()
                        
                        Button(action: {
                            plannerService.toggleFavorite(for: memory.id)
                        }) {
                            Image(systemName: memory.isFavorite ? "heart.fill" : "heart")
                                .foregroundColor(memory.isFavorite ? .red : EuropeanDesign.Colors.textSecondary)
                                .font(.system(size: 14))
                        }
                    }
                    
                    Text(memory.date.formatted(date: .abbreviated, time: .omitted))
                        .font(.caption)
                        .foregroundColor(EuropeanDesign.Colors.textSecondary)
                    
                    HStack {
                        Image(systemName: "figure.walk")
                            .foregroundColor(.blue)
                            .font(.caption)
                        
                        Text(memory.duration)
                            .font(.caption)
                            .foregroundColor(EuropeanDesign.Colors.textSecondary)
                        
                        Spacer()
                        
                        Image(systemName: "location.fill")
                            .foregroundColor(.green)
                            .font(.caption)
                        
                        Text(memory.distance)
                            .font(.caption)
                            .foregroundColor(EuropeanDesign.Colors.textSecondary)
                    }
                }
                .padding(.horizontal, 12)
                .padding(.bottom, 12)
            }
        }
    }
}

// MARK: - Preview
#Preview {
    WalkMemoriesView()
        .environmentObject(PetPlannerService.shared)
}
