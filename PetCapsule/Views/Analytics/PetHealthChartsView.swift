//
//  PetHealthChartsView.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 12/24/24.
//

import SwiftUI
import Charts

struct PetHealthChartsView: View {
    let pet: Pet
    @StateObject private var healthAnalytics = PetHealthAnalyticsService.shared
    @State private var selectedTimeRange: TimeRange = .week
    @State private var selectedMetric: HealthMetric = .weight
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // Time Range Picker
                    TimeRangePicker(selectedRange: $selectedTimeRange)
                    
                    // Metric Selector
                    MetricSelector(selectedMetric: $selectedMetric)
                    
                    // Main Chart
                    MainHealthChart(
                        pet: pet,
                        metric: selectedMetric,
                        timeRange: selectedTimeRange
                    )
                    
                    // Health Insights
                    HealthInsightsSection(pet: pet)
                    
                    // Activity Summary
                    ActivitySummaryChart(pet: pet, timeRange: selectedTimeRange)
                    
                    // Medication Adherence
                    MedicationAdherenceChart(pet: pet, timeRange: selectedTimeRange)
                }
                .padding()
            }
            .navigationTitle("Health Analytics")
            .onAppear {
                healthAnalytics.loadHealthData(for: pet)
            }
        }
    }
}

// MARK: - Time Range Picker
struct TimeRangePicker: View {
    @Binding var selectedRange: TimeRange
    
    var body: some View {
        Picker("Time Range", selection: $selectedRange) {
            ForEach(TimeRange.allCases, id: \.self) { range in
                Text(range.displayName).tag(range)
            }
        }
        .pickerStyle(.segmented)
    }
}

// MARK: - Metric Selector
struct MetricSelector: View {
    @Binding var selectedMetric: HealthMetric
    
    var body: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                ForEach(HealthMetric.allCases, id: \.self) { metric in
                    MetricButton(
                        metric: metric,
                        isSelected: selectedMetric == metric
                    ) {
                        selectedMetric = metric
                    }
                }
            }
            .padding(.horizontal)
        }
    }
}

struct MetricButton: View {
    let metric: HealthMetric
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 4) {
                Image(systemName: metric.icon)
                    .font(.title2)
                    .foregroundColor(isSelected ? .white : metric.color)
                
                Text(metric.displayName)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(isSelected ? .white : .primary)
            }
            .padding(.vertical, 8)
            .padding(.horizontal, 12)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(isSelected ? metric.color : Color(.systemGray6))
            )
        }
        .buttonStyle(.plain)
    }
}

// MARK: - Main Health Chart
struct MainHealthChart: View {
    let pet: Pet
    let metric: HealthMetric
    let timeRange: TimeRange
    @StateObject private var healthAnalytics = PetHealthAnalyticsService.shared
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                VStack(alignment: .leading) {
                    Text(metric.displayName)
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    if let latestValue = healthAnalytics.getLatestValue(for: metric) {
                        Text(latestValue)
                            .font(.title2)
                            .fontWeight(.bold)
                            .foregroundColor(metric.color)
                    }
                }
                
                Spacer()
                
                TrendIndicator(trend: healthAnalytics.getTrend(for: metric))
            }
            
            Chart(healthAnalytics.getChartData(for: metric, timeRange: timeRange)) { dataPoint in
                LineMark(
                    x: .value("Date", dataPoint.date),
                    y: .value(metric.displayName, dataPoint.value)
                )
                .foregroundStyle(metric.color.gradient)
                .lineStyle(StrokeStyle(lineWidth: 3))
                
                AreaMark(
                    x: .value("Date", dataPoint.date),
                    y: .value(metric.displayName, dataPoint.value)
                )
                .foregroundStyle(
                    LinearGradient(
                        colors: [metric.color.opacity(0.3), metric.color.opacity(0.1)],
                        startPoint: .top,
                        endPoint: .bottom
                    )
                )
                
                PointMark(
                    x: .value("Date", dataPoint.date),
                    y: .value(metric.displayName, dataPoint.value)
                )
                .foregroundStyle(metric.color)
                .symbolSize(30)
            }
            .frame(height: 200)
            .chartXAxis {
                AxisMarks(values: .stride(by: timeRange.axisStride)) { value in
                    AxisGridLine()
                    AxisValueLabel(format: timeRange.dateFormat)
                }
            }
            .chartYAxis {
                AxisMarks { value in
                    AxisGridLine()
                    AxisValueLabel()
                }
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(16)
    }
}

// MARK: - Trend Indicator
struct TrendIndicator: View {
    let trend: HealthTrend
    
    var body: some View {
        HStack(spacing: 4) {
            Image(systemName: trend.icon)
                .font(.caption)
                .foregroundColor(trend.color)
            
            Text(trend.displayName)
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(trend.color)
        }
        .padding(.horizontal, 8)
        .padding(.vertical, 4)
        .background(trend.color.opacity(0.1))
        .cornerRadius(8)
    }
}

// MARK: - Health Insights Section
struct HealthInsightsSection: View {
    let pet: Pet
    @StateObject private var healthAnalytics = PetHealthAnalyticsService.shared
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Health Insights")
                .font(.headline)
                .fontWeight(.semibold)
            
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 12) {
                ForEach(healthAnalytics.getHealthInsights(for: pet)) { insight in
                    HealthInsightCard(insight: insight)
                }
            }
        }
    }
}

struct HealthInsightCard: View {
    let insight: HealthInsight
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: insight.icon)
                    .font(.title2)
                    .foregroundColor(insight.color)
                
                Spacer()
                
                Text(insight.description)
                    .font(.title3)
                    .fontWeight(.bold)
            }
            
            Text(insight.title)
                .font(.subheadline)
                .fontWeight(.medium)
            
            Text(insight.description)
                .font(.caption)
                .foregroundColor(.secondary)
                .lineLimit(2)
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

// MARK: - Activity Summary Chart
struct ActivitySummaryChart: View {
    let pet: Pet
    let timeRange: TimeRange
    @StateObject private var healthAnalytics = PetHealthAnalyticsService.shared
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Activity Summary")
                .font(.headline)
                .fontWeight(.semibold)
            
            Chart(healthAnalytics.getActivityData(for: pet, timeRange: timeRange)) { activity in
                BarMark(
                    x: .value("Date", activity.date),
                    y: .value("Minutes", activity.duration)
                )
                .foregroundStyle(Color.blue.gradient)
                .cornerRadius(4)
            }
            .frame(height: 150)
            .chartXAxis {
                AxisMarks(values: .stride(by: timeRange.axisStride)) { value in
                    AxisGridLine()
                    AxisValueLabel(format: timeRange.dateFormat)
                }
            }
            .chartYAxis {
                AxisMarks { value in
                    AxisGridLine()
                    AxisValueLabel()
                }
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(16)
    }
}

// MARK: - Medication Adherence Chart
struct MedicationAdherenceChart: View {
    let pet: Pet
    let timeRange: TimeRange
    @StateObject private var healthAnalytics = PetHealthAnalyticsService.shared
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Medication Adherence")
                .font(.headline)
                .fontWeight(.semibold)
            
            Chart(healthAnalytics.getMedicationData(for: pet, timeRange: timeRange)) { medication in
                SectorMark(
                    angle: .value("Adherence", medication.adherencePercentage),
                    innerRadius: .ratio(0.6),
                    angularInset: 2
                )
                .foregroundStyle(medication.color)
                .opacity(0.8)
            }
            .frame(height: 150)
            
            // Legend
            HStack {
                ForEach(healthAnalytics.getMedicationData(for: pet, timeRange: timeRange)) { medication in
                    HStack(spacing: 4) {
                        Circle()
                            .fill(medication.color)
                            .frame(width: 8, height: 8)
                        
                        Text(medication.name)
                            .font(.caption)
                        
                        Text("\(Int(medication.adherencePercentage))%")
                            .font(.caption)
                            .fontWeight(.medium)
                    }
                }
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(16)
    }
}

#Preview {
    PetHealthChartsView(pet: Pet.samplePet)
}
