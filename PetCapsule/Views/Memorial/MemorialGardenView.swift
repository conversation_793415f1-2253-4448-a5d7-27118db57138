//
//  MemorialGardenView.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI

struct MemorialGardenView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var memorialService = MemorialGardenService.shared
    @State private var selectedPet: Pet?
    @State private var showingCreateMemorial = false
    @State private var animateFlowers = false
    @State private var showingTribute = false
    @State private var selectedMemorial: MemorialGarden?
    
    var body: some View {
        NavigationView {
            ZStack {
                // Peaceful background
                LinearGradient(
                    colors: [
                        Color(red: 0.95, green: 0.98, blue: 1.0),
                        Color(red: 0.98, green: 0.95, blue: 1.0),
                        Color(red: 1.0, green: 0.98, blue: 0.95)
                    ],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()
                
                ScrollView {
                    VStack(spacing: Spacing.xl) {
                        // Custom Navigation Bar
                        HStack {
                            Button(action: {
                                dismiss()
                            }) {
                                HStack(spacing: 8) {
                                    Image(systemName: "chevron.left")
                                        .font(.title2)
                                    Text("Back")
                                        .font(.petHeadline)
                                }
                                .foregroundColor(.black)
                                .padding(.horizontal, 16)
                                .padding(.vertical, 8)
                                .background(
                                    RoundedRectangle(cornerRadius: 20)
                                        .fill(Color.white.opacity(0.8))
                                        .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
                                )
                            }

                            Spacer()
                        }
                        .padding(.horizontal, Spacing.lg)
                        .padding(.top, Spacing.md)

                        // Header
                        VStack(spacing: Spacing.md) {
                            Text("Memorial Gardens")
                                .font(.petLargeTitle)
                                .foregroundColor(.black)

                            Text("A peaceful place to honor and remember our beloved companions")
                                .font(.petBody)
                                .foregroundColor(.black.opacity(0.7))
                                .multilineTextAlignment(.center)
                                .padding(.horizontal, Spacing.lg)
                        }
                        .padding(.top, Spacing.lg)
                        
                        // Create Memorial Button
                        Button(action: {
                            showingCreateMemorial = true
                        }) {
                            HStack {
                                Image(systemName: "plus.circle.fill")
                                    .font(.title2)
                                Text("Create Memorial")
                                    .font(.petHeadline)
                            }
                            .foregroundColor(.white)
                            .padding(.horizontal, Spacing.xl)
                            .padding(.vertical, Spacing.md)
                            .background(
                                LinearGradient(
                                    colors: [Color.purple.opacity(0.7), Color.blue.opacity(0.7)],
                                    startPoint: .leading,
                                    endPoint: .trailing
                                )
                            )
                            .cornerRadius(CornerRadius.lg)
                            .shadow(color: .purple.opacity(0.3), radius: 8, x: 0, y: 4)
                        }
                        
                        // Memorial Gardens Grid
                        if memorialService.memorialGardens.isEmpty {
                            EmptyMemorialGardensView()
                        } else {
                            LazyVGrid(columns: [
                                GridItem(.flexible()),
                                GridItem(.flexible())
                            ], spacing: Spacing.lg) {
                                ForEach(memorialService.memorialGardens) { memorial in
                                    MemorialGardenCard(
                                        memorial: memorial,
                                        animateFlowers: $animateFlowers
                                    )
                                    .onTapGesture {
                                        selectedMemorial = memorial
                                        showingTribute = true
                                    }
                                }
                            }
                            .padding(.horizontal, Spacing.lg)
                        }
                        
                        // Community Memorials Section
                        if !memorialService.communityMemorials.isEmpty {
                            VStack(alignment: .leading, spacing: Spacing.lg) {
                                Text("Community Memorials")
                                    .font(.petTitle2)
                                    .foregroundColor(.black)
                                    .padding(.horizontal, Spacing.lg)
                                
                                ScrollView(.horizontal, showsIndicators: false) {
                                    HStack(spacing: Spacing.md) {
                                        ForEach(memorialService.communityMemorials) { memorial in
                                            CommunityMemorialCard(memorial: memorial)
                                                .onTapGesture {
                                                    selectedMemorial = memorial
                                                    showingTribute = true
                                                }
                                        }
                                    }
                                    .padding(.horizontal, Spacing.lg)
                                }
                            }
                        }
                        
                        Spacer(minLength: 100)
                    }
                }
            }
            .navigationBarHidden(true)
            .onAppear {
                Task {
                    await memorialService.loadMemorialGardens()
                }
                
                withAnimation(.easeInOut(duration: 2).repeatForever(autoreverses: true)) {
                    animateFlowers = true
                }
            }
            .sheet(isPresented: $showingCreateMemorial) {
                CreateMemorialView()
            }
            .sheet(isPresented: $showingTribute) {
                if let memorial = selectedMemorial {
                    MemorialTributeView(memorial: memorial)
                }
            }
        }
    }
}

struct MemorialGardenCard: View {
    let memorial: MemorialGarden
    @Binding var animateFlowers: Bool
    
    var body: some View {
        VStack(spacing: Spacing.md) {
            // Pet Photo with Halo Effect
            ZStack {
                // Halo background
                Circle()
                    .fill(
                        RadialGradient(
                            colors: [
                                Color.white.opacity(0.8),
                                Color.yellow.opacity(0.3),
                                Color.clear
                            ],
                            center: .center,
                            startRadius: 30,
                            endRadius: 60
                        )
                    )
                    .frame(width: 120, height: 120)
                    .scaleEffect(animateFlowers ? 1.1 : 1.0)
                    .animation(.easeInOut(duration: 2).repeatForever(autoreverses: true), value: animateFlowers)
                
                // Pet photo
                AsyncImage(url: URL(string: memorial.petImageURL ?? "")) { image in
                    image
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                } placeholder: {
                    Image(systemName: "heart.circle.fill")
                        .font(.system(size: 40))
                        .foregroundColor(.pink)
                }
                .frame(width: 80, height: 80)
                .clipShape(Circle())
                .overlay(
                    Circle()
                        .stroke(Color.white, lineWidth: 3)
                )
                .shadow(color: .black.opacity(0.2), radius: 4, x: 0, y: 2)
            }
            
            // Pet Name and Dates
            VStack(spacing: Spacing.xs) {
                Text(memorial.petName)
                    .font(.petHeadline)
                    .foregroundColor(.black)

                if let dateOfPassing = memorial.dateOfPassing {
                    Text("🌈 \(dateOfPassing, style: .date)")
                        .font(.petCaption)
                        .foregroundColor(.black.opacity(0.6))
                }
            }
            
            // Floating Flowers
            HStack(spacing: Spacing.sm) {
                ForEach(["🌸", "🌺", "🌻", "🌷"], id: \.self) { flower in
                    Text(flower)
                        .font(.title3)
                        .offset(y: animateFlowers ? -5 : 5)
                        .animation(
                            .easeInOut(duration: 1.5)
                                .repeatForever(autoreverses: true)
                                .delay(Double.random(in: 0...1)),
                            value: animateFlowers
                        )
                }
            }
            
            // Tribute Count
            HStack {
                Image(systemName: "heart.fill")
                    .foregroundColor(.pink)
                Text("\(memorial.tributeCount) tributes")
                    .font(.petCaption)
                    .foregroundColor(.black.opacity(0.6))
            }
        }
        .padding(Spacing.lg)
        .background(
            RoundedRectangle(cornerRadius: CornerRadius.lg)
                .fill(Color.white.opacity(0.8))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }
}

struct CommunityMemorialCard: View {
    let memorial: MemorialGarden
    
    var body: some View {
        VStack(spacing: Spacing.sm) {
            AsyncImage(url: URL(string: memorial.petImageURL ?? "")) { image in
                image
                    .resizable()
                    .aspectRatio(contentMode: .fill)
            } placeholder: {
                Image(systemName: "heart.circle.fill")
                    .font(.system(size: 30))
                    .foregroundColor(.pink)
            }
            .frame(width: 60, height: 60)
            .clipShape(Circle())
            .overlay(
                Circle()
                    .stroke(Color.white, lineWidth: 2)
            )
            
            Text(memorial.petName)
                .font(.petCaption)
                .foregroundColor(.black)
                .lineLimit(1)
        }
        .padding(Spacing.md)
        .background(
            RoundedRectangle(cornerRadius: CornerRadius.md)
                .fill(Color.white.opacity(0.7))
                .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
        )
        .frame(width: 100)
    }
}

struct EmptyMemorialGardensView: View {
    var body: some View {
        VStack(spacing: Spacing.xl) {
            // Angel pet illustration
            ZStack {
                Circle()
                    .fill(
                        RadialGradient(
                            colors: [
                                Color.white.opacity(0.8),
                                Color.blue.opacity(0.2),
                                Color.clear
                            ],
                            center: .center,
                            startRadius: 50,
                            endRadius: 100
                        )
                    )
                    .frame(width: 200, height: 200)
                
                Text("😇")
                    .font(.system(size: 80))
            }
            
            VStack(spacing: Spacing.md) {
                Text("No Memorial Gardens Yet")
                    .font(.petTitle2)
                    .foregroundColor(.black)

                Text("Create a beautiful memorial garden to honor and remember your beloved companion")
                    .font(.petBody)
                    .foregroundColor(.black.opacity(0.7))
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, Spacing.xl)
            }
            
            // Floating elements
            HStack(spacing: Spacing.lg) {
                ForEach(["🌸", "🦋", "☁️", "🌈", "💫"], id: \.self) { element in
                    Text(element)
                        .font(.title2)
                        .opacity(0.6)
                }
            }
        }
        .padding(Spacing.xl)
    }
}

struct CreateMemorialView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var memorialService = MemorialGardenService.shared
    @State private var selectedPet: Pet?
    @State private var memorialMessage = ""
    @State private var isPublic = true
    @State private var selectedTheme: MemorialTheme = .peaceful
    @State private var showingPetPicker = false
    
    var body: some View {
        NavigationView {
            Form {
                Section("Pet Selection") {
                    Button(action: {
                        showingPetPicker = true
                    }) {
                        HStack {
                            if let pet = selectedPet {
                                Text(pet.name)
                                    .foregroundColor(.primary)
                                Spacer()
                                Text("Selected")
                                    .font(.caption)
                                    .foregroundColor(.green)
                            } else {
                                Text("Select Pet")
                                    .foregroundColor(.blue)
                                Spacer()
                                Image(systemName: "chevron.right")
                                    .foregroundColor(.secondary)
                            }
                        }
                    }
                }
                
                Section("Memorial Message") {
                    TextEditor(text: $memorialMessage)
                        .frame(minHeight: 100)
                }
                
                Section("Theme") {
                    Picker("Memorial Theme", selection: $selectedTheme) {
                        ForEach(MemorialTheme.allCases, id: \.self) { theme in
                            Text(theme.displayName).tag(theme)
                        }
                    }
                    .pickerStyle(SegmentedPickerStyle())
                }
                
                Section("Visibility") {
                    Toggle("Share with Community", isOn: $isPublic)
                }
            }
            .navigationTitle("Create Memorial")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Create") {
                        Task {
                            await createMemorial()
                        }
                    }
                    .disabled(selectedPet == nil || memorialMessage.isEmpty)
                }
            }
        }
        .sheet(isPresented: $showingPetPicker) {
            PetPickerView(selectedPet: $selectedPet)
        }
    }
    
    private func createMemorial() async {
        guard let pet = selectedPet else { return }
        
        await memorialService.createMemorialGarden(
            for: pet,
            message: memorialMessage,
            theme: selectedTheme,
            isPublic: isPublic
        )
        
        dismiss()
    }
}

struct PetPickerView: View {
    @Environment(\.dismiss) private var dismiss
    @Binding var selectedPet: Pet?
    
    // Mock pets for demo - in real app, fetch from SwiftData
    private let deceasedPets: [Pet] = []
    
    var body: some View {
        NavigationView {
            List(deceasedPets) { pet in
                HStack {
                    AsyncImage(url: URL(string: pet.profileImageURL ?? "")) { image in
                        image
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                    } placeholder: {
                        Image(systemName: "heart.circle.fill")
                            .foregroundColor(.pink)
                    }
                    .frame(width: 50, height: 50)
                    .clipShape(Circle())
                    
                    VStack(alignment: .leading) {
                        Text(pet.name)
                            .font(.headline)
                        Text(pet.breed)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()
                    
                    if selectedPet?.id == pet.id {
                        Image(systemName: "checkmark.circle.fill")
                            .foregroundColor(.green)
                    }
                }
                .contentShape(Rectangle())
                .onTapGesture {
                    selectedPet = pet
                    dismiss()
                }
            }
            .navigationTitle("Select Pet")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
            }
        }
    }
}

#Preview {
    MemorialGardenView()
}
