//
//  EnhancedAIChatView.swift
//  PetCapsule
//
//  🚀 PHASE 1: Advanced AI Chat Interface with Voice & Image Support
//  🤖 Production-ready chat with real Gemini Flash 2.0 integration
//

import SwiftUI
import PhotosUI

struct EnhancedAIChatView: View {
    let agent: AIAgent
    @EnvironmentObject private var realDataService: RealDataService
    @EnvironmentObject private var subscriptionService: SubscriptionService
    @Environment(\.dismiss) private var dismiss

    @StateObject private var aiService = EnhancedAIAgentService.shared
    @State private var currentMessage = ""
    @State private var selectedPet: Pet?
    @State private var showPetSelector = false
    @State private var showImagePicker = false
    @State private var selectedImage: UIImage?
    @State private var showConversationHistory = false
    @State private var animateMessages = false



    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Header
                headerView

                // Messages
                messagesView

                // Input Area
                inputView
            }
            .navigationBarHidden(true)
            .background(
                LinearGradient(
                    colors: agent.gradientColors.map { Color(hex: $0) },
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .opacity(0.1)
            )
        }
        .sheet(isPresented: $showPetSelector) {
            petSelectorView
        }
        .sheet(isPresented: $showImagePicker) {
            ImagePicker(selectedImage: $selectedImage)
        }
        .sheet(isPresented: $showConversationHistory) {
            ConversationHistoryView()
        }
        .onAppear {
            if let firstPet = realDataService.pets.first {
                selectedPet = firstPet
                aiService.updatePetContext(pet: firstPet, for: agent)
            }
        }
    }

    // MARK: - Header View

    private var headerView: some View {
        HStack {
            Button(action: { dismiss() }) {
                Image(systemName: "chevron.left")
                    .font(.title2)
                    .foregroundColor(.primary)
            }

            VStack(alignment: .leading, spacing: 2) {
                HStack {
                    Text(agent.iconName)
                        .font(.title2)

                    Text(agent.name)
                        .font(.headline)
                        .fontWeight(.bold)

                    if agent.isPremium {
                        Image(systemName: "crown.fill")
                            .foregroundColor(.yellow)
                            .font(.caption)
                    }
                }

                Text(agent.description)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(1)
            }

            Spacer()

            // Action Buttons
            HStack(spacing: 12) {
                // Conversation History
                Button(action: { showConversationHistory = true }) {
                    Image(systemName: "clock.arrow.circlepath")
                        .font(.title3)
                        .foregroundColor(.primary)
                }

                // Pet Selector
                Button(action: { showPetSelector = true }) {
                    if let pet = selectedPet {
                        AsyncImage(url: URL(string: pet.profileImageURL ?? "")) { image in
                            image
                                .resizable()
                                .aspectRatio(contentMode: .fill)
                        } placeholder: {
                            Image(systemName: "pawprint.fill")
                                .foregroundColor(.primary)
                        }
                        .frame(width: 30, height: 30)
                        .clipShape(Circle())
                    } else {
                        Image(systemName: "pawprint.fill")
                            .font(.title3)
                            .foregroundColor(.primary)
                    }
                }

                // Clear Conversation
                Button(action: { aiService.clearConversation(for: agent) }) {
                    Image(systemName: "trash")
                        .font(.title3)
                        .foregroundColor(.red)
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
    }

    // MARK: - Messages View

    private var messagesView: some View {
        ScrollViewReader { proxy in
            ScrollView {
                LazyVStack(spacing: 12) {
                    if let messages = aiService.conversationHistory[agent.id.uuidString] {
                        ForEach(messages) { message in
                            MessageBubbleView(
                                message: message,
                                agent: agent,
                                onTranslate: { translateMessage(message) }
                            )
                            .scaleEffect(animateMessages ? 1 : 0.8)
                            .opacity(animateMessages ? 1 : 0)
                            .animation(
                                .spring(response: 0.6, dampingFraction: 0.8)
                                .delay(Double(messages.firstIndex(where: { $0.id == message.id }) ?? 0) * 0.1),
                                value: animateMessages
                            )
                        }
                    } else {
                        // Welcome Message
                        VStack(spacing: 16) {
                            Text(agent.iconName)
                                .font(.system(size: 60))

                            Text("Hello! I'm \(agent.name)")
                                .font(.title2)
                                .fontWeight(.bold)

                            Text(agent.description)
                                .font(.body)
                                .foregroundColor(.secondary)
                                .multilineTextAlignment(.center)

                            VStack(alignment: .leading, spacing: 8) {
                                Text("I can help you with:")
                                    .font(.headline)

                                ForEach(agent.specialties, id: \.self) { specialty in
                                    HStack {
                                        Image(systemName: "checkmark.circle.fill")
                                            .foregroundColor(.green)
                                        Text(specialty)
                                    }
                                }
                            }
                            .padding()
                            .background(Color(.systemGray6))
                            .cornerRadius(12)
                        }
                        .padding()
                    }

                    if aiService.isLoading {
                        TypingIndicatorView()
                    }
                }
                .padding()
            }
            .onAppear {
                animateMessages = true
            }
            .onChange(of: aiService.conversationHistory[agent.id.uuidString]?.count) {
                if let lastMessage = aiService.conversationHistory[agent.id.uuidString]?.last {
                    withAnimation {
                        proxy.scrollTo(lastMessage.id, anchor: .bottom)
                    }
                }
            }
        }
    }

    // MARK: - Input View

    private var inputView: some View {
        VStack(spacing: 12) {
            // Image Preview
            if let image = selectedImage {
                HStack {
                    Image(uiImage: image)
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(height: 60)
                        .cornerRadius(8)

                    Spacer()

                    Button("Remove") {
                        selectedImage = nil
                    }
                    .foregroundColor(.red)
                }
                .padding(.horizontal)
            }

            // Input Row
            HStack(spacing: 12) {
                // Voice Button
                Button(action: toggleVoiceRecognition) {
                    Image(systemName: aiService.isListening ? "mic.fill" : "mic")
                        .font(.title2)
                        .foregroundColor(aiService.isListening ? .red : .primary)
                        .scaleEffect(aiService.isListening ? 1.2 : 1.0)
                        .animation(.easeInOut(duration: 0.2), value: aiService.isListening)
                }
                .disabled(aiService.isLoading)

                // Image Button
                Button(action: { showImagePicker = true }) {
                    Image(systemName: "camera")
                        .font(.title2)
                        .foregroundColor(.primary)
                }
                .disabled(aiService.isLoading)

                // Text Input
                TextField("Ask \(agent.name) anything...", text: $currentMessage, axis: .vertical)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .lineLimit(1...4)

                // Send Button
                Button(action: sendMessage) {
                    Image(systemName: "arrow.up.circle.fill")
                        .font(.title2)
                        .foregroundColor(currentMessage.isEmpty && selectedImage == nil ? .gray : .blue)
                }
                .disabled(currentMessage.isEmpty && selectedImage == nil || aiService.isLoading)
            }
            .padding()
        }
        .background(Color(.systemBackground))
        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: -1)
    }

    // MARK: - Actions

    private func sendMessage() {
        guard !currentMessage.isEmpty || selectedImage != nil else { return }

        let messageText = currentMessage.isEmpty ? "Please analyze this image" : currentMessage
        currentMessage = ""

        Task {
            let _ = await aiService.sendMessage(
                to: agent,
                message: messageText,
                pet: selectedPet,
                includeImage: selectedImage
            )
            selectedImage = nil
        }
    }

    private func toggleVoiceRecognition() {
        if aiService.isListening {
            aiService.stopVoiceRecognition()
        } else {
            Task {
                await aiService.startVoiceRecognition(for: agent)
            }
        }
    }

    private func translateMessage(_ message: ChatMessage) {
        guard !message.isFromUser else { return }

        Task {
            // Translation functionality removed since we only support English
            // Future enhancement: Add translation support if needed
        }
    }

    // MARK: - Sheet Views

    private var petSelectorView: some View {
        NavigationView {
            List(realDataService.pets) { pet in
                Button(action: {
                    selectedPet = pet
                    aiService.updatePetContext(pet: pet, for: agent)
                    showPetSelector = false
                }) {
                    HStack {
                        AsyncImage(url: URL(string: pet.profileImageURL ?? "")) { image in
                            image
                                .resizable()
                                .aspectRatio(contentMode: .fill)
                        } placeholder: {
                            Image(systemName: "pawprint.fill")
                                .foregroundColor(.gray)
                        }
                        .frame(width: 40, height: 40)
                        .clipShape(Circle())

                        VStack(alignment: .leading) {
                            Text(pet.name)
                                .font(.headline)
                            Text("\(pet.species.capitalized) • \(pet.breed)")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }

                        Spacer()

                        if selectedPet?.id == pet.id {
                            Image(systemName: "checkmark.circle.fill")
                                .foregroundColor(.blue)
                        }
                    }
                }
                .buttonStyle(PlainButtonStyle())
            }
            .navigationTitle("Select Pet")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        showPetSelector = false
                    }
                }
            }
        }
    }


}

// MARK: - Supporting Views

struct MessageBubbleView: View {
    let message: ChatMessage
    let agent: AIAgent
    let onTranslate: () -> Void

    var body: some View {
        HStack {
            if message.isFromUser {
                Spacer()
            }

            VStack(alignment: message.isFromUser ? .trailing : .leading, spacing: 4) {
                Text(message.content)
                    .padding(12)
                    .background(
                        message.isFromUser ?
                        Color.blue :
                        Color(.systemGray5)
                    )
                    .foregroundColor(
                        message.isFromUser ? .white : .primary
                    )
                    .cornerRadius(16)
                    .contextMenu {
                        if !message.isFromUser {
                            Button("Translate", action: onTranslate)
                        }
                        Button("Copy") {
                            UIPasteboard.general.string = message.content
                        }
                    }

                Text(message.timestamp.formatted(date: .omitted, time: .shortened))
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }

            if !message.isFromUser {
                Spacer()
            }
        }
    }
}

struct TypingIndicatorView: View {
    @State private var animating = false

    var body: some View {
        HStack {
            HStack(spacing: 4) {
                ForEach(0..<3) { index in
                    Circle()
                        .fill(Color.gray)
                        .frame(width: 8, height: 8)
                        .scaleEffect(animating ? 1.2 : 0.8)
                        .animation(
                            .easeInOut(duration: 0.6)
                            .repeatForever()
                            .delay(Double(index) * 0.2),
                            value: animating
                        )
                }
            }
            .padding(12)
            .background(Color(.systemGray5))
            .cornerRadius(16)

            Spacer()
        }
        .onAppear {
            animating = true
        }
    }
}

struct ImagePicker: UIViewControllerRepresentable {
    @Binding var selectedImage: UIImage?
    @Environment(\.dismiss) private var dismiss

    func makeUIViewController(context: Context) -> UIImagePickerController {
        let picker = UIImagePickerController()
        picker.delegate = context.coordinator
        picker.sourceType = .photoLibrary
        return picker
    }

    func updateUIViewController(_ uiViewController: UIImagePickerController, context: Context) {}

    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }

    class Coordinator: NSObject, UIImagePickerControllerDelegate, UINavigationControllerDelegate {
        let parent: ImagePicker

        init(_ parent: ImagePicker) {
            self.parent = parent
        }

        func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
            if let image = info[.originalImage] as? UIImage {
                parent.selectedImage = image
            }
            parent.dismiss()
        }

        func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
            parent.dismiss()
        }
    }
}

// MARK: - Color Extension

extension Color {
    init(hex: String) {
        let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            (a, r, g, b) = (1, 1, 1, 0)
        }

        self.init(
            .sRGB,
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue:  Double(b) / 255,
            opacity: Double(a) / 255
        )
    }
}
