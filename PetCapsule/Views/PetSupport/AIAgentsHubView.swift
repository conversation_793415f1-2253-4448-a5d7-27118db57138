//
//  AIAgentsHubView.swift
//  PetCapsule
//
//  Complete AI Agents Hub Interface
//  Comprehensive hub for all AI agents with enhanced navigation
//

import SwiftUI

struct AIAgentsHubView: View {
    @StateObject private var aiService = EnhancedAIAgentService.shared
    @State private var selectedAgent: AIAgent?
    @State private var showingPetMaster = false
    @State private var showingConversationHistory = false
    @State private var searchText = ""
    @State private var selectedCategory: AgentCategory = .all
    @State private var animateCards = false
    
    enum AgentCategory: String, CaseIterable {
        case all = "All Agents"
        case health = "Health & Care"
        case lifestyle = "Lifestyle"
        case emergency = "Emergency"
        case premium = "Premium"
        
        var icon: String {
            switch self {
            case .all: return "sparkles"
            case .health: return "heart.fill"
            case .lifestyle: return "house.fill"
            case .emergency: return "cross.fill"
            case .premium: return "crown.fill"
            }
        }
        
        var color: Color {
            switch self {
            case .all: return .purple
            case .health: return .red
            case .lifestyle: return .green
            case .emergency: return .orange
            case .premium: return .yellow
            }
        }
    }
    
    var filteredAgents: [AIAgent] {
        let agents = selectedCategory == .all ? aiService.availableAgents : 
                    aiService.availableAgents.filter { categorizeAgent($0) == selectedCategory }
        
        if searchText.isEmpty {
            return agents
        } else {
            return agents.filter { agent in
                agent.name.localizedCaseInsensitiveContains(searchText) ||
                agent.description.localizedCaseInsensitiveContains(searchText) ||
                agent.specialty.localizedCaseInsensitiveContains(searchText)
            }
        }
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Header Section
                headerSection
                
                // Category Selector
                categorySelector
                
                // Search Bar
                searchBar
                
                // Agents Grid
                agentsGrid
            }
            .navigationBarHidden(true)
            .background(Color(.systemGroupedBackground))
            .onAppear {
                Task {
                    await aiService.refreshUserPets()
                }
                withAnimation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.2)) {
                    animateCards = true
                }
            }
        }
        .sheet(item: $selectedAgent) { agent in
            EnhancedAIChatView(agent: agent)
        }
        .sheet(isPresented: $showingPetMaster) {
            PetMasterChatView()
        }
        .sheet(isPresented: $showingConversationHistory) {
            ConversationHistoryView()
        }
    }
    
    // MARK: - Header Section
    
    private var headerSection: some View {
        VStack(spacing: 16) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("AI Agents Hub")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)
                    
                    Text("Your intelligent pet care assistants")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                Spacer()

                HStack(spacing: 12) {
                    // Conversation History Button
                    Button(action: { showingConversationHistory = true }) {
                        VStack(spacing: 4) {
                            Image(systemName: "clock.arrow.circlepath")
                                .font(.title3)
                                .foregroundColor(.white)
                            Text("History")
                                .font(.caption2)
                                .fontWeight(.medium)
                                .foregroundColor(.white)
                        }
                        .frame(width: 70, height: 60)
                        .background(
                            LinearGradient(
                                colors: [.orange, .red],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .cornerRadius(10)
                        .shadow(color: .black.opacity(0.2), radius: 4, x: 0, y: 2)
                    }

                    // Pet Master Quick Access
                    Button(action: { showingPetMaster = true }) {
                        VStack(spacing: 4) {
                            Image(systemName: "brain.head.profile")
                                .font(.title2)
                                .foregroundColor(.white)
                            Text("Pet Master")
                                .font(.caption)
                                .fontWeight(.medium)
                                .foregroundColor(.white)
                        }
                        .frame(width: 80, height: 70)
                        .background(
                            LinearGradient(
                                colors: [.purple, .blue],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .cornerRadius(12)
                        .shadow(color: .black.opacity(0.2), radius: 4, x: 0, y: 2)
                    }
                    .accessibilityIdentifier("🎯 Pet Master")
                }
            }
            
            // Stats Row
            HStack(spacing: 20) {
                AgentStatCard(title: "Available Agents", value: "\(aiService.availableAgents.count)", icon: "person.3.fill", color: .blue)
                AgentStatCard(title: "Active Chats", value: "24/7", icon: "message.fill", color: .green)
                AgentStatCard(title: "Response Time", value: "< 1s", icon: "clock.fill", color: .orange)
            }
        }
        .padding(.horizontal, 20)
        .padding(.top, 10)
        .padding(.bottom, 20)
        .background(Color(.systemBackground))
        .scaleEffect(animateCards ? 1.0 : 0.9)
        .opacity(animateCards ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8), value: animateCards)
    }
    
    // MARK: - Category Selector
    
    private var categorySelector: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                ForEach(AgentCategory.allCases, id: \.self) { category in
                    Button(action: {
                        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                            selectedCategory = category
                        }
                    }) {
                        HStack(spacing: 8) {
                            Image(systemName: category.icon)
                                .font(.system(size: 14, weight: .semibold))
                            
                            Text(category.rawValue)
                                .font(.system(size: 14, weight: .semibold))
                        }
                        .foregroundColor(selectedCategory == category ? .white : category.color)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 10)
                        .background(
                            Capsule()
                                .fill(selectedCategory == category ? category.color : category.color.opacity(0.1))
                        )
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
            .padding(.horizontal, 20)
        }
        .padding(.bottom, 16)
    }
    
    // MARK: - Search Bar
    
    private var searchBar: some View {
        HStack {
            Image(systemName: "magnifyingglass")
                .foregroundColor(.secondary)
            TextField("Search agents...", text: $searchText)
                .textFieldStyle(PlainTextFieldStyle())
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(Color(.systemBackground))
        .cornerRadius(10)
        .padding(.horizontal, 20)
        .padding(.bottom, 20)
    }
    
    // MARK: - Agents Grid
    
    private var agentsGrid: some View {
        ScrollView {
            LazyVGrid(columns: [
                GridItem(.flexible(), spacing: 16),
                GridItem(.flexible(), spacing: 16)
            ], spacing: 20) {
                ForEach(Array(filteredAgents.enumerated()), id: \.element.id) { index, agent in
                    EnhancedAgentCard(agent: agent) {
                        selectedAgent = agent
                    }
                    .scaleEffect(animateCards ? 1.0 : 0.8)
                    .opacity(animateCards ? 1.0 : 0.0)
                    .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(Double(index) * 0.1), value: animateCards)
                }
            }
            .padding(.horizontal, 20)
            .padding(.bottom, 20)
        }
    }
    
    // MARK: - Helper Functions
    
    private func categorizeAgent(_ agent: AIAgent) -> AgentCategory {
        let name = agent.name.lowercased()
        let specialty = agent.specialty.lowercased()
        
        if name.contains("nutrition") || name.contains("health") || name.contains("wellness") || name.contains("psychologist") ||
           specialty.contains("health") || specialty.contains("nutrition") || specialty.contains("wellness") {
            return .health
        } else if name.contains("emergency") || specialty.contains("emergency") {
            return .emergency
        } else if agent.isPremium {
            return .premium
        } else {
            return .lifestyle
        }
    }
}

// MARK: - Supporting Views

struct AgentStatCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 4) {
            Image(systemName: icon)
                .font(.system(size: 16, weight: .semibold))
                .foregroundColor(color)
            
            Text(value)
                .font(.system(size: 16, weight: .bold))
                .foregroundColor(.primary)
            
            Text(title)
                .font(.system(size: 10, weight: .medium))
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 12)
        .background(Color(.systemGray6))
        .cornerRadius(8)
    }
}

struct EnhancedAgentCard: View {
    let agent: AIAgent
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 12) {
                // Agent Avatar
                ZStack {
                    Circle()
                        .fill(LinearGradient(
                            gradient: Gradient(colors: agent.gradientColors.map { Color(hex: $0) }),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ))
                        .frame(width: 60, height: 60)
                    
                    Text(agent.iconName)
                        .font(.system(size: 28))
                }
                
                // Agent Info
                VStack(spacing: 4) {
                    Text(agent.name)
                        .font(.system(size: 16, weight: .bold))
                        .foregroundColor(.primary)
                        .multilineTextAlignment(.center)
                        .lineLimit(2)
                    
                    Text(agent.specialty)
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                        .lineLimit(2)
                }
                
                // Status Indicator
                HStack(spacing: 4) {
                    Circle()
                        .fill(Color.green)
                        .frame(width: 6, height: 6)
                    
                    Text("Available")
                        .font(.system(size: 10, weight: .semibold))
                        .foregroundColor(.green)
                }
            }
            .padding(16)
            .frame(height: 160)
            .background(Color(.systemBackground))
            .cornerRadius(16)
            .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

#Preview {
    AIAgentsHubView()
}
