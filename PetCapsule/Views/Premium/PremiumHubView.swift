//
//  PremiumHubView.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI

struct PremiumHubView: View {
    @EnvironmentObject var realDataService: RealDataService
    @State private var selectedTab = 0
    @State private var showSubscriptionPlans = false
    @State private var animateCards = false

    var currentSubscriptionTier: String {
        realDataService.pets.first?.subscriptionTier ?? "free"
    }

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Subscription Status Header
                subscriptionStatusHeader

                // Tab Selector
                tabSelectorSection

                // Content based on selected tab
                TabView(selection: $selectedTab) {
                    featuresTab
                        .tag(0)

                    marketplaceTab
                        .tag(1)

                    analyticsTab
                        .tag(2)

                    investorTab
                        .tag(3)
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
            }
            .navigationTitle("Premium Hub")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Upgrade") {
                        showSubscriptionPlans = true
                    }
                    .font(.petSubheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 8)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(Color.purple)
                    )
                }
            }
            .sheet(isPresented: $showSubscriptionPlans) {
                SubscriptionView()
            }
            .onAppear {
                withAnimation(.spring(response: 0.8, dampingFraction: 0.8)) {
                    animateCards = true
                }
            }
        }
    }

    // MARK: - Subscription Status Header

    private var subscriptionStatusHeader: some View {
        VStack(spacing: 16) {
            HStack {
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Image(systemName: "crown.fill")
                            .font(.title2)
                            .foregroundColor(currentSubscriptionTier == "free" ? .gray : .yellow)

                        Text(currentSubscriptionTier.capitalized)
                            .font(.petTitle2)
                            .fontWeight(.bold)
                            .foregroundColor(.primary)
                    }

                    Text(currentSubscriptionTier == "free" ?
                         "Unlock premium features for the ultimate pet experience" :
                         "Thank you for being a premium member!")
                        .font(.petSubheadline)
                        .foregroundColor(.secondary)
                }

                Spacer()

                if currentSubscriptionTier != "free" {
                    VStack {
                        Text("$\(subscriptionPrice)")
                            .font(.petTitle3)
                            .fontWeight(.bold)
                            .foregroundColor(.purple)

                        Text("/month")
                            .font(.petCaption)
                            .foregroundColor(.secondary)
                    }
                }
            }

            // Feature highlights
            if currentSubscriptionTier == "free" {
                HStack(spacing: 16) {
                    featureHighlight(icon: "brain.head.profile.fill", title: "AI Health", isLocked: true)
                    featureHighlight(icon: "video.fill", title: "Video Montages", isLocked: true)
                    featureHighlight(icon: "chart.line.uptrend.xyaxis", title: "Analytics", isLocked: true)
                }
            } else {
                HStack(spacing: 16) {
                    featureHighlight(icon: "checkmark.circle.fill", title: "AI Health", isLocked: false)
                    featureHighlight(icon: "checkmark.circle.fill", title: "Video Montages", isLocked: false)
                    featureHighlight(icon: "checkmark.circle.fill", title: "Analytics", isLocked: false)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(
                    LinearGradient(
                        colors: currentSubscriptionTier == "free" ?
                            [Color(.systemGray6), Color(.systemGray5)] :
                            [Color.purple.opacity(0.1), Color.blue.opacity(0.1)],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
        )
        .padding()
        .scaleEffect(animateCards ? 1.0 : 0.9)
        .opacity(animateCards ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.1), value: animateCards)
    }

    private func featureHighlight(icon: String, title: String, isLocked: Bool) -> some View {
        VStack(spacing: 6) {
            ZStack {
                Image(systemName: icon)
                    .font(.title3)
                    .foregroundColor(isLocked ? .gray : .green)

                if isLocked {
                    Image(systemName: "lock.fill")
                        .font(.caption2)
                        .foregroundColor(.white)
                        .padding(2)
                        .background(Circle().fill(Color.gray))
                        .offset(x: 8, y: -8)
                }
            }

            Text(title)
                .font(.petCaption)
                .fontWeight(.medium)
                .foregroundColor(isLocked ? .gray : .primary)
        }
        .frame(maxWidth: .infinity)
    }

    private var subscriptionPrice: String {
        switch currentSubscriptionTier {
        case "premium": return "19.99"
        case "family": return "39.99"
        case "professional": return "99.99"
        default: return "0"
        }
    }

    // MARK: - Tab Selector Section

    private var tabSelectorSection: some View {
        HStack(spacing: 0) {
            tabButton(title: "Features", icon: "star.fill", index: 0)
            tabButton(title: "Marketplace", icon: "cart.fill", index: 1)
            tabButton(title: "Analytics", icon: "chart.bar.fill", index: 2)
            tabButton(title: "Investor", icon: "dollarsign.circle.fill", index: 3)
        }
        .padding(.horizontal)
        .background(Color(.systemBackground))
    }

    private func tabButton(title: String, icon: String, index: Int) -> some View {
        Button(action: { selectedTab = index }) {
            VStack(spacing: 6) {
                Image(systemName: icon)
                    .font(.caption)
                    .foregroundColor(selectedTab == index ? .purple : .secondary)

                Text(title)
                    .font(.petCaption)
                    .fontWeight(.medium)
                    .foregroundColor(selectedTab == index ? .purple : .secondary)
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(selectedTab == index ? Color.purple.opacity(0.1) : Color.clear)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }

    // MARK: - Features Tab

    private var featuresTab: some View {
        ScrollView {
            LazyVStack(spacing: 20) {
                // Premium Features Grid
                premiumFeaturesGrid

                // Subscription Plans
                subscriptionPlansSection

                // Feature Comparison
                featureComparisonSection
            }
            .padding()
            .padding(.bottom, 100)
        }
    }

    private var premiumFeaturesGrid: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Premium Features")
                .font(.petTitle2)
                .fontWeight(.bold)

            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                premiumFeatureCard(
                    icon: "brain.head.profile.fill",
                    title: "AI Health Analysis",
                    description: "Advanced health insights and predictions",
                    isUnlocked: currentSubscriptionTier != "free",
                    color: .purple
                )

                premiumFeatureCard(
                    icon: "video.fill",
                    title: "Video Montages",
                    description: "Professional pet video creation",
                    isUnlocked: currentSubscriptionTier != "free",
                    color: .blue
                )

                premiumFeatureCard(
                    icon: "chart.line.uptrend.xyaxis",
                    title: "Advanced Analytics",
                    description: "Detailed health and behavior tracking",
                    isUnlocked: currentSubscriptionTier != "free",
                    color: .green
                )

                premiumFeatureCard(
                    icon: "stethoscope",
                    title: "Vet Chat",
                    description: "24/7 veterinarian consultation",
                    isUnlocked: currentSubscriptionTier == "professional",
                    color: .orange
                )

                premiumFeatureCard(
                    icon: "leaf.fill",
                    title: "Nutrition Planning",
                    description: "AI-powered meal planning",
                    isUnlocked: currentSubscriptionTier != "free",
                    color: .green
                )

                premiumFeatureCard(
                    icon: "heart.circle.fill",
                    title: "Memorial Gardens",
                    description: "Beautiful tribute spaces",
                    isUnlocked: currentSubscriptionTier != "free",
                    color: .pink
                )
            }
        }
        .scaleEffect(animateCards ? 1.0 : 0.9)
        .opacity(animateCards ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.2), value: animateCards)
    }

    private func premiumFeatureCard(icon: String, title: String, description: String, isUnlocked: Bool, color: Color) -> some View {
        VStack(spacing: 12) {
            ZStack {
                Image(systemName: icon)
                    .font(.title)
                    .foregroundColor(isUnlocked ? color : .gray)

                if !isUnlocked {
                    Image(systemName: "lock.fill")
                        .font(.caption)
                        .foregroundColor(.white)
                        .padding(4)
                        .background(Circle().fill(Color.gray))
                        .offset(x: 15, y: -15)
                }
            }

            VStack(spacing: 4) {
                Text(title)
                    .font(.petSubheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
                    .multilineTextAlignment(.center)

                Text(description)
                    .font(.petCaption)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .lineLimit(2)
            }

            if !isUnlocked {
                Button("Unlock") {
                    showSubscriptionPlans = true
                }
                .font(.petCaption)
                .fontWeight(.semibold)
                .foregroundColor(.white)
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(
                    RoundedRectangle(cornerRadius: 6)
                        .fill(color)
                )
            } else {
                HStack {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                    Text("Unlocked")
                        .font(.petCaption)
                        .foregroundColor(.green)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(isUnlocked ? color.opacity(0.1) : Color(.systemGray6))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(isUnlocked ? color.opacity(0.3) : Color.gray.opacity(0.3), lineWidth: 1)
                )
        )
    }

    private var subscriptionPlansSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Subscription Plans")
                .font(.petTitle2)
                .fontWeight(.bold)

            VStack(spacing: 12) {
                subscriptionPlanRow(
                    name: "Premium",
                    price: "$19.99/month",
                    features: ["AI Health Analysis", "Video Montages", "Advanced Analytics"],
                    isCurrentPlan: currentSubscriptionTier == "premium",
                    color: .purple
                )

                subscriptionPlanRow(
                    name: "Family",
                    price: "$39.99/month",
                    features: ["Everything in Premium", "Up to 5 pets", "Family sharing"],
                    isCurrentPlan: currentSubscriptionTier == "family",
                    color: .blue
                )

                subscriptionPlanRow(
                    name: "Professional",
                    price: "$99.99/month",
                    features: ["Everything in Family", "Vet Chat", "Business tools"],
                    isCurrentPlan: currentSubscriptionTier == "professional",
                    color: .orange
                )
            }
        }
        .scaleEffect(animateCards ? 1.0 : 0.9)
        .opacity(animateCards ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.3), value: animateCards)
    }

    private func subscriptionPlanRow(name: String, price: String, features: [String], isCurrentPlan: Bool, color: Color) -> some View {
        HStack(spacing: 16) {
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text(name)
                        .font(.petSubheadline)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)

                    if isCurrentPlan {
                        Text("CURRENT")
                            .font(.caption2)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(
                                RoundedRectangle(cornerRadius: 4)
                                    .fill(color)
                            )
                    }

                    Spacer()

                    Text(price)
                        .font(.petSubheadline)
                        .fontWeight(.bold)
                        .foregroundColor(color)
                }

                VStack(alignment: .leading, spacing: 4) {
                    ForEach(features, id: \.self) { feature in
                        HStack {
                            Image(systemName: "checkmark.circle.fill")
                                .font(.caption)
                                .foregroundColor(.green)

                            Text(feature)
                                .font(.petCaption)
                                .foregroundColor(.secondary)
                        }
                    }
                }
            }

            if !isCurrentPlan {
                Button("Select") {
                    showSubscriptionPlans = true
                }
                .font(.petCaption)
                .fontWeight(.semibold)
                .foregroundColor(.white)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(color)
                )
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(isCurrentPlan ? color.opacity(0.1) : Color(.systemGray6))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(isCurrentPlan ? color : Color.clear, lineWidth: 2)
                )
        )
    }

    private var featureComparisonSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Feature Comparison")
                .font(.petTitle2)
                .fontWeight(.bold)

            VStack(spacing: 0) {
                // Header
                HStack {
                    Text("Feature")
                        .font(.petSubheadline)
                        .fontWeight(.semibold)
                        .frame(maxWidth: .infinity, alignment: .leading)

                    Text("Free")
                        .font(.petCaption)
                        .fontWeight(.semibold)
                        .frame(width: 50)

                    Text("Premium")
                        .font(.petCaption)
                        .fontWeight(.semibold)
                        .frame(width: 60)

                    Text("Family")
                        .font(.petCaption)
                        .fontWeight(.semibold)
                        .frame(width: 50)

                    Text("Pro")
                        .font(.petCaption)
                        .fontWeight(.semibold)
                        .frame(width: 40)
                }
                .padding()
                .background(Color(.systemGray6))

                // Feature rows
                let features = [
                    ("Basic pet profiles", [true, true, true, true]),
                    ("Photo storage", [true, true, true, true]),
                    ("AI health analysis", [false, true, true, true]),
                    ("Video montages", [false, true, true, true]),
                    ("Advanced analytics", [false, true, true, true]),
                    ("Multiple pets", [false, false, true, true]),
                    ("Vet chat", [false, false, false, true])
                ]

                ForEach(Array(features.enumerated()), id: \.offset) { index, feature in
                    HStack {
                        Text(feature.0)
                            .font(.petCaption)
                            .frame(maxWidth: .infinity, alignment: .leading)

                        ForEach(0..<4) { planIndex in
                            Image(systemName: feature.1[planIndex] ? "checkmark.circle.fill" : "xmark.circle.fill")
                                .font(.caption)
                                .foregroundColor(feature.1[planIndex] ? .green : .red)
                                .frame(width: planIndex == 0 ? 50 : planIndex == 1 ? 60 : planIndex == 2 ? 50 : 40)
                        }
                    }
                    .padding(.horizontal)
                    .padding(.vertical, 8)
                    .background(index % 2 == 0 ? Color.clear : Color(.systemGray6).opacity(0.5))
                }
            }
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemBackground))
                    .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
            )
        }
        .scaleEffect(animateCards ? 1.0 : 0.9)
        .opacity(animateCards ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.4), value: animateCards)
    }

    // MARK: - Marketplace Tab

    private var marketplaceTab: some View {
        ScrollView {
            LazyVStack(spacing: 20) {
                // Marketplace Header
                VStack(alignment: .leading, spacing: 16) {
                    HStack {
                        Image(systemName: "cart.fill")
                            .font(.title2)
                            .foregroundColor(.blue)

                        Text("Pet Marketplace")
                            .font(.petTitle2)
                            .fontWeight(.bold)

                        Spacer()

                        Button("View All") {
                            // Navigate to full marketplace
                        }
                        .font(.petSubheadline)
                        .foregroundColor(.blue)
                    }

                    Text("Premium products and services for your pets")
                        .font(.petSubheadline)
                        .foregroundColor(.secondary)
                }
                .padding()

                // Product Categories
                LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                    marketplaceCategory(
                        icon: "book.fill",
                        title: "Memory Books",
                        description: "Custom photo books",
                        price: "From $29.99",
                        color: .orange
                    )

                    marketplaceCategory(
                        icon: "paintbrush.fill",
                        title: "Custom Portraits",
                        description: "Professional pet art",
                        price: "From $49.99",
                        color: .purple
                    )

                    marketplaceCategory(
                        icon: "heart.circle.fill",
                        title: "Memorial Items",
                        description: "Tribute keepsakes",
                        price: "From $19.99",
                        color: .pink
                    )

                    marketplaceCategory(
                        icon: "stethoscope",
                        title: "Pet Care",
                        description: "Health products",
                        price: "From $9.99",
                        color: .green
                    )
                }
                .padding(.horizontal)

                // Featured Products
                VStack(alignment: .leading, spacing: 16) {
                    Text("Featured Products")
                        .font(.petTitle3)
                        .fontWeight(.bold)
                        .padding(.horizontal)

                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: 16) {
                            ForEach(0..<5, id: \.self) { index in
                                featuredProductCard(index: index)
                            }
                        }
                        .padding(.horizontal)
                    }
                }
            }
            .padding(.bottom, 100)
        }
    }

    private func marketplaceCategory(icon: String, title: String, description: String, price: String, color: Color) -> some View {
        VStack(spacing: 12) {
            Image(systemName: icon)
                .font(.title)
                .foregroundColor(color)

            VStack(spacing: 4) {
                Text(title)
                    .font(.petSubheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)

                Text(description)
                    .font(.petCaption)
                    .foregroundColor(.secondary)

                Text(price)
                    .font(.petCaption)
                    .fontWeight(.bold)
                    .foregroundColor(color)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(color.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(color.opacity(0.3), lineWidth: 1)
                )
        )
        .onTapGesture {
            // Navigate to category
        }
    }

    private func featuredProductCard(index: Int) -> some View {
        let products = [
            ("Custom Pet Portrait", "$49.99", "paintbrush.fill", Color.purple),
            ("Memory Photo Book", "$29.99", "book.fill", Color.orange),
            ("Pet Health Kit", "$19.99", "cross.case.fill", Color.green),
            ("Memorial Plaque", "$39.99", "heart.circle.fill", Color.pink),
            ("Training Guide", "$14.99", "graduationcap.fill", Color.blue)
        ]

        let product = products[index]

        return VStack(spacing: 12) {
            ZStack {
                RoundedRectangle(cornerRadius: 12)
                    .fill(product.3.opacity(0.2))
                    .frame(width: 120, height: 80)

                Image(systemName: product.2)
                    .font(.title)
                    .foregroundColor(product.3)
            }

            VStack(spacing: 4) {
                Text(product.0)
                    .font(.petCaption)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
                    .multilineTextAlignment(.center)
                    .lineLimit(2)

                Text(product.1)
                    .font(.petCaption)
                    .fontWeight(.bold)
                    .foregroundColor(product.3)
            }
        }
        .frame(width: 140)
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }

    // MARK: - Analytics Tab

    private var analyticsTab: some View {
        ScrollView {
            LazyVStack(spacing: 20) {
                if currentSubscriptionTier == "free" {
                    analyticsLockedView
                } else {
                    analyticsContentView
                }
            }
            .padding()
            .padding(.bottom, 100)
        }
    }

    private var analyticsLockedView: some View {
        VStack(spacing: 24) {
            Spacer()

            Image(systemName: "chart.bar.fill")
                .font(.system(size: 80))
                .foregroundColor(.purple.opacity(0.6))

            VStack(spacing: 8) {
                Text("Advanced Analytics")
                    .font(.petTitle2)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)

                Text("Unlock detailed insights about your pet's health, behavior, and happiness")
                    .font(.petSubheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }

            Button("Upgrade to Premium") {
                showSubscriptionPlans = true
            }
            .font(.petSubheadline)
            .fontWeight(.semibold)
            .foregroundColor(.white)
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.purple)
            )

            Spacer()
        }
    }

    private var analyticsContentView: some View {
        VStack(spacing: 20) {
            // Analytics Header
            VStack(alignment: .leading, spacing: 16) {
                HStack {
                    Image(systemName: "chart.line.uptrend.xyaxis")
                        .font(.title2)
                        .foregroundColor(.green)

                    Text("Pet Analytics")
                        .font(.petTitle2)
                        .fontWeight(.bold)

                    Spacer()
                }

                Text("Comprehensive insights into your pet's wellbeing")
                    .font(.petSubheadline)
                    .foregroundColor(.secondary)
            }

            // Key Metrics
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                analyticsMetricCard(
                    title: "Health Score",
                    value: "92%",
                    change: "+5%",
                    icon: "heart.fill",
                    color: .green
                )

                analyticsMetricCard(
                    title: "Activity Level",
                    value: "High",
                    change: "+12%",
                    icon: "figure.run",
                    color: .blue
                )

                analyticsMetricCard(
                    title: "Happiness Index",
                    value: "8.7/10",
                    change: "+0.3",
                    icon: "face.smiling.fill",
                    color: .yellow
                )

                analyticsMetricCard(
                    title: "Social Score",
                    value: "89%",
                    change: "+7%",
                    icon: "heart.circle.fill",
                    color: .pink
                )
            }

            // Charts Section
            VStack(alignment: .leading, spacing: 16) {
                Text("Health Trends")
                    .font(.petTitle3)
                    .fontWeight(.bold)

                // Mock chart
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color(.systemGray6))
                    .frame(height: 200)
                    .overlay(
                        VStack {
                            Image(systemName: "chart.line.uptrend.xyaxis")
                                .font(.system(size: 40))
                                .foregroundColor(.green)

                            Text("Health Score Trend")
                                .font(.petSubheadline)
                                .fontWeight(.semibold)
                                .foregroundColor(.primary)

                            Text("Showing improvement over 30 days")
                                .font(.petCaption)
                                .foregroundColor(.secondary)
                        }
                    )
            }
        }
    }

    private func analyticsMetricCard(title: String, value: String, change: String, icon: String, color: Color) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(color)

                Spacer()

                Text(change)
                    .font(.petCaption)
                    .fontWeight(.semibold)
                    .foregroundColor(.green)
                    .padding(.horizontal, 6)
                    .padding(.vertical, 2)
                    .background(
                        RoundedRectangle(cornerRadius: 4)
                            .fill(Color.green.opacity(0.2))
                    )
            }

            VStack(alignment: .leading, spacing: 4) {
                Text(value)
                    .font(.petTitle)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)

                Text(title)
                    .font(.petSubheadline)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }

    // MARK: - Investor Tab

    private var investorTab: some View {
        ScrollView {
            LazyVStack(spacing: 20) {
                // Investor Dashboard Header
                VStack(alignment: .leading, spacing: 16) {
                    HStack {
                        Image(systemName: "dollarsign.circle.fill")
                            .font(.title2)
                            .foregroundColor(.green)

                        Text("Investor Dashboard")
                            .font(.petTitle2)
                            .fontWeight(.bold)

                        Spacer()

                        Button("Full Report") {
                            // Navigate to investor dashboard
                        }
                        .font(.petSubheadline)
                        .foregroundColor(.green)
                    }

                    Text("Real-time metrics for our $2M/month revenue platform")
                        .font(.petSubheadline)
                        .foregroundColor(.secondary)
                }
                .padding()

                // Revenue Metrics
                LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                    revenueMetricCard(
                        title: "Monthly Revenue",
                        value: "$1.75M",
                        target: "$2M target",
                        progress: 0.875,
                        color: .green
                    )

                    revenueMetricCard(
                        title: "Active Users",
                        value: "85K",
                        target: "100K target",
                        progress: 0.85,
                        color: .blue
                    )

                    revenueMetricCard(
                        title: "Premium Conversion",
                        value: "12.5%",
                        target: "15% target",
                        progress: 0.83,
                        color: .purple
                    )

                    revenueMetricCard(
                        title: "Market Share",
                        value: "3.2%",
                        target: "5% target",
                        progress: 0.64,
                        color: .orange
                    )
                }
                .padding(.horizontal)

                // Business Highlights
                VStack(alignment: .leading, spacing: 16) {
                    Text("Business Highlights")
                        .font(.petTitle3)
                        .fontWeight(.bold)
                        .padding(.horizontal)

                    VStack(spacing: 12) {
                        businessHighlightRow(
                            icon: "chart.line.uptrend.xyaxis",
                            title: "Revenue Growth",
                            value: "+127% YoY",
                            color: .green
                        )

                        businessHighlightRow(
                            icon: "person.3.fill",
                            title: "User Acquisition",
                            value: "+15K this month",
                            color: .blue
                        )

                        businessHighlightRow(
                            icon: "brain.head.profile.fill",
                            title: "AI Engagement",
                            value: "94% adoption rate",
                            color: .purple
                        )

                        businessHighlightRow(
                            icon: "cart.fill",
                            title: "Marketplace GMV",
                            value: "$450K monthly",
                            color: .orange
                        )
                    }
                    .padding(.horizontal)
                }
            }
            .padding(.bottom, 100)
        }
    }

    private func revenueMetricCard(title: String, value: String, target: String, progress: Double, color: Color) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            VStack(alignment: .leading, spacing: 4) {
                Text(value)
                    .font(.petTitle)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)

                Text(title)
                    .font(.petSubheadline)
                    .foregroundColor(.secondary)
            }

            VStack(alignment: .leading, spacing: 6) {
                HStack {
                    Text(target)
                        .font(.petCaption)
                        .foregroundColor(.secondary)

                    Spacer()

                    Text("\(Int(progress * 100))%")
                        .font(.petCaption)
                        .fontWeight(.semibold)
                        .foregroundColor(color)
                }

                ProgressView(value: progress)
                    .progressViewStyle(LinearProgressViewStyle(tint: color))
                    .scaleEffect(y: 2)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }

    private func businessHighlightRow(icon: String, title: String, value: String, color: Color) -> some View {
        HStack(spacing: 16) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)
                .frame(width: 30)

            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.petSubheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)

                Text(value)
                    .font(.petCaption)
                    .foregroundColor(.secondary)
            }

            Spacer()

            Image(systemName: "chevron.right")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 6, x: 0, y: 3)
        )
    }
}
