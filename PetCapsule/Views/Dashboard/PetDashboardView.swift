//
//  PetDashboardView.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI

struct PetDashboardView: View {
    @EnvironmentObject var realDataService: RealDataService
    @EnvironmentObject var aiSupportService: PetAISupportService
    @State private var selectedPet: Pet?
    @State private var showAddPet = false
    @State private var animateCards = false
    @State private var showHealthAlert = false
    @State private var currentHealthAlert: String = ""
    @State private var showMyPets = false
    @State private var showMemories = false
    @State private var showPetSupport = false
    @State private var showSubscription = false
    @State private var showAddMemory = false
    @State private var showPetHealth = false
    @State private var showMemorialGarden = false
    @State private var showPetDetail = false

    // MARK: - Computed Properties for Real Data

    private var averageHealthScore: Double {
        guard !realDataService.pets.isEmpty else { return 0.0 }
        let totalScore = realDataService.pets.reduce(0.0) { $0 + $1.healthScore }
        return totalScore / Double(realDataService.pets.count)
    }

    private var totalHealthAlerts: Int {
        realDataService.pets.reduce(0) { $0 + $1.healthAlerts.count }
    }

    private var urgentHealthAlerts: Int {
        realDataService.pets.reduce(0) { total, pet in
            total + pet.healthAlerts.filter { $0.lowercased().contains("urgent") || $0.lowercased().contains("critical") }.count
        }
    }

    private var recentMemoriesCount: Int {
        let oneWeekAgo = Calendar.current.date(byAdding: .day, value: -7, to: Date()) ?? Date()
        return realDataService.memories.filter { $0.createdAt >= oneWeekAgo }.count
    }

    var body: some View {
        NavigationView {
            ScrollView {
                LazyVStack(spacing: EuropeanDesign.Spacing.sophisticated) {
                    // Welcome Header with Animation
                    welcomeHeader

                    // Quick Stats Cards
                    quickStatsSection

                    // My Pets Section
                    myPetsSection

                    // AI Health Insights
                    aiHealthSection

                    // Recent Memories
                    recentMemoriesSection

                    // Premium Features Showcase
                    premiumFeaturesSection

                    // Quick Actions
                    quickActionsSection
                }
                .padding(.horizontal, EuropeanDesign.Spacing.elegant)
                .padding(.bottom, 100)
            }
            .background(EuropeanDesign.Gradients.elegantBackground)
            .navigationTitle("Dashboard")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: {
                        withAnimation(EuropeanDesign.Animations.sophisticated) {
                            showAddPet = true
                        }
                    }) {
                        ZStack {
                            Circle()
                                .fill(EuropeanDesign.Colors.accent)
                                .frame(width: 36, height: 36)
                                .shadow(
                                    color: EuropeanDesign.Shadows.accentGlow.color,
                                    radius: EuropeanDesign.Shadows.accentGlow.radius,
                                    x: 0, y: 2
                                )

                            Image(systemName: "plus")
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(EuropeanDesign.Colors.textPrimary)
                        }
                    }
                }
            }
            .sheet(isPresented: $showAddPet) {
                AddPetView()
                    .environmentObject(realDataService)
            }
            .sheet(isPresented: $showMyPets) {
                MyPetsView()
                    .environmentObject(realDataService)
            }
            .sheet(isPresented: $showMemories) {
                MemoryVaultView()
                    .environmentObject(realDataService)
            }
            .sheet(isPresented: $showPetSupport) {
                PetSupportView()
                    .environmentObject(aiSupportService)
            }
            .sheet(isPresented: $showSubscription) {
                SubscriptionView()
            }
            .sheet(isPresented: $showAddMemory) {
                AddMemoryView()
                    .environmentObject(realDataService)
            }
            .sheet(isPresented: $showPetHealth) {
                PetHealthView()
                    .environmentObject(realDataService)
            }
            .sheet(isPresented: $showMemorialGarden) {
                MemorialGardenView()
                    .environmentObject(realDataService)
            }
            .sheet(isPresented: $showPetDetail) {
                if let selectedPet = selectedPet {
                    PetDetailView(pet: selectedPet)
                        .environmentObject(realDataService)
                }
            }
            .alert("Health Alert", isPresented: $showHealthAlert) {
                Button("View Details") {
                    showPetHealth = true
                }
                Button("Dismiss", role: .cancel) { }
            } message: {
                Text(currentHealthAlert)
            }
            .onAppear {
                withAnimation(.spring(response: 0.8, dampingFraction: 0.8)) {
                    animateCards = true
                }
                checkHealthAlerts()
                // Only load data once on first appearance and if not already loading
                if realDataService.memories.isEmpty &&
                   realDataService.pets.isEmpty &&
                   !realDataService.isLoading {
                    Task {
                        await realDataService.refreshAllData()
                    }
                }
            }
            .refreshable {
                // Only refresh if not already loading
                if !realDataService.isLoading {
                    await realDataService.refreshAllData()
                }
            }
        }
    }

    // MARK: - Welcome Header

    private var welcomeHeader: some View {
        EuropeanCard {
            VStack(spacing: EuropeanDesign.Spacing.elegant) {
                HStack {
                    VStack(alignment: .leading, spacing: EuropeanDesign.Spacing.sm) {
                        Text("Welcome back! 🐾")
                            .font(EuropeanDesign.Typography.elegantTitle)
                            .foregroundColor(EuropeanDesign.Colors.textPrimary)

                        Text("Your pets are waiting for you")
                            .font(EuropeanDesign.Typography.sophisticatedBody)
                            .foregroundColor(EuropeanDesign.Colors.textSecondary)
                    }

                    Spacer()

                    // Elegant pet mood indicator
                    VStack(spacing: EuropeanDesign.Spacing.xs) {
                        ZStack {
                            Circle()
                                .fill(EuropeanDesign.Colors.accentLight.opacity(0.3))
                                .frame(width: 50, height: 50)
                                .scaleEffect(animateCards ? 1.1 : 1.0)
                                .animation(EuropeanDesign.Animations.luxurious.repeatForever(autoreverses: true), value: animateCards)

                            Image(systemName: "sun.max.fill")
                                .font(.title2)
                                .foregroundColor(EuropeanDesign.Colors.accent)
                                .rotationEffect(.degrees(animateCards ? 360 : 0))
                                .animation(.linear(duration: 20).repeatForever(autoreverses: false), value: animateCards)
                        }

                        Text("Joyful")
                            .font(EuropeanDesign.Typography.refinedCaption)
                            .foregroundColor(EuropeanDesign.Colors.accent)
                    }
                }

                // Today's elegant highlights
                HStack(spacing: EuropeanDesign.Spacing.md) {
                    highlightCard(
                        icon: "heart.fill",
                        title: "Health Score",
                        value: "\(Int(averageHealthScore * 100))%",
                        color: averageHealthScore > 0.8 ? EuropeanDesign.Colors.success :
                               averageHealthScore > 0.6 ? EuropeanDesign.Colors.warning : EuropeanDesign.Colors.error
                    )

                    highlightCard(
                        icon: "photo.fill",
                        title: "New Memories",
                        value: "\(ProductionMemoryService.shared.memories.count)",
                        color: EuropeanDesign.Colors.info
                    )

                    highlightCard(
                        icon: "brain.head.profile",
                        title: "AI Insights",
                        value: "\(totalHealthAlerts)",
                        color: EuropeanDesign.Colors.premium
                    )
                }
            }
        }
        .scaleEffect(animateCards ? 1.0 : 0.9)
        .opacity(animateCards ? 1.0 : 0.0)
        .animation(EuropeanDesign.Animations.sophisticated.delay(0.1), value: animateCards)
    }

    private func highlightCard(icon: String, title: String, value: String, color: Color) -> some View {
        VStack(spacing: EuropeanDesign.Spacing.sm) {
            ZStack {
                Circle()
                    .fill(color.opacity(0.15))
                    .frame(width: 40, height: 40)

                Image(systemName: icon)
                    .font(.system(size: 18, weight: .medium))
                    .foregroundColor(color)
            }

            Text(value)
                .font(EuropeanDesign.Typography.title3)
                .fontWeight(.medium)
                .foregroundColor(EuropeanDesign.Colors.textPrimary)
                .lineLimit(1)
                .minimumScaleFactor(0.8)

            Text(title)
                .font(EuropeanDesign.Typography.caption)
                .foregroundColor(EuropeanDesign.Colors.textSecondary)
                .multilineTextAlignment(.center)
                .lineLimit(2)
                .minimumScaleFactor(0.9)
        }
        .frame(maxWidth: .infinity, minHeight: 120, maxHeight: 120)
        .padding(EuropeanDesign.Spacing.md)
        .background(
            RoundedRectangle(cornerRadius: EuropeanDesign.CornerRadius.medium)
                .fill(EuropeanDesign.Colors.surface)
                .overlay(
                    RoundedRectangle(cornerRadius: EuropeanDesign.CornerRadius.medium)
                        .stroke(color.opacity(0.2), lineWidth: 1)
                )
        )
        .shadow(
            color: EuropeanDesign.Shadows.subtle.color,
            radius: EuropeanDesign.Shadows.subtle.radius,
            x: 0, y: 1
        )
    }

    // MARK: - Quick Stats Section

    private var quickStatsSection: some View {
        VStack(alignment: .leading, spacing: EuropeanDesign.Spacing.elegant) {
            HStack {
                Text("Quick Stats")
                    .font(EuropeanDesign.Typography.elegantTitle)
                    .foregroundColor(EuropeanDesign.Colors.textPrimary)

                Spacer()

                EuropeanButton("View All", icon: "arrow.right", style: .secondary) {
                    withAnimation(EuropeanDesign.Animations.elegant) {
                        showMyPets = true
                    }
                }
            }

            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: EuropeanDesign.Spacing.md) {
                statCard(
                    title: "Total Pets",
                    value: "\(realDataService.pets.count)",
                    icon: "pawprint.fill",
                    color: EuropeanDesign.Colors.accent,
                    trend: realDataService.pets.count > 0 ? "Active" : "Add pets"
                ) {
                    withAnimation(EuropeanDesign.Animations.elegant) {
                        showMyPets = true
                    }
                }

                statCard(
                    title: "Memories Saved",
                    value: "\(realDataService.memories.count)",
                    icon: "photo.stack.fill",
                    color: EuropeanDesign.Colors.info,
                    trend: "+\(recentMemoriesCount) this week"
                ) {
                    withAnimation(EuropeanDesign.Animations.elegant) {
                        showMemories = true
                    }
                }

                statCard(
                    title: "Health Alerts",
                    value: "\(totalHealthAlerts)",
                    icon: "heart.text.square.fill",
                    color: urgentHealthAlerts > 0 ? EuropeanDesign.Colors.error : EuropeanDesign.Colors.warning,
                    trend: urgentHealthAlerts > 0 ? "\(urgentHealthAlerts) urgent" : "All good"
                ) {
                    withAnimation(EuropeanDesign.Animations.elegant) {
                        showPetSupport = true
                    }
                }

                statCard(
                    title: "AI Insights",
                    value: "\(realDataService.pets.reduce(0) { $0 + $1.aiRecommendations.count })",
                    icon: "brain.head.profile.fill",
                    color: EuropeanDesign.Colors.premium,
                    trend: "Available"
                ) {
                    withAnimation(EuropeanDesign.Animations.elegant) {
                        showPetSupport = true
                    }
                }
            }
        }
        .scaleEffect(animateCards ? 1.0 : 0.9)
        .opacity(animateCards ? 1.0 : 0.0)
        .animation(EuropeanDesign.Animations.sophisticated.delay(0.2), value: animateCards)
    }

    private func statCard(title: String, value: String, icon: String, color: Color, trend: String, action: @escaping () -> Void) -> some View {
        EuropeanCard {
            VStack(alignment: .leading, spacing: EuropeanDesign.Spacing.md) {
                HStack {
                    ZStack {
                        RoundedRectangle(cornerRadius: EuropeanDesign.CornerRadius.small)
                            .fill(color.opacity(0.15))
                            .frame(width: 36, height: 36)

                        Image(systemName: icon)
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(color)
                    }

                    Spacer()

                    VStack(alignment: .trailing, spacing: 2) {
                        Text(trend)
                            .font(EuropeanDesign.Typography.caption)
                            .foregroundColor(EuropeanDesign.Colors.textTertiary)

                        Circle()
                            .fill(color.opacity(0.3))
                            .frame(width: 6, height: 6)
                    }
                }

                VStack(alignment: .leading, spacing: EuropeanDesign.Spacing.xs) {
                    Text(value)
                        .font(EuropeanDesign.Typography.title1)
                        .fontWeight(.light)
                        .foregroundColor(EuropeanDesign.Colors.textPrimary)

                    Text(title)
                        .font(EuropeanDesign.Typography.subheadline)
                        .foregroundColor(EuropeanDesign.Colors.textSecondary)
                }
            }
        }
        .onTapGesture {
            print("📊 Tapped on stat card: \(title)")
            action()
        }
    }

    // MARK: - My Pets Section

    private var myPetsSection: some View {
        VStack(alignment: .leading, spacing: EuropeanDesign.Spacing.elegant) {
            HStack {
                Text("My Pets")
                    .font(EuropeanDesign.Typography.elegantTitle)
                    .foregroundColor(EuropeanDesign.Colors.textPrimary)

                Spacer()

                EuropeanButton("Manage All", icon: "pawprint", style: .secondary) {
                    withAnimation(EuropeanDesign.Animations.elegant) {
                        showMyPets = true
                    }
                }
            }

            if realDataService.pets.isEmpty {
                emptyPetsView
            } else {
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: EuropeanDesign.Spacing.elegant) {
                        ForEach(Array(realDataService.pets.enumerated()), id: \.element.id) { index, pet in
                            petCard(pet: pet)
                                .scaleEffect(animateCards ? 1.0 : 0.8)
                                .opacity(animateCards ? 1.0 : 0.0)
                                .animation(EuropeanDesign.Animations.sophisticated.delay(Double(index) * 0.1 + 0.3), value: animateCards)
                        }
                    }
                    .padding(.horizontal, EuropeanDesign.Spacing.elegant)
                }
            }
        }
    }

    private var emptyPetsView: some View {
        EuropeanCard {
            VStack(spacing: EuropeanDesign.Spacing.elegant) {
                ZStack {
                    Circle()
                        .fill(EuropeanDesign.Colors.accent.opacity(0.1))
                        .frame(width: 80, height: 80)

                    Image(systemName: "pawprint.circle.fill")
                        .font(.system(size: 40))
                        .foregroundColor(EuropeanDesign.Colors.accent)
                }

                VStack(spacing: EuropeanDesign.Spacing.sm) {
                    Text("No pets added yet")
                        .font(EuropeanDesign.Typography.title3)
                        .foregroundColor(EuropeanDesign.Colors.textPrimary)

                    Text("Add your first pet to get started with AI health insights and memory preservation")
                        .font(EuropeanDesign.Typography.subheadline)
                        .foregroundColor(EuropeanDesign.Colors.textSecondary)
                        .multilineTextAlignment(.center)
                }

                EuropeanButton("Add Your First Pet", icon: "plus.circle.fill", style: .accent) {
                    withAnimation(EuropeanDesign.Animations.sophisticated) {
                        showAddPet = true
                    }
                }
            }
        }
    }

    private func petCard(pet: Pet) -> some View {
        EuropeanCard {
            VStack(spacing: EuropeanDesign.Spacing.md) {
                // Elegant pet image or placeholder
                AsyncImage(url: URL(string: pet.profileImageURL ?? "")) { image in
                    image
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                } placeholder: {
                    ZStack {
                        RoundedRectangle(cornerRadius: EuropeanDesign.CornerRadius.medium)
                            .fill(EuropeanDesign.Colors.accent.opacity(0.15))

                        Text(pet.species == "dog" ? "🐕" : pet.species == "cat" ? "🐱" : "🐾")
                            .font(.system(size: 36))
                    }
                }
                .frame(width: 90, height: 90)
                .clipShape(RoundedRectangle(cornerRadius: EuropeanDesign.CornerRadius.medium))
                .overlay(
                    RoundedRectangle(cornerRadius: EuropeanDesign.CornerRadius.medium)
                        .stroke(EuropeanDesign.Colors.accent.opacity(0.2), lineWidth: 1)
                )

                VStack(spacing: EuropeanDesign.Spacing.xs) {
                    Text(pet.name)
                        .font(EuropeanDesign.Typography.headline)
                        .foregroundColor(EuropeanDesign.Colors.textPrimary)

                    Text(pet.breed)
                        .font(EuropeanDesign.Typography.caption)
                        .foregroundColor(EuropeanDesign.Colors.textSecondary)

                    // Elegant health indicator
                    HStack(spacing: EuropeanDesign.Spacing.xs) {
                        Circle()
                            .fill(pet.healthScore > 0.8 ? EuropeanDesign.Colors.success :
                                  pet.healthScore > 0.6 ? EuropeanDesign.Colors.warning : EuropeanDesign.Colors.error)
                            .frame(width: 6, height: 6)

                        Text("\(Int(pet.healthScore * 100))% Health")
                            .font(EuropeanDesign.Typography.caption)
                            .foregroundColor(EuropeanDesign.Colors.textTertiary)
                    }
                }
            }
        }
        .onTapGesture {
            print("🐾 Tapped on pet: \(pet.name)")
            withAnimation(EuropeanDesign.Animations.elegant) {
                selectedPet = pet
                showPetDetail = true
            }
        }
    }

    // MARK: - AI Health Section

    private var aiHealthSection: some View {
        VStack(alignment: .leading, spacing: EuropeanDesign.Spacing.elegant) {
            HStack {
                Text("AI Health Insights")
                    .font(EuropeanDesign.Typography.elegantTitle)
                    .foregroundColor(EuropeanDesign.Colors.textPrimary)

                Spacer()

                EuropeanButton("View All", icon: "brain.head.profile", style: .secondary) {
                    withAnimation(EuropeanDesign.Animations.elegant) {
                        showPetSupport = true
                    }
                }
            }

            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: EuropeanDesign.Spacing.elegant) {
                    aiInsightCard(
                        icon: "brain.head.profile.fill",
                        title: "Nutrition Analysis",
                        description: "Max needs 15% more protein",
                        priority: .medium,
                        color: EuropeanDesign.Colors.warning
                    )

                    aiInsightCard(
                        icon: "heart.text.square.fill",
                        title: "Exercise Recommendation",
                        description: "Luna needs 20 min more activity",
                        priority: .low,
                        color: EuropeanDesign.Colors.info
                    )

                    aiInsightCard(
                        icon: "stethoscope",
                        title: "Health Prediction",
                        description: "Charlie: Joint care recommended",
                        priority: .high,
                        color: EuropeanDesign.Colors.error
                    )
                }
                .padding(.horizontal, EuropeanDesign.Spacing.elegant)
            }
        }
        .scaleEffect(animateCards ? 1.0 : 0.9)
        .opacity(animateCards ? 1.0 : 0.0)
        .animation(EuropeanDesign.Animations.sophisticated.delay(0.4), value: animateCards)
    }

    private func aiInsightCard(icon: String, title: String, description: String, priority: Priority, color: Color) -> some View {
        EuropeanCard {
            VStack(alignment: .leading, spacing: EuropeanDesign.Spacing.md) {
                HStack {
                    ZStack {
                        RoundedRectangle(cornerRadius: EuropeanDesign.CornerRadius.small)
                            .fill(color.opacity(0.15))
                            .frame(width: 36, height: 36)

                        Image(systemName: icon)
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(color)
                    }

                    Spacer()

                    Text(priority.rawValue.uppercased())
                        .font(EuropeanDesign.Typography.caption)
                        .fontWeight(.medium)
                        .foregroundColor(Color(priority.color))
                        .padding(.horizontal, EuropeanDesign.Spacing.sm)
                        .padding(.vertical, EuropeanDesign.Spacing.xs)
                        .background(
                            RoundedRectangle(cornerRadius: EuropeanDesign.CornerRadius.small)
                                .fill(Color(priority.color).opacity(0.15))
                        )
                }

                VStack(alignment: .leading, spacing: EuropeanDesign.Spacing.xs) {
                    Text(title)
                        .font(EuropeanDesign.Typography.headline)
                        .foregroundColor(EuropeanDesign.Colors.textPrimary)

                    Text(description)
                        .font(EuropeanDesign.Typography.caption)
                        .foregroundColor(EuropeanDesign.Colors.textSecondary)
                        .lineLimit(2)
                }

                EuropeanButton("View Details", style: .secondary) {
                    withAnimation(EuropeanDesign.Animations.elegant) {
                        showPetSupport = true
                    }
                }
            }
        }
        .frame(width: 220)
    }

    // MARK: - Recent Memories Section

    private var recentMemoriesSection: some View {
        VStack(alignment: .leading, spacing: EuropeanDesign.Spacing.elegant) {
            HStack {
                Text("Recent Memories")
                    .font(EuropeanDesign.Typography.elegantTitle)
                    .foregroundColor(EuropeanDesign.Colors.textPrimary)

                Spacer()

                EuropeanButton("View All", icon: "photo.stack", style: .secondary) {
                    withAnimation(EuropeanDesign.Animations.elegant) {
                        showMemories = true
                    }
                }
            }

            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: EuropeanDesign.Spacing.md) {
                ForEach(realDataService.memories.prefix(6).indices, id: \.self) { index in
                    memoryThumbnail(memory: realDataService.memories[index], index: index)
                        .scaleEffect(animateCards ? 1.0 : 0.8)
                        .opacity(animateCards ? 1.0 : 0.0)
                        .animation(EuropeanDesign.Animations.sophisticated.delay(Double(index) * 0.05 + 0.5), value: animateCards)
                }

                // Show placeholder thumbnails if no memories
                if realDataService.memories.isEmpty {
                    ForEach(0..<6, id: \.self) { index in
                        placeholderMemoryThumbnail(index: index)
                            .scaleEffect(animateCards ? 1.0 : 0.8)
                            .opacity(animateCards ? 1.0 : 0.0)
                            .animation(EuropeanDesign.Animations.sophisticated.delay(Double(index) * 0.05 + 0.5), value: animateCards)
                    }
                }
            }
        }
    }

    private func memoryThumbnail(memory: Memory, index: Int) -> some View {
        ZStack {
            // Background with European styling
            RoundedRectangle(cornerRadius: EuropeanDesign.CornerRadius.medium)
                .fill(EuropeanDesign.Gradients.sophisticatedCard)
                .aspectRatio(1, contentMode: .fit)
                .overlay(
                    RoundedRectangle(cornerRadius: EuropeanDesign.CornerRadius.medium)
                        .stroke(EuropeanDesign.Colors.accent.opacity(0.2), lineWidth: 1)
                )

            // Memory content
            if let thumbnailURL = memory.thumbnailURL, !thumbnailURL.isEmpty {
                AsyncImage(url: URL(string: thumbnailURL)) { image in
                    image
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .clipShape(RoundedRectangle(cornerRadius: EuropeanDesign.CornerRadius.medium))
                } placeholder: {
                    memoryPlaceholderContent(for: memory.type)
                }
            } else {
                memoryPlaceholderContent(for: memory.type)
            }

            // Memory type indicator
            VStack {
                HStack {
                    Spacer()
                    Image(systemName: memory.type == .photo ? "photo.fill" :
                                    memory.type == .video ? "video.fill" :
                                    memory.type == .milestone ? "star.fill" : "text.alignleft")
                        .font(.caption)
                        .foregroundColor(EuropeanDesign.Colors.accent)
                        .padding(EuropeanDesign.Spacing.xs)
                        .background(
                            Circle()
                                .fill(EuropeanDesign.Colors.background.opacity(0.9))
                        )
                }
                Spacer()
            }
            .padding(EuropeanDesign.Spacing.xs)
        }
        .shadow(
            color: EuropeanDesign.Shadows.soft.color,
            radius: EuropeanDesign.Shadows.soft.radius,
            x: 0, y: 2
        )
        .onTapGesture {
            print("📸 Tapped on memory: \(memory.title)")
            withAnimation(EuropeanDesign.Animations.elegant) {
                showMemories = true
            }
        }
    }

    private func placeholderMemoryThumbnail(index: Int) -> some View {
        ZStack {
            RoundedRectangle(cornerRadius: EuropeanDesign.CornerRadius.medium)
                .fill(EuropeanDesign.Colors.accent.opacity(0.1))
                .aspectRatio(1, contentMode: .fit)
                .overlay(
                    RoundedRectangle(cornerRadius: EuropeanDesign.CornerRadius.medium)
                        .stroke(EuropeanDesign.Colors.accent.opacity(0.2), lineWidth: 1)
                )

            VStack(spacing: EuropeanDesign.Spacing.xs) {
                Image(systemName: index % 3 == 0 ? "photo.fill" :
                                index % 3 == 1 ? "video.fill" : "star.fill")
                    .font(.title3)
                    .foregroundColor(EuropeanDesign.Colors.accent)

                Text("Memory \(index + 1)")
                    .font(EuropeanDesign.Typography.caption)
                    .foregroundColor(EuropeanDesign.Colors.textSecondary)
            }
        }
        .onTapGesture {
            withAnimation(EuropeanDesign.Animations.elegant) {
                showMemories = true
            }
        }
    }

    private func memoryPlaceholderContent(for type: MemoryType) -> some View {
        VStack(spacing: EuropeanDesign.Spacing.xs) {
            ZStack {
                Circle()
                    .fill(EuropeanDesign.Colors.accent.opacity(0.15))
                    .frame(width: 32, height: 32)

                Image(systemName: type == .photo ? "photo.fill" :
                                type == .video ? "video.fill" :
                                type == .milestone ? "star.fill" : "text.alignleft")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(EuropeanDesign.Colors.accent)
            }

            Text(type.rawValue.capitalized)
                .font(EuropeanDesign.Typography.caption)
                .foregroundColor(EuropeanDesign.Colors.textSecondary)
        }
    }

    // MARK: - Premium Features Section

    private var premiumFeaturesSection: some View {
        VStack(alignment: .leading, spacing: EuropeanDesign.Spacing.elegant) {
            HStack {
                Text("Premium Features")
                    .font(EuropeanDesign.Typography.elegantTitle)
                    .foregroundColor(EuropeanDesign.Colors.textPrimary)

                Spacer()

                EuropeanButton("Upgrade", icon: "crown.fill", style: .premium) {
                    withAnimation(EuropeanDesign.Animations.elegant) {
                        showSubscription = true
                    }
                }
            }

            HStack(spacing: EuropeanDesign.Spacing.md) {
                premiumFeatureCard(
                    icon: "brain.head.profile.fill",
                    title: "AI Health Analysis",
                    description: "Advanced health insights",
                    isLocked: false
                )

                premiumFeatureCard(
                    icon: "video.fill",
                    title: "Video Montages",
                    description: "Professional pet videos",
                    isLocked: false
                )
            }
        }
        .scaleEffect(animateCards ? 1.0 : 0.9)
        .opacity(animateCards ? 1.0 : 0.0)
        .animation(EuropeanDesign.Animations.sophisticated.delay(0.6), value: animateCards)
    }

    private func premiumFeatureCard(icon: String, title: String, description: String, isLocked: Bool) -> some View {
        VStack(spacing: EuropeanDesign.Spacing.md) {
            ZStack {
                Circle()
                    .fill(isLocked ? EuropeanDesign.Colors.surfaceTertiary : EuropeanDesign.Colors.premium.opacity(0.15))
                    .frame(width: 50, height: 50)

                Image(systemName: icon)
                    .font(.system(size: 20, weight: .medium))
                    .foregroundColor(isLocked ? EuropeanDesign.Colors.textTertiary : EuropeanDesign.Colors.premium)

                if isLocked {
                    Image(systemName: "lock.fill")
                        .font(.caption)
                        .foregroundColor(EuropeanDesign.Colors.background)
                        .padding(EuropeanDesign.Spacing.xs)
                        .background(Circle().fill(EuropeanDesign.Colors.textTertiary))
                        .offset(x: 18, y: -18)
                }
            }

            VStack(spacing: EuropeanDesign.Spacing.xs) {
                Text(title)
                    .font(EuropeanDesign.Typography.headline)
                    .foregroundColor(EuropeanDesign.Colors.textPrimary)
                    .lineLimit(2)
                    .multilineTextAlignment(.center)
                    .minimumScaleFactor(0.9)

                Text(description)
                    .font(EuropeanDesign.Typography.caption)
                    .foregroundColor(EuropeanDesign.Colors.textSecondary)
                    .multilineTextAlignment(.center)
                    .lineLimit(2)
                    .minimumScaleFactor(0.9)
            }
        }
        .frame(maxWidth: .infinity, minHeight: 140, maxHeight: 140)
        .padding(EuropeanDesign.Spacing.elegant)
        .background(
            Group {
                if isLocked {
                    RoundedRectangle(cornerRadius: EuropeanDesign.CornerRadius.elegant)
                        .fill(EuropeanDesign.Colors.surfaceSecondary)
                } else {
                    RoundedRectangle(cornerRadius: EuropeanDesign.CornerRadius.elegant)
                        .fill(EuropeanDesign.Gradients.premiumGlow)
                }
            }
            .overlay(
                RoundedRectangle(cornerRadius: EuropeanDesign.CornerRadius.elegant)
                    .stroke(isLocked ? EuropeanDesign.Colors.surfaceTertiary : EuropeanDesign.Colors.premium.opacity(0.3), lineWidth: 1)
            )
        )
        .shadow(
            color: isLocked ? EuropeanDesign.Shadows.subtle.color : EuropeanDesign.Shadows.premiumGlow.color,
            radius: isLocked ? EuropeanDesign.Shadows.subtle.radius : EuropeanDesign.Shadows.premiumGlow.radius,
            x: 0, y: isLocked ? 1 : 4
        )
    }

    // MARK: - Quick Actions Section

    private var quickActionsSection: some View {
        VStack(alignment: .leading, spacing: EuropeanDesign.Spacing.elegant) {
            Text("Quick Actions")
                .font(EuropeanDesign.Typography.elegantTitle)
                .foregroundColor(EuropeanDesign.Colors.textPrimary)

            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: EuropeanDesign.Spacing.md) {
                quickActionButton(
                    icon: "plus.circle.fill",
                    title: "Add Memory",
                    color: EuropeanDesign.Colors.info
                ) {
                    withAnimation(EuropeanDesign.Animations.elegant) {
                        showAddMemory = true
                    }
                }

                quickActionButton(
                    icon: "stethoscope",
                    title: "Health Check",
                    color: EuropeanDesign.Colors.success
                ) {
                    withAnimation(EuropeanDesign.Animations.elegant) {
                        showPetHealth = true
                    }
                }

                quickActionButton(
                    icon: "calendar.badge.plus",
                    title: "Schedule Vet",
                    color: EuropeanDesign.Colors.warning
                ) {
                    withAnimation(EuropeanDesign.Animations.elegant) {
                        showPetHealth = true
                    }
                }

                quickActionButton(
                    icon: "heart.circle.fill",
                    title: "Memorial Garden",
                    color: EuropeanDesign.Colors.premium
                ) {
                    withAnimation(EuropeanDesign.Animations.elegant) {
                        showMemorialGarden = true
                    }
                }
            }
        }
        .scaleEffect(animateCards ? 1.0 : 0.9)
        .opacity(animateCards ? 1.0 : 0.0)
        .animation(EuropeanDesign.Animations.sophisticated.delay(0.7), value: animateCards)
    }

    private func quickActionButton(icon: String, title: String, color: Color, action: @escaping () -> Void) -> some View {
        Button(action: action) {
            HStack(alignment: .center, spacing: EuropeanDesign.Spacing.sm) {
                ZStack {
                    RoundedRectangle(cornerRadius: EuropeanDesign.CornerRadius.small)
                        .fill(color.opacity(0.15))
                        .frame(width: 32, height: 32)

                    Image(systemName: icon)
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(color)
                }

                Text(title)
                    .font(EuropeanDesign.Typography.subheadline)
                    .foregroundColor(EuropeanDesign.Colors.textPrimary)
                    .lineLimit(2)
                    .multilineTextAlignment(.leading)
                    .minimumScaleFactor(0.8)
                    .frame(maxWidth: .infinity, alignment: .leading)

                Image(systemName: "chevron.right")
                    .font(.system(size: 10, weight: .medium))
                    .foregroundColor(EuropeanDesign.Colors.textTertiary)
            }
            .frame(minHeight: 56)
            .padding(.horizontal, EuropeanDesign.Spacing.md)
            .padding(.vertical, EuropeanDesign.Spacing.sm)
            .background(
                RoundedRectangle(cornerRadius: EuropeanDesign.CornerRadius.elegant)
                    .fill(EuropeanDesign.Gradients.sophisticatedCard)
                    .overlay(
                        RoundedRectangle(cornerRadius: EuropeanDesign.CornerRadius.elegant)
                            .stroke(color.opacity(0.2), lineWidth: 1)
                    )
            )
            .shadow(
                color: EuropeanDesign.Shadows.soft.color,
                radius: EuropeanDesign.Shadows.soft.radius,
                x: 0, y: 2
            )
        }
        .buttonStyle(PlainButtonStyle())
    }

    // MARK: - Helper Methods

    private func checkHealthAlerts() {
        let petsWithAlerts = realDataService.pets.filter { !$0.healthAlerts.isEmpty }
        if let firstPet = petsWithAlerts.first, !firstPet.healthAlerts.isEmpty {
            currentHealthAlert = "\(firstPet.name): \(firstPet.healthAlerts.first!)"
            showHealthAlert = true
        }
    }
}
