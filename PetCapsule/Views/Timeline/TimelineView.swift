//
//  TimelineView.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI

struct TimelineView: View {
    @StateObject private var aiService = AIService.shared

    @State private var selectedPet: Pet?
    @State private var memories: [Memory] = []
    @State private var showingMemoryPrompts = false

    @State private var showingPetSelector = false
    @State private var memoryPrompts: [MemoryPrompt] = []
    @State private var selectedTimeFilter: TimeFilter = .all
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Pet Selector Header
                PetSelectorHeader(
                    selectedPet: $selectedPet,
                    showingPetSelector: $showingPetSelector
                )
                
                // Quick Actions Bar
                QuickActionsBar(
                    showingMemoryPrompts: $showingMemoryPrompts,
                    selectedPet: selectedPet
                )
                
                // Time Filter
                TimeFilterPicker(selectedFilter: $selectedTimeFilter)
                
                // Timeline Content
                if memories.isEmpty {
                    EmptyTimelineView(
                        selectedPet: selectedPet,
                        showingMemoryPrompts: $showingMemoryPrompts
                    )
                } else {
                    TimelineScrollView(
                        memories: filteredMemories,
                        selectedPet: selectedPet
                    )
                }
            }
            .navigationTitle("Timeline")
            .navigationBarTitleDisplayMode(.large)
            .onAppear {
                loadMemories()
            }
            .sheet(isPresented: $showingPetSelector) {
                PetSelectorSheet(selectedPet: $selectedPet)
            }
            .sheet(isPresented: $showingMemoryPrompts) {
                MemoryPromptsView(
                    pet: selectedPet,
                    prompts: memoryPrompts
                )
            }

        }
    }
    
    private var filteredMemories: [Memory] {
        switch selectedTimeFilter {
        case .all:
            return memories
        case .thisWeek:
            return memories.filter { Calendar.current.isDate($0.createdAt, equalTo: Date(), toGranularity: .weekOfYear) }
        case .thisMonth:
            return memories.filter { Calendar.current.isDate($0.createdAt, equalTo: Date(), toGranularity: .month) }
        case .thisYear:
            return memories.filter { Calendar.current.isDate($0.createdAt, equalTo: Date(), toGranularity: .year) }
        }
    }
    
    private func loadMemories() {
        // Mock memories for demo
        memories = generateMockMemories()
        
        // Load AI prompts if pet is selected
        if let pet = selectedPet {
            Task {
                do {
                    memoryPrompts = try await aiService.generateMemoryPrompts(for: pet)
                } catch {
                    print("Error loading memory prompts: \(error)")
                }
            }
        }
    }
    
    private func generateMockMemories() -> [Memory] {
        return [
            Memory(
                title: "First Day Home",
                content: "Buddy's first day in his new home. He was so curious about everything!",
                type: .photo,
                milestone: "Adoption Day",
                sentiment: "joyful",
                tags: ["adoption", "home", "first day", "curious"]
            ),
            Memory(
                title: "Learning to Sit",
                content: "Teaching Buddy his first command. He's such a smart boy!",
                type: .video,
                milestone: "First Command",
                sentiment: "proud",
                tags: ["training", "sit", "smart", "learning"]
            ),
            Memory(
                title: "Beach Adventure",
                content: "Buddy's first time at the beach. He loved the sand but was scared of the waves at first.",
                type: .photo,
                milestone: "First Beach Visit",
                sentiment: "adventurous",
                tags: ["beach", "sand", "waves", "adventure", "first time"]
            )
        ]
    }
}

struct PetSelectorHeader: View {
    @Binding var selectedPet: Pet?
    @Binding var showingPetSelector: Bool
    
    var body: some View {
        HStack {
            Button(action: {
                showingPetSelector = true
            }) {
                HStack(spacing: Spacing.sm) {
                    if let pet = selectedPet {
                        AsyncImage(url: URL(string: pet.profileImageURL ?? "")) { image in
                            image
                                .resizable()
                                .aspectRatio(contentMode: .fill)
                        } placeholder: {
                            Circle()
                                .fill(Color.petAccent.opacity(0.2))
                                .overlay(
                                    Text("🐕")
                                        .font(.title2)
                                )
                        }
                        .frame(width: 40, height: 40)
                        .clipShape(Circle())
                        
                        VStack(alignment: .leading, spacing: 2) {
                            Text(pet.name)
                                .font(.petHeadline)
                                .foregroundColor(.primary)
                            
                            Text(pet.breed)
                                .font(.petCaption)
                                .foregroundColor(.secondary)
                        }
                    } else {
                        Circle()
                            .fill(Color.gray.opacity(0.2))
                            .frame(width: 40, height: 40)
                            .overlay(
                                Image(systemName: "plus")
                                    .foregroundColor(.secondary)
                            )
                        
                        Text("Select Pet")
                            .font(.petHeadline)
                            .foregroundColor(.secondary)
                    }
                    
                    Image(systemName: "chevron.down")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            Spacer()
        }
        .padding(.horizontal, Spacing.lg)
        .padding(.vertical, Spacing.md)
        .background(Color.gray.opacity(0.05))
    }
}

struct QuickActionsBar: View {
    @Binding var showingMemoryPrompts: Bool
    let selectedPet: Pet?
    
    var body: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: Spacing.md) {
                QuickActionButton(
                    icon: "lightbulb.fill",
                    title: "Memory Ideas",
                    color: .yellow
                ) {
                    showingMemoryPrompts = true
                }
                .disabled(selectedPet == nil)
                

                
                QuickActionButton(
                    icon: "brain.head.profile",
                    title: "AI Insights",
                    color: .blue
                ) {
                    // Show AI insights
                }
                .disabled(selectedPet == nil)
                
                QuickActionButton(
                    icon: "heart.text.square",
                    title: "Milestones",
                    color: .pink
                ) {
                    // Show milestones
                }
                .disabled(selectedPet == nil)
                
                QuickActionButton(
                    icon: "calendar.badge.clock",
                    title: "Time Vault",
                    color: .green
                ) {
                    // Create time vault
                }
                .disabled(selectedPet == nil)
            }
            .padding(.horizontal, Spacing.lg)
        }
        .padding(.vertical, Spacing.sm)
    }
}

struct QuickActionButton: View {
    let icon: String
    let title: String
    let color: Color
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: Spacing.xs) {
                Image(systemName: icon)
                    .font(.title3)
                    .foregroundColor(color)
                
                Text(title)
                    .font(.petCaption)
                    .foregroundColor(.primary)
                    .multilineTextAlignment(.center)
            }
            .frame(width: 80, height: 60)
            .background(
                RoundedRectangle(cornerRadius: CornerRadius.md)
                    .fill(color.opacity(0.1))
            )
        }
    }
}

enum TimeFilter: String, CaseIterable {
    case all = "All"
    case thisWeek = "This Week"
    case thisMonth = "This Month"
    case thisYear = "This Year"
}

struct TimeFilterPicker: View {
    @Binding var selectedFilter: TimeFilter
    
    var body: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: Spacing.sm) {
                ForEach(TimeFilter.allCases, id: \.self) { filter in
                    Button(action: {
                        withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                            selectedFilter = filter
                        }
                    }) {
                        Text(filter.rawValue)
                            .font(.petCallout)
                            .foregroundColor(selectedFilter == filter ? .white : .secondary)
                            .padding(.horizontal, Spacing.md)
                            .padding(.vertical, Spacing.xs)
                            .background(
                                Capsule()
                                    .fill(selectedFilter == filter ? Color.petAccent : Color.gray.opacity(0.2))
                            )
                    }
                }
            }
            .padding(.horizontal, Spacing.lg)
        }
        .padding(.vertical, Spacing.sm)
    }
}

struct EmptyTimelineView: View {
    let selectedPet: Pet?
    @Binding var showingMemoryPrompts: Bool
    
    var body: some View {
        VStack(spacing: Spacing.xl) {
            Spacer()
            
            VStack(spacing: Spacing.lg) {
                Text("📸")
                    .font(.system(size: 80))
                
                VStack(spacing: Spacing.sm) {
                    if let pet = selectedPet {
                        Text("Start \(pet.name)'s Timeline")
                            .font(.petTitle2)
                            .foregroundColor(.primary)
                        
                        Text("Capture precious moments and create lasting memories together.")
                            .font(.petBody)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                    } else {
                        Text("Select a Pet")
                            .font(.petTitle2)
                            .foregroundColor(.primary)
                        
                        Text("Choose a pet to start building their timeline of memories.")
                            .font(.petBody)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                    }
                }
            }
            
            if selectedPet != nil {
                VStack(spacing: Spacing.md) {
                    Button("Get Memory Ideas") {
                        showingMemoryPrompts = true
                    }
                    .petButtonStyle(.primary)
                    
                    Button("Add First Memory") {
                        // Navigate to memory creation
                    }
                    .petButtonStyle(.secondary)
                }
            }
            
            Spacer()
        }
        .padding(Spacing.xl)
    }
}

struct TimelineScrollView: View {
    let memories: [Memory]
    let selectedPet: Pet?
    
    var body: some View {
        ScrollView {
            LazyVStack(spacing: Spacing.lg) {
                ForEach(memories, id: \.id) { memory in
                    TimelineMemoryCard(memory: memory)
                }
            }
            .padding(.horizontal, Spacing.lg)
            .padding(.top, Spacing.md)
        }
    }
}

struct TimelineMemoryCard: View {
    let memory: Memory
    @State private var showingDetails = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: Spacing.md) {
            // Memory header
            HStack {
                VStack(alignment: .leading, spacing: Spacing.xs) {
                    Text(memory.title)
                        .font(.petHeadline)
                        .foregroundColor(.primary)
                    
                    Text(memory.createdAt, style: .date)
                        .font(.petCaption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                // Memory type icon
                Image(systemName: memory.type.systemImage)
                    .font(.title3)
                    .foregroundColor(.petAccent)
            }
            
            // Memory content
            Text(memory.content)
                .font(.petBody)
                .foregroundColor(.primary)
                .lineLimit(3)
            
            // AI-generated tags and insights
            if !memory.tags.isEmpty || memory.milestone != nil {
                VStack(alignment: .leading, spacing: Spacing.sm) {
                    if let milestone = memory.milestone {
                        HStack {
                            Image(systemName: "star.fill")
                                .font(.caption)
                                .foregroundColor(.yellow)
                            
                            Text(milestone)
                                .font(.petCaption)
                                .foregroundColor(.primary)
                                .padding(.horizontal, Spacing.sm)
                                .padding(.vertical, Spacing.xs)
                                .background(Color.yellow.opacity(0.2))
                                .cornerRadius(CornerRadius.sm)
                        }
                    }
                    
                    if !memory.tags.isEmpty {
                        ScrollView(.horizontal, showsIndicators: false) {
                            HStack(spacing: Spacing.xs) {
                                ForEach(memory.tags.prefix(3), id: \.self) { tag in
                                    Text("#\(tag)")
                                        .font(.petCaption)
                                        .foregroundColor(.blue)
                                        .padding(.horizontal, Spacing.sm)
                                        .padding(.vertical, Spacing.xs)
                                        .background(Color.blue.opacity(0.1))
                                        .cornerRadius(CornerRadius.sm)
                                }
                                
                                if memory.tags.count > 3 {
                                    Text("+\(memory.tags.count - 3)")
                                        .font(.petCaption)
                                        .foregroundColor(.secondary)
                                }
                            }
                        }
                    }
                }
            }
            
            // Action buttons
            HStack {
                Button("View Details") {
                    showingDetails = true
                }
                .font(.petCaption)
                .foregroundColor(.blue)
                
                Spacer()
                
                Button(action: {}) {
                    Image(systemName: "heart")
                        .foregroundColor(.pink)
                }
                
                Button(action: {}) {
                    Image(systemName: "square.and.arrow.up")
                        .foregroundColor(.blue)
                }
            }
        }
        .padding(Spacing.md)
        .background(
            RoundedRectangle(cornerRadius: CornerRadius.lg)
                .fill(Color.white)
                .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
        )
        .sheet(isPresented: $showingDetails) {
            MemoryDetailView(memory: memory)
                .environmentObject(RealDataService())
        }
    }
}

// Placeholder views
struct PetSelectorSheet: View {
    @Binding var selectedPet: Pet?
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack {
                Text("Select Pet")
                    .font(.petTitle2)
                Spacer()
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") { dismiss() }
                }
            }
        }
    }
}

struct MemoryPromptsView: View {
    let pet: Pet?
    let prompts: [MemoryPrompt]
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack {
                Text("Memory Ideas")
                    .font(.petTitle2)
                Spacer()
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") { dismiss() }
                }
            }
        }
    }
}



// MemoryDetailView is now in PetCapsule/Views/Memory/MemoryDetailView.swift

#Preview {
    TimelineView()
        .environmentObject(SupabaseService.shared)
}
