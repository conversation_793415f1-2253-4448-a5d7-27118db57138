//
//  MoreView.swift
//  PetCapsule
//
//  More page with Settings, Premium, and Pet Health
//

import SwiftUI

struct MoreView: View {
    @EnvironmentObject private var authService: AuthenticationService
    @EnvironmentObject private var subscriptionService: SubscriptionService
    @EnvironmentObject private var themeManager: ThemeManager
    @EnvironmentObject private var realDataService: RealDataService

    // Sheet states
    @State private var showSettings = false
    @State private var showPremium = false
    @State private var showPetHealth = false
    @State private var showProfile = false
    @State private var showHelp = false
    @State private var showRating = false
    @State private var showSignOutAlert = false
    @State private var showSharing = false
    @State private var showPrivacy = false
    @State private var showTerms = false
    @State private var showKnowledgeBase = false
    @State private var showVetSearch = false
    @State private var showEmergencyContacts = false
    @State private var showPetInsurance = false
    @State private var showPetTraining = false
    @State private var showNutritionGuide = false
    @State private var showSubscriptions = false
    @State private var showBilling = false
    @State private var showPlanner = false
    @State private var showWalkMemories = false
    @State private var showMemoryTest = false
    @State private var showVaccinationPlanner = false

    // Collapsible sections
    @State private var expandedSections: Set<String> = ["Account & Support"]
    @State private var animateItems = false

    var body: some View {
        NavigationView {
            ScrollView {
                LazyVStack(spacing: 20) {
                    // Collapsible sections
                    collapsibleSection("Planner", icon: "calendar.badge.plus", items: plannerItems)
                    collapsibleSection("Resources", icon: "book.circle", items: resourcesItems)
                    collapsibleSection("Emergency & Health", icon: "cross.circle", items: emergencyHealthItems)
                    collapsibleSection("Account & Support", icon: "person.circle", items: accountSupportItems)
                    collapsibleSection("About", icon: "info.circle", items: aboutItems)
                }
                .padding(.horizontal)
                .padding(.bottom, 100)
            }
            .navigationTitle("More")
            .navigationBarTitleDisplayMode(.large)
            .background(Color.themeBackground)
            .onAppear {
                withAnimation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.1)) {
                    animateItems = true
                }
            }
        }
        .sheet(isPresented: $showSettings) {
            AppSettingsView()
                .environmentObject(authService)
                .environmentObject(themeManager)
        }
        .sheet(isPresented: $showPremium) {
            PremiumHubView()
                .environmentObject(subscriptionService)
        }
        .sheet(isPresented: $showPetHealth) {
            PetHealthView()
                .environmentObject(authService)
        }
        .sheet(isPresented: $showProfile) {
            UserProfileView()
                .environmentObject(authService)
        }
        .sheet(isPresented: $showHelp) {
            HelpSupportView()
        }
        .sheet(isPresented: $showRating) {
            AppRatingView()
        }
        .sheet(isPresented: $showSharing) {
            AppSharingView()
        }
        .sheet(isPresented: $showPrivacy) {
            PrivacyPolicyView()
        }
        .sheet(isPresented: $showTerms) {
            TermsOfServiceView()
        }
        .sheet(isPresented: $showKnowledgeBase) {
            KnowledgeBaseView()
        }
        .sheet(isPresented: $showVetSearch) {
            VetSearchView()
        }
        .sheet(isPresented: $showEmergencyContacts) {
            EmergencyContactsView()
        }
        .sheet(isPresented: $showPetInsurance) {
            PetInsuranceView()
        }
        .sheet(isPresented: $showPetTraining) {
            PetTrainingView()
        }
        .sheet(isPresented: $showNutritionGuide) {
            NutritionGuideView()
        }
        .sheet(isPresented: $showSubscriptions) {
            SubscriptionView()
                .environmentObject(subscriptionService)
        }
        .sheet(isPresented: $showPlanner) {
            PetPlannerView()
        }
        .sheet(isPresented: $showWalkMemories) {
            WalkMemoriesView()
                .environmentObject(PetPlannerService.shared)
        }
        .sheet(isPresented: $showMemoryTest) {
            StorageTestView()
        }
        .sheet(isPresented: $showVaccinationPlanner) {
            VaccinationPlannerView()
        }
        .alert("Sign Out", isPresented: $showSignOutAlert) {
            Button("Cancel", role: .cancel) { }
            Button("Sign Out", role: .destructive) {
                Task {
                    await authService.signOut()
                }
            }
        } message: {
            Text("Are you sure you want to sign out? Your data will remain safe in the cloud.")
        }
    }

    // MARK: - Section Data

    private var plannerItems: [MoreMenuItem] {
        [
            MoreMenuItem(icon: "figure.walk", title: "Smart Walk Planner", subtitle: "Weather-based walk recommendations with environmental alerts", color: .blue, action: { showPlanner = true }),
            MoreMenuItem(icon: "leaf.fill", title: "Nutrition Planner", subtitle: "Feeding schedules, diet plans, and nutrition tracking", color: .green, action: { showNutritionGuide = true }),
            MoreMenuItem(icon: "graduationcap.fill", title: "Training Planner", subtitle: "Professional training schedules and progress tracking", color: .orange, action: { showPetTraining = true }),
            MoreMenuItem(icon: "syringe.fill", title: "Vaccination Planner", subtitle: "Track vaccination schedules and health records", color: .red, action: { showVaccinationPlanner = true }),
            MoreMenuItem(icon: "location.fill", title: "Pet-Friendly Places", subtitle: "Discover parks, cafes, and pet-friendly locations", color: .purple, action: { showPlanner = true })
        ]
    }



    private var resourcesItems: [MoreMenuItem] {
        [
            MoreMenuItem(icon: "doc.fill", title: "Documents", subtitle: "Store recipes, medical data, and important documents", color: .blue, action: { showKnowledgeBase = true }),
            MoreMenuItem(icon: "stethoscope", title: "Find Veterinarians", subtitle: "Search and save vet contacts for emergencies", color: .green, action: { showVetSearch = true }),
            MoreMenuItem(icon: "shield.fill", title: "Pet Insurance", subtitle: "Compare plans and manage insurance policies", color: .indigo, action: { showPetInsurance = true })
        ]
    }

    private var emergencyHealthItems: [MoreMenuItem] {
        [
            MoreMenuItem(icon: "brain.head.profile.fill", title: "Pet Health", subtitle: "AI-powered health insights and analysis", color: .purple, action: { showPetHealth = true }),
            MoreMenuItem(icon: "phone.fill", title: "Emergency Contacts", subtitle: "Quick access to vets, poison control, and emergency services", color: .red, action: { showEmergencyContacts = true })
        ]
    }

    private var accountSupportItems: [MoreMenuItem] {
        [
            MoreMenuItem(icon: "person.crop.circle", title: "Profile", subtitle: getUserProfileSubtitle(), color: .purple, action: { showProfile = true }),
            MoreMenuItem(icon: "crown.fill", title: "Premium Features", subtitle: "Unlock advanced AI and premium tools", color: .orange, badge: subscriptionService.subscriptionStatus == .free ? "Upgrade" : nil, action: { showPremium = true }),
            MoreMenuItem(icon: "creditcard.fill", title: "Manage Subscriptions", subtitle: "View and manage your subscription", color: .blue, action: { showSubscriptions = true }),
            MoreMenuItem(icon: "dollarsign.circle.fill", title: "Billing", subtitle: "Payment history and billing information", color: .green, action: { showSubscriptions = true }),
            MoreMenuItem(icon: "questionmark.circle.fill", title: "Help & Support", subtitle: "Get help and contact support", color: .blue, action: { showHelp = true }),
            MoreMenuItem(icon: "gearshape.fill", title: "Settings", subtitle: "App preferences and notifications", color: .gray, action: { showSettings = true }),
            MoreMenuItem(icon: "wrench.and.screwdriver.fill", title: "Memory Upload Test", subtitle: "Debug image upload functionality", color: .orange, action: { showMemoryTest = true }),
            MoreMenuItem(icon: "rectangle.portrait.and.arrow.right.fill", title: "Sign Out", subtitle: "Sign out of your account", color: .red, action: { showSignOutAlert = true })
        ]
    }

    private var aboutItems: [MoreMenuItem] {
        [
            MoreMenuItem(icon: "doc.text.fill", title: "Privacy Policy", subtitle: "How we protect your data", color: .indigo, action: { showPrivacy = true }),
            MoreMenuItem(icon: "doc.plaintext.fill", title: "Terms of Service", subtitle: "Terms and conditions", color: .teal, action: { showTerms = true }),
            MoreMenuItem(icon: "star.fill", title: "Rate App", subtitle: "Share your experience on the App Store", color: .yellow, action: { showRating = true })
        ]
    }

    // MARK: - Collapsible Section View

    private func collapsibleSection(_ title: String, icon: String, items: [MoreMenuItem]) -> some View {
        VStack(spacing: 0) {
            // Section Header
            Button(action: {
                withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                    if expandedSections.contains(title) {
                        expandedSections.remove(title)
                    } else {
                        expandedSections.insert(title)
                    }
                }
            }) {
                HStack(spacing: 12) {
                    // Section Icon
                    Image(systemName: icon)
                        .font(.title2)
                        .foregroundColor(.themeAccent)
                        .frame(width: 24, height: 24)

                    // Section Title
                    Text(title)
                        .font(.petTitle3)
                        .fontWeight(.bold)
                        .foregroundColor(.themePrimary)

                    Spacer()

                    // Expand/Collapse Icon
                    Image(systemName: "chevron.right")
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(.themeSecondary)
                        .rotationEffect(.degrees(expandedSections.contains(title) ? 90 : 0))
                        .animation(.spring(response: 0.3, dampingFraction: 0.8), value: expandedSections.contains(title))
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
                .background(Color.themeCardBackground)
                .cornerRadius(12)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.themeBorder.opacity(0.3), lineWidth: 1)
                )
            }
            .buttonStyle(PlainButtonStyle())

            // Section Content
            if expandedSections.contains(title) {
                VStack(spacing: 8) {
                    ForEach(items.indices, id: \.self) { index in
                        moreMenuItem(item: items[index])
                            .transition(.asymmetric(
                                insertion: .opacity.combined(with: .move(edge: .top)),
                                removal: .opacity.combined(with: .move(edge: .top))
                            ))
                    }
                }
                .padding(.top, 8)
            }
        }
        .scaleEffect(animateItems ? 1.0 : 0.9)
        .opacity(animateItems ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.1), value: animateItems)
    }

    // MARK: - Helper Methods

    private func getUserProfileSubtitle() -> String {
        let name = getUserName()
        let status = subscriptionService.subscriptionStatus.displayName
        return "\(name) • \(status)"
    }



    private func getUserName() -> String {
        return authService.currentUser?.displayName ?? "Pet Parent"
    }

    private func getUserInitials() -> String {
        let name = getUserName()
        let components = name.components(separatedBy: " ")
        if components.count >= 2 {
            return String(components[0].prefix(1)) + String(components[1].prefix(1))
        } else {
            return String(name.prefix(2))
        }
    }

    private func moreMenuItem(item: MoreMenuItem) -> some View {
        Button(action: item.action) {
            HStack(spacing: 16) {
                // Icon
                RoundedRectangle(cornerRadius: 12)
                    .fill(item.color.opacity(0.15))
                    .frame(width: 44, height: 44)
                    .overlay(
                        Image(systemName: item.icon)
                            .font(.title3)
                            .foregroundColor(item.color)
                    )

                // Content
                VStack(alignment: .leading, spacing: 2) {
                    HStack {
                        Text(item.title)
                            .font(.petSubheadline)
                            .fontWeight(.semibold)
                            .foregroundColor(.themePrimary)

                        if let badge = item.badge {
                            Text(badge)
                                .font(.petCaption2)
                                .fontWeight(.bold)
                                .foregroundColor(.white)
                                .padding(.horizontal, 8)
                                .padding(.vertical, 2)
                                .background(
                                    Capsule()
                                        .fill(Color.themeAccent)
                                )
                        }

                        Spacer()
                    }

                    Text(item.subtitle)
                        .font(.petCaption)
                        .foregroundColor(.themeSecondary)
                        .multilineTextAlignment(.leading)
                }

                Image(systemName: "chevron.right")
                    .foregroundColor(.themeSecondary)
                    .font(.caption)
            }
            .padding(.vertical, 12)
            .padding(.horizontal, 16)
            .background(Color.themeCardBackground)
            .cornerRadius(12)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(Color.themeBorder.opacity(0.1), lineWidth: 1)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - MoreMenuItem Model

struct MoreMenuItem {
    let icon: String
    let title: String
    let subtitle: String
    let color: Color
    let badge: String?
    let action: () -> Void

    init(icon: String, title: String, subtitle: String, color: Color, badge: String? = nil, action: @escaping () -> Void) {
        self.icon = icon
        self.title = title
        self.subtitle = subtitle
        self.color = color
        self.badge = badge
        self.action = action
    }
}

// MARK: - Subscription Status Extension

extension SubscriptionStatus {
    var displayName: String {
        switch self {
        case .free:
            return "Free Plan"
        case .premium:
            return "Premium Member"
        case .family:
            return "Family Plan"
        case .professional:
            return "Professional"
        case .expired:
            return "Expired Plan"
        }
    }
}

#Preview {
    MoreView()
        .environmentObject(AuthenticationService())
        .environmentObject(SubscriptionService.shared)
        .environmentObject(ThemeManager.shared)
        .environmentObject(RealDataService())
}
