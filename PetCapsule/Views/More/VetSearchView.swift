//
//  VetSearchView.swift
//  PetCapsule
//
//  Search and manage veterinarian contacts
//

import SwiftUI
import MapKit

struct VetSearchView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var vetService = VetSearchService.shared
    @State private var searchText = ""
    @State private var selectedVet: VeterinarianInfo?
    @State private var showMap = false
    @State private var animateItems = false
    
    var filteredVets: [VeterinarianInfo] {
        if searchText.isEmpty {
            return vetService.veterinarians
        } else {
            return vetService.veterinarians.filter { vet in
                vet.name.localizedCaseInsensitiveContains(searchText) ||
                vet.clinicName.localizedCaseInsensitiveContains(searchText) ||
                vet.specialties.contains { $0.localizedCaseInsensitiveContains(searchText) }
            }
        }
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Search and Filter Section
                searchSection
                
                // Toggle View Mode
                viewModeToggle
                
                // Content
                if showMap {
                    mapView
                } else {
                    if filteredVets.isEmpty {
                        emptyStateView
                    } else {
                        vetsListView
                    }
                }
            }
            .navigationTitle("Find Veterinarians")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Close") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Add Vet") {
                        // TODO: Add manual vet entry
                    }
                    .font(.subheadline)
                    .foregroundColor(.blue)
                }
            }
            .sheet(item: $selectedVet) { vet in
                VetDetailView(vet: vet)
                    .environmentObject(vetService)
            }
            .onAppear {
                withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                    animateItems = true
                }
                Task {
                    await vetService.searchNearbyVets()
                }
            }
        }
    }
    
    // MARK: - Search Section
    
    private var searchSection: some View {
        VStack(spacing: 12) {
            HStack {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(.secondary)
                
                TextField("Search vets, clinics, or specialties", text: $searchText)
                    .textFieldStyle(PlainTextFieldStyle())
                
                if !searchText.isEmpty {
                    Button(action: { searchText = "" }) {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundColor(.secondary)
                    }
                }
            }
            .padding()
            .background(Color(.systemGray6))
            .cornerRadius(12)
            
            // Quick Filter Buttons
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    filterButton("All", isSelected: searchText.isEmpty) {
                        searchText = ""
                    }
                    filterButton("Emergency", isSelected: false) {
                        searchText = "emergency"
                    }
                    filterButton("Specialist", isSelected: false) {
                        searchText = "specialist"
                    }
                    filterButton("24/7", isSelected: false) {
                        searchText = "24 hour"
                    }
                }
                .padding(.horizontal)
            }
        }
        .padding(.horizontal)
        .padding(.top)
    }
    
    private func filterButton(_ title: String, isSelected: Bool, action: @escaping () -> Void) -> some View {
        Button(action: action) {
            Text(title)
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(isSelected ? .white : .blue)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .fill(isSelected ? Color.blue : Color.blue.opacity(0.1))
                )
        }
    }
    
    // MARK: - View Mode Toggle
    
    private var viewModeToggle: some View {
        HStack {
            Button(action: { showMap = false }) {
                HStack {
                    Image(systemName: "list.bullet")
                    Text("List")
                }
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(showMap ? .secondary : .blue)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .fill(showMap ? Color.clear : Color.blue.opacity(0.1))
                )
            }
            
            Button(action: { showMap = true }) {
                HStack {
                    Image(systemName: "map")
                    Text("Map")
                }
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(showMap ? .blue : .secondary)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .fill(showMap ? Color.blue.opacity(0.1) : Color.clear)
                )
            }
            
            Spacer()
        }
        .padding(.horizontal)
        .padding(.vertical, 8)
    }
    
    // MARK: - Empty State
    
    private var emptyStateView: some View {
        VStack(spacing: 24) {
            Image(systemName: "stethoscope")
                .font(.system(size: 64))
                .foregroundColor(.green)
            
            VStack(spacing: 12) {
                Text("No Veterinarians Found")
                    .font(.title2)
                    .fontWeight(.bold)
                
                Text("Try adjusting your search or check your location settings")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 40)
            }
            
            Button("Search Again") {
                Task {
                    await vetService.searchNearbyVets()
                }
            }
            .font(.headline)
            .foregroundColor(.white)
            .padding()
            .background(Color.green)
            .cornerRadius(12)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    // MARK: - Vets List
    
    private var vetsListView: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                ForEach(Array(filteredVets.enumerated()), id: \.element.id) { index, vet in
                    vetCard(vet: vet)
                        .scaleEffect(animateItems ? 1.0 : 0.9)
                        .opacity(animateItems ? 1.0 : 0.0)
                        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(Double(index) * 0.1), value: animateItems)
                        .onTapGesture {
                            selectedVet = vet
                        }
                }
            }
            .padding()
        }
    }
    
    private func vetCard(vet: VeterinarianInfo) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                // Vet Avatar
                Circle()
                    .fill(Color.green.opacity(0.15))
                    .frame(width: 50, height: 50)
                    .overlay(
                        Image(systemName: "stethoscope")
                            .font(.title3)
                            .foregroundColor(.green)
                    )
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(vet.name)
                        .font(.headline)
                        .fontWeight(.bold)
                    
                    Text(vet.clinicName)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 4) {
                    HStack {
                        Image(systemName: "star.fill")
                            .font(.caption)
                            .foregroundColor(.yellow)
                        Text(String(format: "%.1f", vet.rating))
                            .font(.caption)
                            .fontWeight(.medium)
                    }
                    
                    Text("\(String(format: "%.1f", vet.distance)) mi")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            // Specialties
            if !vet.specialties.isEmpty {
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 8) {
                        ForEach(vet.specialties.prefix(3), id: \.self) { specialty in
                            Text(specialty)
                                .font(.caption)
                                .padding(.horizontal, 8)
                                .padding(.vertical, 4)
                                .background(Color.green.opacity(0.1))
                                .foregroundColor(.green)
                                .cornerRadius(8)
                        }
                    }
                    .padding(.horizontal, 1)
                }
            }
            
            // Contact Info
            HStack(spacing: 16) {
                if vet.isOpen {
                    HStack {
                        Circle()
                            .fill(Color.green)
                            .frame(width: 8, height: 8)
                        Text("Open")
                            .font(.caption)
                            .foregroundColor(.green)
                    }
                } else {
                    HStack {
                        Circle()
                            .fill(Color.red)
                            .frame(width: 8, height: 8)
                        Text("Closed")
                            .font(.caption)
                            .foregroundColor(.red)
                    }
                }
                
                Spacer()
                
                Button(action: { callVet(vet) }) {
                    Image(systemName: "phone.fill")
                        .font(.caption)
                        .foregroundColor(.blue)
                }
                
                Button(action: { openDirections(vet) }) {
                    Image(systemName: "location.fill")
                        .font(.caption)
                        .foregroundColor(.blue)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 2)
        )
    }
    
    // MARK: - Map View
    
    private var mapView: some View {
        Map {
            ForEach(filteredVets, id: \.id) { vet in
                Annotation(vet.clinicName, coordinate: vet.coordinate) {
                    VStack {
                        Image(systemName: "stethoscope")
                            .font(.title3)
                            .foregroundColor(.white)
                            .padding(8)
                            .background(Color.green)
                            .clipShape(Circle())

                        Text(vet.clinicName)
                            .font(.caption)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(Color.white)
                            .cornerRadius(8)
                            .shadow(radius: 2)
                    }
                    .onTapGesture {
                        selectedVet = vet
                    }
                }
            }
        }
        .mapStyle(.standard)
    }
    
    // MARK: - Actions
    
    private func callVet(_ vet: VeterinarianInfo) {
        if let url = URL(string: "tel:\(vet.phoneNumber)") {
            UIApplication.shared.open(url)
        }
    }
    
    private func openDirections(_ vet: VeterinarianInfo) {
        let mapItem = MKMapItem(placemark: MKPlacemark(coordinate: vet.coordinate))
        mapItem.name = vet.clinicName
        mapItem.openInMaps(launchOptions: [MKLaunchOptionsDirectionsModeKey: MKLaunchOptionsDirectionsModeDriving])
    }
}

#Preview {
    VetSearchView()
}
