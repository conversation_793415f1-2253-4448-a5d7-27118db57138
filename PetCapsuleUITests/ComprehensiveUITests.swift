//
//  ComprehensiveUITests.swift
//  PetCapsuleUITests
//
//  Comprehensive UI Testing Suite for PetCapsule App
//  Tests all major features including AI Agents, Memory Management, and Core Functionality
//

import XCTest

final class ComprehensiveUITests: XCTestCase {
    
    var app: XCUIApplication!
    
    override func setUpWithError() throws {
        continueAfterFailure = false
        app = XCUIApplication()
        
        // Set launch arguments for testing
        app.launchArguments = ["UI_TESTING"]
        app.launch()
        
        // Wait for app to fully load
        _ = app.wait(for: .runningForeground, timeout: 10)
    }

    override func tearDownWithError() throws {
        app.terminate()
        app = nil
    }
    
    // MARK: - App Launch and Basic Navigation Tests
    
    @MainActor
    func testAppLaunchAndBasicNavigation() throws {
        // Test app launches successfully
        XCTAssertTrue(app.state == .runningForeground, "App should be running in foreground")
        
        // Take screenshot of launch state
        let launchScreenshot = app.screenshot()
        let attachment = XCTAttachment(screenshot: launchScreenshot)
        attachment.name = "App Launch State"
        attachment.lifetime = .keepAlways
        add(attachment)
        
        // Test main navigation elements exist
        let tabBar = app.tabBars.firstMatch
        XCTAssertTrue(tabBar.waitForExistence(timeout: 5), "Tab bar should exist")
        
        // Test all main tabs are accessible
        let expectedTabs = ["Dashboard", "Memory", "Pet Support", "More"]
        for tabName in expectedTabs {
            let tab = tabBar.buttons[tabName]
            if tab.exists {
                print("✅ \(tabName) tab found")
            } else {
                print("❌ \(tabName) tab not found")
            }
        }
    }
    
    @MainActor
    func testTabNavigation() throws {
        let tabBar = app.tabBars.firstMatch
        XCTAssertTrue(tabBar.waitForExistence(timeout: 5), "Tab bar should exist")
        
        // Test each tab navigation
        let tabs = ["Dashboard", "Memory", "Pet Support", "More"]
        for tabName in tabs {
            let tabButton = tabBar.buttons[tabName]
            if tabButton.exists {
                tabButton.tap()
                sleep(2) // Allow time for navigation
                
                // Take screenshot of each tab
                let tabScreenshot = app.screenshot()
                let attachment = XCTAttachment(screenshot: tabScreenshot)
                attachment.name = "\(tabName) Tab"
                attachment.lifetime = .keepAlways
                add(attachment)
                
                print("✅ Successfully navigated to \(tabName)")
            } else {
                print("❌ \(tabName) tab not accessible")
            }
        }
    }
    
    // MARK: - AI Agents Testing
    
    @MainActor
    func testAIAgentsAccess() throws {
        // Navigate to Pet Support
        let tabBar = app.tabBars.firstMatch
        let petSupportTab = tabBar.buttons["Pet Support"]
        
        if petSupportTab.exists {
            petSupportTab.tap()
            sleep(2)
            
            // Look for AI Agents related elements
            let aiElements = app.buttons.matching(NSPredicate(format: "label CONTAINS[c] 'AI' OR label CONTAINS[c] 'agent'"))
            
            if aiElements.count > 0 {
                print("✅ Found \(aiElements.count) AI-related elements")
                
                // Take screenshot of Pet Support with AI elements
                let aiScreenshot = app.screenshot()
                let attachment = XCTAttachment(screenshot: aiScreenshot)
                attachment.name = "Pet Support - AI Elements"
                attachment.lifetime = .keepAlways
                add(attachment)
                
                // Try to tap the first AI element
                let firstAIElement = aiElements.firstMatch
                if firstAIElement.exists {
                    firstAIElement.tap()
                    sleep(2)
                    
                    // Take screenshot after tapping AI element
                    let aiDetailScreenshot = app.screenshot()
                    let aiDetailAttachment = XCTAttachment(screenshot: aiDetailScreenshot)
                    aiDetailAttachment.name = "AI Interface"
                    aiDetailAttachment.lifetime = .keepAlways
                    add(aiDetailAttachment)
                }
            } else {
                print("❌ No AI-related elements found in Pet Support")
            }
        }
    }
    
    @MainActor
    func testPetMasterAgent() throws {
        // Navigate to Pet Support
        app.tabBars.firstMatch.buttons["Pet Support"].tap()
        sleep(2)
        
        // Look for Pet Master specifically
        let petMasterButton = app.buttons.matching(NSPredicate(format: "label CONTAINS[c] 'Pet Master' OR label CONTAINS[c] '🎯'")).firstMatch
        
        if petMasterButton.waitForExistence(timeout: 5) {
            petMasterButton.tap()
            sleep(2)
            
            print("✅ Pet Master button found and tapped")
            
            // Take screenshot of Pet Master interface
            let petMasterScreenshot = app.screenshot()
            let attachment = XCTAttachment(screenshot: petMasterScreenshot)
            attachment.name = "Pet Master Interface"
            attachment.lifetime = .keepAlways
            add(attachment)
            
            // Look for chat interface elements
            let textFields = app.textFields
            let textViews = app.textViews
            
            if textFields.count > 0 || textViews.count > 0 {
                print("✅ Chat interface elements found")
            } else {
                print("❌ No chat interface elements found")
            }
        } else {
            print("❌ Pet Master button not found")
        }
    }
    
    // MARK: - Memory Management Testing
    
    @MainActor
    func testMemorySection() throws {
        // Navigate to Memory tab
        app.tabBars.firstMatch.buttons["Memory"].tap()
        sleep(2)
        
        // Take screenshot of Memory section
        let memoryScreenshot = app.screenshot()
        let attachment = XCTAttachment(screenshot: memoryScreenshot)
        attachment.name = "Memory Section"
        attachment.lifetime = .keepAlways
        add(attachment)
        
        // Look for memory-related buttons
        let addButtons = app.buttons.matching(NSPredicate(format: "label CONTAINS[c] 'add' OR label CONTAINS[c] '+'"))
        let memoryButtons = app.buttons.matching(NSPredicate(format: "label CONTAINS[c] 'memory' OR label CONTAINS[c] 'montage'"))
        
        print("✅ Found \(addButtons.count) add buttons")
        print("✅ Found \(memoryButtons.count) memory-related buttons")
        
        // Test memory functionality
        if memoryButtons.count > 0 {
            let firstMemoryButton = memoryButtons.firstMatch
            if firstMemoryButton.exists {
                firstMemoryButton.tap()
                sleep(2)
                
                // Take screenshot after tapping memory button
                let memoryDetailScreenshot = app.screenshot()
                let memoryDetailAttachment = XCTAttachment(screenshot: memoryDetailScreenshot)
                memoryDetailAttachment.name = "Memory Feature Detail"
                memoryDetailAttachment.lifetime = .keepAlways
                add(memoryDetailAttachment)
            }
        }
    }
    
    @MainActor
    func testAIVideoMontage() throws {
        // Navigate to Memory section
        app.tabBars.firstMatch.buttons["Memory"].tap()
        sleep(2)
        
        // Look for AI Video Montage feature
        let montageButtons = app.buttons.matching(NSPredicate(format: "label CONTAINS[c] 'montage' OR label CONTAINS[c] 'video' OR label CONTAINS[c] 'AI'"))
        
        if montageButtons.count > 0 {
            print("✅ Found \(montageButtons.count) montage-related buttons")
            
            let firstMontageButton = montageButtons.firstMatch
            if firstMontageButton.exists {
                firstMontageButton.tap()
                sleep(3)
                
                // Take screenshot of AI Video Montage
                let montageScreenshot = app.screenshot()
                let attachment = XCTAttachment(screenshot: montageScreenshot)
                attachment.name = "AI Video Montage"
                attachment.lifetime = .keepAlways
                add(attachment)
                
                print("✅ AI Video Montage interface accessed")
            }
        } else {
            print("❌ No montage-related buttons found")
        }
    }
    
    // MARK: - Knowledge Base Testing
    
    @MainActor
    func testKnowledgeBase() throws {
        // Navigate to More section
        app.tabBars.firstMatch.buttons["More"].tap()
        sleep(2)
        
        // Look for Knowledge Base
        let knowledgeBaseButton = app.buttons.matching(NSPredicate(format: "label CONTAINS[c] 'knowledge' OR label CONTAINS[c] 'base'")).firstMatch
        
        if knowledgeBaseButton.waitForExistence(timeout: 5) {
            knowledgeBaseButton.tap()
            sleep(2)
            
            print("✅ Knowledge Base accessed")
            
            // Take screenshot of Knowledge Base
            let kbScreenshot = app.screenshot()
            let attachment = XCTAttachment(screenshot: kbScreenshot)
            attachment.name = "Knowledge Base"
            attachment.lifetime = .keepAlways
            add(attachment)
        } else {
            print("❌ Knowledge Base not found in More section")
        }
    }
    
    // MARK: - Performance Testing
    
    @MainActor
    func testLaunchPerformance() throws {
        if #available(macOS 10.15, iOS 13.0, tvOS 13.0, watchOS 7.0, *) {
            measure(metrics: [XCTApplicationLaunchMetric()]) {
                XCUIApplication().launch()
            }
        }
    }
    
    @MainActor
    func testNavigationPerformance() throws {
        let tabBar = app.tabBars.firstMatch
        
        measure {
            // Test rapid tab switching performance
            tabBar.buttons["Dashboard"].tap()
            tabBar.buttons["Memory"].tap()
            tabBar.buttons["Pet Support"].tap()
            tabBar.buttons["More"].tap()
        }
    }
}
