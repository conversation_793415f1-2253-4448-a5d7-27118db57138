//
//  AIAgentsUITests.swift
//  PetCapsuleUITests
//
//  Specialized UI Tests for AI Agents System
//  Tests Pet Master, Knowledge Base, and Agent Interactions
//

import XCTest

final class AIAgentsUITests: XCTestCase {
    
    var app: XCUIApplication!
    
    override func setUpWithError() throws {
        continueAfterFailure = false
        app = XCUIApplication()
        app.launchArguments = ["UI_TESTING", "AI_AGENTS_TESTING"]
        app.launch()
        _ = app.wait(for: .runningForeground, timeout: 10)
    }

    override func tearDownWithError() throws {
        app.terminate()
        app = nil
    }
    
    // MARK: - AI Agents Hub Tests
    
    @MainActor
    func testAIAgentsHubAccess() throws {
        // Navigate to Pet Support
        app.tabBars.firstMatch.buttons["Pet Support"].tap()
        sleep(2)
        
        // Look for AI Agents Hub access
        let aiAgentsElements = [
            app.buttons["AI Agents"],
            app.buttons.matching(NSPredicate(format: "label CONTAINS[c] 'AI Agents'")).firstMatch,
            app.buttons.matching(NSPredicate(format: "label CONTAINS[c] 'Agents Hub'")).firstMatch
        ]
        
        var foundAIAgents = false
        for element in aiAgentsElements {
            if element.exists {
                element.tap()
                sleep(2)
                foundAIAgents = true
                break
            }
        }
        
        if foundAIAgents {
            print("✅ AI Agents Hub accessed successfully")
            
            // Take screenshot of AI Agents Hub
            let hubScreenshot = app.screenshot()
            let attachment = XCTAttachment(screenshot: hubScreenshot)
            attachment.name = "AI Agents Hub"
            attachment.lifetime = .keepAlways
            add(attachment)
            
            // Test agent cards/buttons exist
            let agentButtons = app.buttons.matching(NSPredicate(format: "label CONTAINS[c] 'Dr.' OR label CONTAINS[c] 'Agent' OR label CONTAINS[c] 'Master'"))
            print("✅ Found \(agentButtons.count) agent elements")
            
        } else {
            print("❌ AI Agents Hub not accessible")
        }
    }
    
    @MainActor
    func testPetMasterAgentFunctionality() throws {
        // Navigate to Pet Support
        app.tabBars.firstMatch.buttons["Pet Support"].tap()
        sleep(2)
        
        // Look for Pet Master button
        let petMasterSelectors = [
            app.buttons["🎯 Pet Master"],
            app.buttons.matching(NSPredicate(format: "label CONTAINS[c] 'Pet Master'")).firstMatch,
            app.buttons.matching(NSPredicate(format: "label CONTAINS[c] '🎯'")).firstMatch
        ]
        
        var petMasterFound = false
        for selector in petMasterSelectors {
            if selector.waitForExistence(timeout: 3) {
                selector.tap()
                sleep(2)
                petMasterFound = true
                break
            }
        }
        
        if petMasterFound {
            print("✅ Pet Master agent accessed")
            
            // Take screenshot of Pet Master interface
            let petMasterScreenshot = app.screenshot()
            let attachment = XCTAttachment(screenshot: petMasterScreenshot)
            attachment.name = "Pet Master Chat Interface"
            attachment.lifetime = .keepAlways
            add(attachment)
            
            // Test chat interface elements
            testChatInterface()
            
        } else {
            print("❌ Pet Master agent not found")
        }
    }
    
    @MainActor
    func testSpecializedAgents() throws {
        // Navigate to Pet Support
        app.tabBars.firstMatch.buttons["Pet Support"].tap()
        sleep(2)
        
        // Look for specialized agents
        let agentNames = ["Dr. Nutrition", "Health Guardian", "Trainer Pro", "Shopping Assistant"]
        
        for agentName in agentNames {
            let agentButton = app.buttons.matching(NSPredicate(format: "label CONTAINS[c] '\(agentName)'")).firstMatch
            
            if agentButton.exists {
                print("✅ Found \(agentName) agent")
                
                agentButton.tap()
                sleep(2)
                
                // Take screenshot of agent interface
                let agentScreenshot = app.screenshot()
                let attachment = XCTAttachment(screenshot: agentScreenshot)
                attachment.name = "\(agentName) Interface"
                attachment.lifetime = .keepAlways
                add(attachment)
                
                // Test basic chat functionality
                testBasicChatFunctionality(agentName: agentName)
                
                // Go back to agents list
                let backButton = app.navigationBars.buttons.matching(NSPredicate(format: "label CONTAINS[c] 'back' OR label CONTAINS[c] '<'")).firstMatch
                if backButton.exists {
                    backButton.tap()
                    sleep(1)
                }
            } else {
                print("❌ \(agentName) agent not found")
            }
        }
    }
    
    @MainActor
    func testKnowledgeBaseIntegration() throws {
        // Navigate to More section
        app.tabBars.firstMatch.buttons["More"].tap()
        sleep(2)
        
        // Look for Knowledge Base
        let knowledgeBaseButton = app.buttons.matching(NSPredicate(format: "label CONTAINS[c] 'Knowledge Base' OR label CONTAINS[c] 'Knowledge'")).firstMatch
        
        if knowledgeBaseButton.waitForExistence(timeout: 5) {
            knowledgeBaseButton.tap()
            sleep(2)
            
            print("✅ Knowledge Base accessed")
            
            // Take screenshot of Knowledge Base
            let kbScreenshot = app.screenshot()
            let attachment = XCTAttachment(screenshot: kbScreenshot)
            attachment.name = "Knowledge Base Interface"
            attachment.lifetime = .keepAlways
            add(attachment)
            
            // Test knowledge base functionality
            testKnowledgeBaseFunctionality()
            
        } else {
            print("❌ Knowledge Base not accessible")
        }
    }
    
    @MainActor
    func testConversationHistory() throws {
        // Navigate to Pet Support and access an agent
        app.tabBars.firstMatch.buttons["Pet Support"].tap()
        sleep(2)
        
        // Find any available agent
        let agentButtons = app.buttons.matching(NSPredicate(format: "label CONTAINS[c] 'Dr.' OR label CONTAINS[c] 'Agent' OR label CONTAINS[c] 'Master'"))
        
        if agentButtons.count > 0 {
            let firstAgent = agentButtons.firstMatch
            firstAgent.tap()
            sleep(2)
            
            // Look for conversation history elements
            let historyElements = app.scrollViews.matching(NSPredicate(format: "identifier CONTAINS[c] 'conversation' OR identifier CONTAINS[c] 'chat'"))
            let messageElements = app.staticTexts.matching(NSPredicate(format: "identifier CONTAINS[c] 'message'"))
            
            print("✅ Found \(historyElements.count) conversation containers")
            print("✅ Found \(messageElements.count) message elements")
            
            // Take screenshot of conversation interface
            let conversationScreenshot = app.screenshot()
            let attachment = XCTAttachment(screenshot: conversationScreenshot)
            attachment.name = "Conversation History Interface"
            attachment.lifetime = .keepAlways
            add(attachment)
        }
    }
    
    // MARK: - Helper Methods
    
    private func testChatInterface() {
        // Look for text input elements
        let textFields = app.textFields
        let textViews = app.textViews
        let sendButtons = app.buttons.matching(NSPredicate(format: "label CONTAINS[c] 'send' OR label CONTAINS[c] 'Send'"))
        
        print("✅ Found \(textFields.count) text fields")
        print("✅ Found \(textViews.count) text views")
        print("✅ Found \(sendButtons.count) send buttons")
        
        // Test text input if available
        if textFields.count > 0 {
            let textField = textFields.firstMatch
            textField.tap()
            textField.typeText("Test message for UI testing")
            
            if sendButtons.count > 0 {
                sendButtons.firstMatch.tap()
                sleep(2)
                print("✅ Test message sent successfully")
            }
        }
    }
    
    private func testBasicChatFunctionality(agentName: String) {
        // Look for agent-specific elements
        let agentTitle = app.navigationBars.staticTexts[agentName]
        if agentTitle.exists {
            print("✅ \(agentName) interface loaded correctly")
        }
        
        // Test conversation starters if available
        let starterButtons = app.buttons.matching(NSPredicate(format: "label CONTAINS[c] 'help' OR label CONTAINS[c] 'advice' OR label CONTAINS[c] 'question'"))
        
        if starterButtons.count > 0 {
            print("✅ Found \(starterButtons.count) conversation starters for \(agentName)")
            
            // Tap first conversation starter
            starterButtons.firstMatch.tap()
            sleep(2)
        }
    }
    
    private func testKnowledgeBaseFunctionality() {
        // Look for folder creation or management elements
        let addButtons = app.buttons.matching(NSPredicate(format: "label CONTAINS[c] 'add' OR label CONTAINS[c] '+' OR label CONTAINS[c] 'create'"))
        let folderElements = app.buttons.matching(NSPredicate(format: "label CONTAINS[c] 'folder'"))
        
        print("✅ Found \(addButtons.count) add/create buttons")
        print("✅ Found \(folderElements.count) folder elements")
        
        // Test search functionality if available
        let searchFields = app.searchFields
        if searchFields.count > 0 {
            let searchField = searchFields.firstMatch
            searchField.tap()
            searchField.typeText("test search")
            print("✅ Knowledge base search tested")
        }
    }
    
    @MainActor
    func testAgentAvailabilityStatus() throws {
        // Navigate to Pet Support
        app.tabBars.firstMatch.buttons["Pet Support"].tap()
        sleep(2)
        
        // Check for agent availability indicators
        let availabilityIndicators = app.images.matching(NSPredicate(format: "identifier CONTAINS[c] 'status' OR identifier CONTAINS[c] 'available'"))
        let onlineIndicators = app.staticTexts.matching(NSPredicate(format: "label CONTAINS[c] 'online' OR label CONTAINS[c] 'available'"))
        
        print("✅ Found \(availabilityIndicators.count) availability indicators")
        print("✅ Found \(onlineIndicators.count) online status indicators")
        
        // Take screenshot of agent status
        let statusScreenshot = app.screenshot()
        let attachment = XCTAttachment(screenshot: statusScreenshot)
        attachment.name = "Agent Availability Status"
        attachment.lifetime = .keepAlways
        add(attachment)
    }
    
    @MainActor
    func testVoiceCapabilities() throws {
        // Navigate to an agent interface
        app.tabBars.firstMatch.buttons["Pet Support"].tap()
        sleep(2)
        
        // Look for voice-related buttons
        let voiceButtons = app.buttons.matching(NSPredicate(format: "label CONTAINS[c] 'voice' OR label CONTAINS[c] 'mic' OR label CONTAINS[c] '🎤'"))
        
        if voiceButtons.count > 0 {
            print("✅ Found \(voiceButtons.count) voice-related buttons")
            
            // Take screenshot showing voice capabilities
            let voiceScreenshot = app.screenshot()
            let attachment = XCTAttachment(screenshot: voiceScreenshot)
            attachment.name = "Voice Capabilities"
            attachment.lifetime = .keepAlways
            add(attachment)
        } else {
            print("❌ No voice capabilities found")
        }
    }
}
