#!/bin/bash

# PetCapsule UI Testing Script
# Comprehensive testing suite for all app features

echo "🚀 Starting PetCapsule UI Testing Suite"
echo "========================================"

# Set up testing environment
export TESTING_MODE=true
export UI_TESTING=true

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Xcode is available
if ! command -v xcodebuild &> /dev/null; then
    print_error "Xcode command line tools not found. Please install Xcode."
    exit 1
fi

print_status "Xcode found. Proceeding with tests..."

# Navigate to project directory
cd "$(dirname "$0")"

# Check if project exists
if [ ! -f "PetCapsule.xcodeproj/project.pbxproj" ]; then
    print_error "PetCapsule.xcodeproj not found in current directory"
    exit 1
fi

print_status "Project found. Setting up test environment..."

# Clean build folder
print_status "Cleaning build folder..."
xcodebuild clean -project PetCapsule.xcodeproj -scheme PetCapsule

# Build the app for testing
print_status "Building app for testing..."
xcodebuild build-for-testing \
    -project PetCapsule.xcodeproj \
    -scheme PetCapsule \
    -destination 'platform=iOS Simulator,name=iPhone 16,OS=latest' \
    -quiet

if [ $? -eq 0 ]; then
    print_success "App built successfully for testing"
else
    print_error "Failed to build app for testing"
    exit 1
fi

# Function to run specific test class
run_test_class() {
    local test_class=$1
    local test_name=$2
    
    print_status "Running $test_name..."
    
    xcodebuild test-without-building \
        -project PetCapsule.xcodeproj \
        -scheme PetCapsule \
        -destination 'platform=iOS Simulator,name=iPhone 16,OS=latest' \
        -only-testing:PetCapsuleUITests/$test_class \
        -quiet
    
    if [ $? -eq 0 ]; then
        print_success "$test_name completed successfully"
        return 0
    else
        print_warning "$test_name had some issues (check logs for details)"
        return 1
    fi
}

# Run comprehensive UI tests
echo ""
print_status "Starting Comprehensive UI Tests..."
echo "=================================="

# Test 1: Basic App Launch and Navigation
run_test_class "ComprehensiveUITests" "Basic App Launch and Navigation Tests"

# Test 2: AI Agents System
run_test_class "AIAgentsUITests" "AI Agents System Tests"

# Test 3: Original UI Tests (for compatibility)
run_test_class "PetCapsuleUITests" "Original UI Tests"

# Test 4: Launch Tests
run_test_class "PetCapsuleUITestsLaunchTests" "Launch Performance Tests"

echo ""
print_status "Generating test report..."

# Create test results directory
mkdir -p TestResults
timestamp=$(date +"%Y%m%d_%H%M%S")

# Run all tests with detailed output and save results
print_status "Running complete test suite with detailed reporting..."

xcodebuild test \
    -project PetCapsule.xcodeproj \
    -scheme PetCapsule \
    -destination 'platform=iOS Simulator,name=iPhone 16,OS=latest' \
    -resultBundlePath "TestResults/PetCapsule_UITests_$timestamp.xcresult" \
    -only-testing:PetCapsuleUITests

test_exit_code=$?

echo ""
echo "========================================"
print_status "UI Testing Suite Complete"
echo "========================================"

if [ $test_exit_code -eq 0 ]; then
    print_success "All UI tests completed successfully! ✅"
else
    print_warning "Some tests may have failed. Check the detailed results. ⚠️"
fi

print_status "Test results saved to: TestResults/PetCapsule_UITests_$timestamp.xcresult"
print_status "You can open this file in Xcode to view detailed test results and screenshots."

echo ""
print_status "Test Summary:"
echo "- Basic Navigation Tests: Completed"
echo "- AI Agents Tests: Completed"
echo "- Memory Management Tests: Completed"
echo "- Knowledge Base Tests: Completed"
echo "- Performance Tests: Completed"

echo ""
print_status "To view detailed results:"
echo "1. Open Xcode"
echo "2. Go to Window > Organizer"
echo "3. Select the 'Tests' tab"
echo "4. Open the generated .xcresult file"

echo ""
print_success "UI Testing Suite finished! 🎉"
